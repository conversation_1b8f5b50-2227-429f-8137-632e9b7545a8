CREATE DATABASE IF NOT EXISTS `portal_taskrunner` DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci;
USE `portal_taskrunner`;

DROP USER IF EXISTS 'portal_taskrunner'@'172.%.%.%';
CREATE USER 'portal_taskrunner'@'172.%.%.%' IDENTIFIED BY 'rjwhTgkrkZkL2e7w';
GRANT SELECT, INSERT, UPDATE ON portal_taskrunner.* TO 'portal_taskrunner'@'172.%.%.%';

CREATE TABLE tasks (id INT AUTO_INCREMENT NOT NULL, bundle VARCHAR(255) NOT NULL, parameters LONGTEXT NOT NULL, statistics LONGTEXT DEFAULT NULL, username VARCHAR(255) NOT NULL, running TINYINT(1) DEFAULT NULL, crdate DATETIME DEFAULT NULL, crawlerStart DATETIME DEFAULT NULL, crawlerEnd DATETIME DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;
ALTER TABLE tasks ADD error LONGTEXT DEFAULT NULL