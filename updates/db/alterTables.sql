USE `portal`;

ALTER TABLE users ADD COlUMN lastactivity datetime DEFAULT NULL AFTER lastvisit;

CREATE TABLE `notifications` (
  `id` int(9) NOT NULL,
  `username` varchar(100) DEFAULT NULL,
  `group` varchar(100) DEFAULT NULL,
  `name_de` varchar(100) DEFAULT NULL,
  `name_en` varchar(100) DEFAULT NULL,
  `name_fr` varchar(100) DEFAULT NULL,
  `name_es` varchar(100) DEFAULT NULL,
  `content_de` mediumtext,
  `content_en` mediumtext,
  `content_fr` mediumtext,
  `content_es` mediumtext,
  `icon` varchar(100) DEFAULT NULL,
  `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(100) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`);

ALTER TABLE `notifications`
  MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;

CREATE TABLE `accountmanagement_companies` (
  `id` int(9) NOT NULL,
  `name` varchar(255) NOT NULL,
  `number` varchar(4) NOT NULL,
  `subnumber` varchar(2) NOT NULL DEFAULT '00',
  `address` mediumtext NOT NULL,
  `permissions` mediumtext NOT NULL,
  `crdate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE `problems` (
  `id` int(9) NOT NULL,
  `type` varchar(20) NOT NULL,
  `service` varchar(50) NOT NULL,
  `description` mediumtext NOT NULL,
  `screenshot` varchar(255) DEFAULT NULL,
  `steps` mediumtext,
  `os` varchar(50) DEFAULT NULL,
  `browser` varchar(50) DEFAULT NULL,
  `browserversion` varchar(255) DEFAULT NULL,
  `username` varchar(100) NOT NULL,
  `status` int(1) NOT NULL DEFAULT '0',
  `assigned` varchar(100) DEFAULT NULL,
  `crdate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

ALTER TABLE technical_data_inform_on_download_list ADD COlUMN tdata_name varchar(255) DEFAULT NULL AFTER email;
ALTER TABLE technical_data_inform_on_download_list ADD COlUMN tdata_email varchar(255) DEFAULT NULL AFTER tdata_name;
ALTER TABLE technical_data_inform_on_download_list ADD COlUMN tdata_number varchar(255) DEFAULT NULL AFTER tdata_email;

-- Datenbank: `portal_service`
--
CREATE DATABASE IF NOT EXISTS `portal_service` DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci;
USE `portal_service`;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `files`
--

CREATE TABLE `files` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `filename` varchar(255) NOT NULL,
  `filesize` int(11) NOT NULL,
  `type` varchar(30) DEFAULT NULL,
  `language` varchar(6) DEFAULT NULL,
  `crdate` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `salesinformation`
--

CREATE TABLE `salesinformation` (
  `id` int(11) NOT NULL,
  `content` mediumtext NOT NULL,
  `receivers` text NOT NULL,
  `categories` text,
  `draft` tinyint(4) NOT NULL DEFAULT '0',
  `published` tinyint(1) NOT NULL DEFAULT '0',
  `email` tinyint(1) NOT NULL,
  `email_sent` tinyint(1) NOT NULL DEFAULT '0',
  `receiver_emails` text,
  `receiver_emails_read` text,
  `receiver_emails_optout` text,
  `author` varchar(255) NOT NULL,
  `author_email` varchar(255) NOT NULL,
  `author_phone` varchar(255) DEFAULT NULL,
  `author_teamroom_id` int(9) DEFAULT NULL,
  `internal_memo` text,
  `release_date` int(11) DEFAULT NULL,
  `publishdate` int(11) DEFAULT NULL,
  `creator` varchar(255) NOT NULL,
  `crdate` int(11) NOT NULL,
  `editor` varchar(255) DEFAULT NULL,
  `editdate` int(11) DEFAULT NULL,
  `retracted` tinyint(1) DEFAULT '0',
  `retractor` varchar(255) DEFAULT NULL,
  `retractdate` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `salesinformation_favorite`
--

CREATE TABLE `salesinformation_favorite` (
  `id` int(11) NOT NULL,
  `salesinformation_id` int(11) NOT NULL,
  `username` varchar(255) NOT NULL,
  `crdate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `salesinformation_files`
--

CREATE TABLE `salesinformation_files` (
  `id` int(11) NOT NULL,
  `salesinformation_id` int(11) NOT NULL,
  `file_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `salesinformation_optin`
--

CREATE TABLE `salesinformation_optin` (
  `username` varchar(255) CHARACTER SET latin1 NOT NULL,
  `categories` text NOT NULL,
  `crdate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `salesinformation_reference`
--

CREATE TABLE `salesinformation_reference` (
  `id` int(9) NOT NULL,
  `salesinformation_id` int(9) NOT NULL,
  `reference_salesinformation_id` int(9) NOT NULL,
  `replacement` tinyint(1) NOT NULL DEFAULT '0',
  `reason_de` varchar(255) DEFAULT NULL,
  `reason_gb` varchar(255) DEFAULT NULL,
  `reason_fr` varchar(255) DEFAULT NULL,
  `creator` varchar(255) NOT NULL,
  `crdate` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `salesinformation_viewed`
--

CREATE TABLE `salesinformation_viewed` (
  `id` int(11) NOT NULL,
  `salesinformation_id` int(11) NOT NULL,
  `username` varchar(255) NOT NULL,
  `crdate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Indizes der exportierten Tabellen
--

--
-- Indizes für die Tabelle `files`
--
ALTER TABLE `files`
  ADD PRIMARY KEY (`id`);

--
-- Indizes für die Tabelle `salesinformation`
--
ALTER TABLE `salesinformation`
  ADD PRIMARY KEY (`id`);

--
-- Indizes für die Tabelle `salesinformation_favorite`
--
ALTER TABLE `salesinformation_favorite`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `favorite_unique` (`salesinformation_id`,`username`);

--
-- Indizes für die Tabelle `salesinformation_files`
--
ALTER TABLE `salesinformation_files`
  ADD PRIMARY KEY (`id`);

--
-- Indizes für die Tabelle `salesinformation_optin`
--
ALTER TABLE `salesinformation_optin`
  ADD PRIMARY KEY (`username`);

--
-- Indizes für die Tabelle `salesinformation_reference`
--
ALTER TABLE `salesinformation_reference`
  ADD PRIMARY KEY (`id`);

--
-- Indizes für die Tabelle `salesinformation_viewed`
--
ALTER TABLE `salesinformation_viewed`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT für exportierte Tabellen
--

--
-- AUTO_INCREMENT für Tabelle `files`
--
ALTER TABLE `files`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `salesinformation`
--
ALTER TABLE `salesinformation`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `salesinformation_favorite`
--
ALTER TABLE `salesinformation_favorite`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `salesinformation_files`
--
ALTER TABLE `salesinformation_files`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `salesinformation_reference`
--
ALTER TABLE `salesinformation_reference`
  MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `salesinformation_viewed`
--
ALTER TABLE `salesinformation_viewed`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;


USE `portal_service`;
DROP USER IF EXISTS 'service'@'172.%.%.%';
CREATE USER 'service'@'172.%.%.%' IDENTIFIED BY '9CREvsfAAbuKXPCS';
GRANT SELECT, INSERT, UPDATE, DELETE ON portal_service.* TO 'service'@'172.%.%.%';


-- Datenbank: `portal_montage`
--
CREATE DATABASE IF NOT EXISTS `portal_montage` DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci;
USE `portal_montage`;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `files`
--

CREATE TABLE `files` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `filename` varchar(255) NOT NULL,
  `filesize` int(11) NOT NULL,
  `type` varchar(30) DEFAULT NULL,
  `language` varchar(6) DEFAULT NULL,
  `crdate` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `salesinformation`
--

CREATE TABLE `salesinformation` (
  `id` int(11) NOT NULL,
  `content` mediumtext NOT NULL,
  `receivers` text NOT NULL,
  `categories` text,
  `draft` tinyint(4) NOT NULL DEFAULT '0',
  `published` tinyint(1) NOT NULL DEFAULT '0',
  `email` tinyint(1) NOT NULL,
  `email_sent` tinyint(1) NOT NULL DEFAULT '0',
  `receiver_emails` text,
  `receiver_emails_read` text,
  `receiver_emails_optout` text,
  `author` varchar(255) NOT NULL,
  `author_email` varchar(255) NOT NULL,
  `author_phone` varchar(255) DEFAULT NULL,
  `author_teamroom_id` int(9) DEFAULT NULL,
  `internal_memo` text,
  `release_date` int(11) DEFAULT NULL,
  `publishdate` int(11) DEFAULT NULL,
  `creator` varchar(255) NOT NULL,
  `crdate` int(11) NOT NULL,
  `editor` varchar(255) DEFAULT NULL,
  `editdate` int(11) DEFAULT NULL,
  `retracted` tinyint(1) DEFAULT '0',
  `retractor` varchar(255) DEFAULT NULL,
  `retractdate` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `salesinformation_favorite`
--

CREATE TABLE `salesinformation_favorite` (
  `id` int(11) NOT NULL,
  `salesinformation_id` int(11) NOT NULL,
  `username` varchar(255) NOT NULL,
  `crdate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `salesinformation_files`
--

CREATE TABLE `salesinformation_files` (
  `id` int(11) NOT NULL,
  `salesinformation_id` int(11) NOT NULL,
  `file_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `salesinformation_optin`
--

CREATE TABLE `salesinformation_optin` (
  `username` varchar(255) CHARACTER SET latin1 NOT NULL,
  `categories` text NOT NULL,
  `crdate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `salesinformation_reference`
--

CREATE TABLE `salesinformation_reference` (
  `id` int(9) NOT NULL,
  `salesinformation_id` int(9) NOT NULL,
  `reference_salesinformation_id` int(9) NOT NULL,
  `replacement` tinyint(1) NOT NULL DEFAULT '0',
  `reason_de` varchar(255) DEFAULT NULL,
  `reason_gb` varchar(255) DEFAULT NULL,
  `reason_fr` varchar(255) DEFAULT NULL,
  `creator` varchar(255) NOT NULL,
  `crdate` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `salesinformation_viewed`
--

CREATE TABLE `salesinformation_viewed` (
  `id` int(11) NOT NULL,
  `salesinformation_id` int(11) NOT NULL,
  `username` varchar(255) NOT NULL,
  `crdate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Indizes der exportierten Tabellen
--

--
-- Indizes für die Tabelle `files`
--
ALTER TABLE `files`
  ADD PRIMARY KEY (`id`);

--
-- Indizes für die Tabelle `salesinformation`
--
ALTER TABLE `salesinformation`
  ADD PRIMARY KEY (`id`);

--
-- Indizes für die Tabelle `salesinformation_favorite`
--
ALTER TABLE `salesinformation_favorite`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `favorite_unique` (`salesinformation_id`,`username`);

--
-- Indizes für die Tabelle `salesinformation_files`
--
ALTER TABLE `salesinformation_files`
  ADD PRIMARY KEY (`id`);

--
-- Indizes für die Tabelle `salesinformation_optin`
--
ALTER TABLE `salesinformation_optin`
  ADD PRIMARY KEY (`username`);

--
-- Indizes für die Tabelle `salesinformation_reference`
--
ALTER TABLE `salesinformation_reference`
  ADD PRIMARY KEY (`id`);

--
-- Indizes für die Tabelle `salesinformation_viewed`
--
ALTER TABLE `salesinformation_viewed`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT für exportierte Tabellen
--

--
-- AUTO_INCREMENT für Tabelle `files`
--
ALTER TABLE `files`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `salesinformation`
--
ALTER TABLE `salesinformation`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `salesinformation_favorite`
--
ALTER TABLE `salesinformation_favorite`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `salesinformation_files`
--
ALTER TABLE `salesinformation_files`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `salesinformation_reference`
--
ALTER TABLE `salesinformation_reference`
  MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `salesinformation_viewed`
--
ALTER TABLE `salesinformation_viewed`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;


USE `portal_montage`;
DROP USER IF EXISTS 'montage'@'172.%.%.%';
CREATE USER 'montage'@'172.%.%.%' IDENTIFIED BY 'fMMdCnLzrbcEE4U4';
GRANT SELECT, INSERT, UPDATE, DELETE ON portal_montage.* TO 'montage'@'172.%.%.%';