-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 4.6.4
-- https://www.phpmyadmin.net/
--
-- Host: abus_portal_2017_db
-- Erstellungszeit: 07. Dez 2016 um 14:47
-- Server-Version: 10.1.17-MariaDB-1~jessie
-- <PERSON>HP-Version: 5.6.25

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Datenbank: `docu`
--
CREATE DATABASE IF NOT EXISTS `docu` DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci;
USE `docu`;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `crawler`
--

CREATE TABLE `crawler` (
  `id` int(9) NOT NULL,
  `language` varchar(50) CHARACTER SET utf8 NOT NULL,
  `path` varchar(255) CHARACTER SET utf8 NOT NULL,
  `filename` varchar(255) CHARACTER SET utf8 NOT NULL,
  `filetype` varchar(50) CHARACTER SET utf8 NOT NULL,
  `filesize` int(9) NOT NULL,
  `crdate` datetime NOT NULL,
  `indexed` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf16 COLLATE=utf16_bin;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `log_download`
--

CREATE TABLE `log_download` (
  `id` int(11) NOT NULL,
  `name` varchar(100) CHARACTER SET utf8 DEFAULT NULL,
  `company` varchar(100) CHARACTER SET utf8 DEFAULT NULL,
  `email` varchar(100) CHARACTER SET utf8 NOT NULL,
  `file` varchar(100) CHARACTER SET utf8 NOT NULL,
  `crdate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `pathFinder`
--

CREATE TABLE `pathFinder` (
  `pathArray` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Indizes der exportierten Tabellen
--

--
-- Indizes für die Tabelle `crawler`
--
ALTER TABLE `crawler`
  ADD PRIMARY KEY (`id`);

--
-- Indizes für die Tabelle `log_download`
--
ALTER TABLE `log_download`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT für exportierte Tabellen
--

--
-- AUTO_INCREMENT für Tabelle `crawler`
--
ALTER TABLE `crawler`
  MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `log_download`
--
ALTER TABLE `log_download`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;--
-- Datenbank: `portal`
--
CREATE DATABASE IF NOT EXISTS `portal` DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci;
USE `portal`;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `accountmanagement`
--

CREATE TABLE `accountmanagement` (
  `id` int(11) NOT NULL,
  `type` varchar(20) NOT NULL,
  `manager` varchar(255) NOT NULL,
  `manager_company` varchar(255) NOT NULL,
  `manager_email` varchar(255) NOT NULL,
  `task` mediumtext NOT NULL,
  `allowed` tinyint(1) DEFAULT NULL,
  `ip` varchar(50) NOT NULL,
  `hash` varchar(30) NOT NULL,
  `crdate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modified` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `accountmanagement_companies`
--

CREATE TABLE `accountmanagement_companies` (
  `id` int(9) NOT NULL,
  `name` varchar(255) NOT NULL,
  `number` varchar(4) NOT NULL,
  `subnumber` varchar(2) NOT NULL DEFAULT '00',
  `address` mediumtext NOT NULL,
  `permissions` mediumtext NOT NULL,
  `crdate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `accountmanagement_company_permission`
--

CREATE TABLE `accountmanagement_company_permission` (
  `email` varchar(255) NOT NULL,
  `companies` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `accountmanagement_logging`
--

CREATE TABLE `accountmanagement_logging` (
  `id` int(9) NOT NULL,
  `event` varchar(100) NOT NULL,
  `message` longtext NOT NULL,
  `user` varchar(255) NOT NULL,
  `usermodeinitiator` varchar(255) DEFAULT NULL,
  `crdate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `country`
--

CREATE TABLE `country` (
  `iso` char(2) NOT NULL,
  `name` varchar(80) NOT NULL,
  `printable_name` varchar(80) NOT NULL,
  `printable_name_de` varchar(80) NOT NULL,
  `iso3` char(3) DEFAULT NULL,
  `numcode` smallint(6) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `error_log_abumobile`
--

CREATE TABLE `error_log_abumobile` (
  `hash` varchar(40) NOT NULL,
  `datetime` datetime DEFAULT NULL,
  `type` varchar(50) DEFAULT NULL,
  `pid` int(9) DEFAULT NULL,
  `tid` int(9) DEFAULT NULL,
  `cid` int(9) DEFAULT NULL,
  `msg` text,
  `client` varchar(255) DEFAULT NULL,
  `server` varchar(255) DEFAULT NULL,
  `request` varchar(255) DEFAULT NULL,
  `host` varchar(255) DEFAULT NULL,
  `edit_by` varchar(255) DEFAULT NULL,
  `edit_date` datetime DEFAULT NULL,
  `solved_by` varchar(255) DEFAULT NULL,
  `solved_date` datetime DEFAULT NULL,
  `solved_primary` tinyint(1) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `error_log_count`
--

CREATE TABLE `error_log_count` (
  `tablename` varchar(255) NOT NULL,
  `counter` int(9) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `error_log_download`
--

CREATE TABLE `error_log_download` (
  `hash` varchar(40) NOT NULL,
  `datetime` datetime DEFAULT NULL,
  `type` varchar(50) DEFAULT NULL,
  `pid` int(9) DEFAULT NULL,
  `tid` int(9) DEFAULT NULL,
  `cid` int(9) DEFAULT NULL,
  `msg` text,
  `client` varchar(255) DEFAULT NULL,
  `server` varchar(255) DEFAULT NULL,
  `request` varchar(255) DEFAULT NULL,
  `host` varchar(255) DEFAULT NULL,
  `edit_by` varchar(255) DEFAULT NULL,
  `edit_date` datetime DEFAULT NULL,
  `solved_by` varchar(255) DEFAULT NULL,
  `solved_date` datetime DEFAULT NULL,
  `solved_primary` tinyint(1) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `error_log_portal`
--

CREATE TABLE `error_log_portal` (
  `hash` varchar(40) NOT NULL,
  `datetime` datetime DEFAULT NULL,
  `type` varchar(50) DEFAULT NULL,
  `pid` int(9) DEFAULT NULL,
  `tid` int(9) DEFAULT NULL,
  `cid` int(9) DEFAULT NULL,
  `msg` text,
  `client` varchar(255) DEFAULT NULL,
  `server` varchar(255) DEFAULT NULL,
  `request` varchar(255) DEFAULT NULL,
  `host` varchar(255) DEFAULT NULL,
  `edit_by` varchar(255) DEFAULT NULL,
  `edit_date` datetime DEFAULT NULL,
  `solved_by` varchar(255) DEFAULT NULL,
  `solved_date` datetime DEFAULT NULL,
  `solved_primary` tinyint(1) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `error_log_website_de`
--

CREATE TABLE `error_log_website_de` (
  `hash` varchar(40) NOT NULL,
  `datetime` datetime DEFAULT NULL,
  `type` varchar(50) DEFAULT NULL,
  `pid` int(9) DEFAULT NULL,
  `tid` int(9) DEFAULT NULL,
  `cid` int(9) DEFAULT NULL,
  `msg` text,
  `client` varchar(255) DEFAULT NULL,
  `server` varchar(255) DEFAULT NULL,
  `request` varchar(255) DEFAULT NULL,
  `host` varchar(255) DEFAULT NULL,
  `edit_by` varchar(255) DEFAULT NULL,
  `edit_date` datetime DEFAULT NULL,
  `solved_by` varchar(255) DEFAULT NULL,
  `solved_date` datetime DEFAULT NULL,
  `solved_primary` tinyint(1) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `error_log_website_es`
--

CREATE TABLE `error_log_website_es` (
  `hash` varchar(40) NOT NULL,
  `datetime` datetime DEFAULT NULL,
  `type` varchar(50) DEFAULT NULL,
  `pid` int(9) DEFAULT NULL,
  `tid` int(9) DEFAULT NULL,
  `cid` int(9) DEFAULT NULL,
  `msg` text,
  `client` varchar(255) DEFAULT NULL,
  `server` varchar(255) DEFAULT NULL,
  `request` varchar(255) DEFAULT NULL,
  `host` varchar(255) DEFAULT NULL,
  `edit_by` varchar(255) DEFAULT NULL,
  `edit_date` datetime DEFAULT NULL,
  `solved_by` varchar(255) DEFAULT NULL,
  `solved_date` datetime DEFAULT NULL,
  `solved_primary` tinyint(1) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `error_log_website_fr`
--

CREATE TABLE `error_log_website_fr` (
  `hash` varchar(40) NOT NULL,
  `datetime` datetime DEFAULT NULL,
  `type` varchar(50) DEFAULT NULL,
  `pid` int(9) DEFAULT NULL,
  `tid` int(9) DEFAULT NULL,
  `cid` int(9) DEFAULT NULL,
  `msg` text,
  `client` varchar(255) DEFAULT NULL,
  `server` varchar(255) DEFAULT NULL,
  `request` varchar(255) DEFAULT NULL,
  `host` varchar(255) DEFAULT NULL,
  `edit_by` varchar(255) DEFAULT NULL,
  `edit_date` datetime DEFAULT NULL,
  `solved_by` varchar(255) DEFAULT NULL,
  `solved_date` datetime DEFAULT NULL,
  `solved_primary` tinyint(1) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `error_log_website_pl`
--

CREATE TABLE `error_log_website_pl` (
  `hash` varchar(40) NOT NULL,
  `datetime` datetime DEFAULT NULL,
  `type` varchar(50) DEFAULT NULL,
  `pid` int(9) DEFAULT NULL,
  `tid` int(9) DEFAULT NULL,
  `cid` int(9) DEFAULT NULL,
  `msg` text,
  `client` varchar(255) DEFAULT NULL,
  `server` varchar(255) DEFAULT NULL,
  `request` varchar(255) DEFAULT NULL,
  `host` varchar(255) DEFAULT NULL,
  `edit_by` varchar(255) DEFAULT NULL,
  `edit_date` datetime DEFAULT NULL,
  `solved_by` varchar(255) DEFAULT NULL,
  `solved_date` datetime DEFAULT NULL,
  `solved_primary` tinyint(1) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `error_log_website_se`
--

CREATE TABLE `error_log_website_se` (
  `hash` varchar(40) NOT NULL,
  `datetime` datetime DEFAULT NULL,
  `type` varchar(50) DEFAULT NULL,
  `pid` int(9) DEFAULT NULL,
  `tid` int(9) DEFAULT NULL,
  `cid` int(9) DEFAULT NULL,
  `msg` text,
  `client` varchar(255) DEFAULT NULL,
  `server` varchar(255) DEFAULT NULL,
  `request` varchar(255) DEFAULT NULL,
  `host` varchar(255) DEFAULT NULL,
  `edit_by` varchar(255) DEFAULT NULL,
  `edit_date` datetime DEFAULT NULL,
  `solved_by` varchar(255) DEFAULT NULL,
  `solved_date` datetime DEFAULT NULL,
  `solved_primary` tinyint(1) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `forgotpassword`
--

CREATE TABLE `forgotpassword` (
  `id` int(9) NOT NULL,
  `email` varchar(255) NOT NULL,
  `hash` varchar(255) NOT NULL,
  `ip` varchar(255) NOT NULL,
  `crdate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `used` tinyint(1) NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `ip2nation`
--

CREATE TABLE `ip2nation` (
  `ip` int(11) UNSIGNED NOT NULL DEFAULT '0',
  `country` char(2) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `ldapGroups`
--

CREATE TABLE `ldapGroups` (
  `id` int(9) NOT NULL,
  `service_id` int(9) NOT NULL,
  `name` varchar(255) NOT NULL,
  `name_nice` varchar(255) DEFAULT NULL,
  `hidden` tinyint(1) NOT NULL DEFAULT '0',
  `deleted` tinyint(1) NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `ldapTmp`
--

CREATE TABLE `ldapTmp` (
  `id` int(11) NOT NULL,
  `user` text NOT NULL,
  `crdate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `log_activity`
--

CREATE TABLE `log_activity` (
  `id` int(9) NOT NULL,
  `service` varchar(50) NOT NULL,
  `name` varchar(100) DEFAULT NULL,
  `company` varchar(100) DEFAULT NULL,
  `email` varchar(100) NOT NULL,
  `activity` text NOT NULL,
  `browser` varchar(255) NOT NULL,
  `ip` varchar(20) NOT NULL,
  `crdate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `log_authentication`
--

CREATE TABLE `log_authentication` (
  `id` int(11) NOT NULL,
  `name` varchar(100) DEFAULT NULL,
  `company` varchar(100) DEFAULT NULL,
  `email` varchar(100) NOT NULL,
  `login_point` varchar(100) NOT NULL,
  `ip` varchar(100) DEFAULT NULL,
  `browser` varchar(255) DEFAULT NULL,
  `crdate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `log_sales_download`
--

CREATE TABLE `log_sales_download` (
  `id` int(11) NOT NULL,
  `name` varchar(100) DEFAULT NULL,
  `company` varchar(100) DEFAULT NULL,
  `email` varchar(100) NOT NULL,
  `pdf_location` varchar(255) DEFAULT NULL,
  `file` varchar(100) NOT NULL,
  `crdate` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `log_technical_data_download`
--

CREATE TABLE `log_technical_data_download` (
  `id` int(11) NOT NULL,
  `name` varchar(100) DEFAULT NULL,
  `company` varchar(100) DEFAULT NULL,
  `email` varchar(100) NOT NULL,
  `pdf_location` varchar(255) DEFAULT NULL,
  `file` varchar(100) NOT NULL,
  `crdate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `logging`
--

CREATE TABLE `logging` (
  `id` int(11) NOT NULL,
  `service` varchar(100) NOT NULL,
  `type` varchar(100) NOT NULL,
  `message` text NOT NULL,
  `user` text,
  `user_agent` text,
  `ip` varchar(30) DEFAULT NULL,
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `level` varchar(10) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `logging2`
--

CREATE TABLE `logging2` (
  `id` int(11) NOT NULL,
  `service` varchar(100) NOT NULL,
  `type` varchar(100) NOT NULL,
  `message` text NOT NULL,
  `user` text,
  `user_agent` text,
  `ip` varchar(30) DEFAULT NULL,
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `level` varchar(10) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `logging_cycle_2014-11-24`
--

CREATE TABLE `logging_cycle_2014-11-24` (
  `id` int(11) NOT NULL,
  `service` varchar(100) NOT NULL,
  `type` varchar(100) NOT NULL,
  `message` text NOT NULL,
  `user` text,
  `user_agent` text,
  `ip` varchar(30) DEFAULT NULL,
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `level` varchar(10) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `logging_step1`
--

CREATE TABLE `logging_step1` (
  `id` int(9) NOT NULL,
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `hash` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `logging_step2`
--

CREATE TABLE `logging_step2` (
  `id` int(9) NOT NULL,
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `hash` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `logging_tmp`
--

CREATE TABLE `logging_tmp` (
  `id` int(11) NOT NULL,
  `service` varchar(100) NOT NULL,
  `type` varchar(100) NOT NULL,
  `message` text NOT NULL,
  `user` text,
  `user_agent` text,
  `ip` varchar(30) DEFAULT NULL,
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `level` varchar(10) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `notifications`
--

CREATE TABLE `notifications` (
  `id` int(9) NOT NULL,
  `username` varchar(100) DEFAULT NULL,
  `group` varchar(100) DEFAULT NULL,
  `name_de` varchar(100) DEFAULT NULL,
  `name_en` varchar(100) DEFAULT NULL,
  `name_fr` varchar(100) DEFAULT NULL,
  `name_es` varchar(100) DEFAULT NULL,
  `content_de` mediumtext,
  `content_en` mediumtext,
  `content_fr` mediumtext,
  `content_es` mediumtext,
  `icon` varchar(100) DEFAULT NULL,
  `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(100) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `problems`
--

CREATE TABLE `problems` (
  `id` int(9) NOT NULL,
  `type` varchar(20) NOT NULL,
  `service` varchar(50) NOT NULL,
  `description` mediumtext NOT NULL,
  `screenshot` varchar(255) DEFAULT NULL,
  `steps` mediumtext,
  `os` varchar(50) DEFAULT NULL,
  `browser` varchar(50) DEFAULT NULL,
  `browserversion` varchar(255) DEFAULT NULL,
  `username` varchar(100) NOT NULL,
  `status` int(1) NOT NULL DEFAULT '0',
  `assigned` varchar(100) DEFAULT NULL,
  `crdate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `request`
--

CREATE TABLE `request` (
  `id` int(11) NOT NULL,
  `customernumber` varchar(255) DEFAULT NULL,
  `company` varchar(255) NOT NULL,
  `function` varchar(100) NOT NULL,
  `gender` int(1) NOT NULL,
  `title` varchar(100) NOT NULL,
  `firstname` varchar(255) NOT NULL,
  `lastname` varchar(255) NOT NULL,
  `street` varchar(255) NOT NULL,
  `postcode` varchar(255) NOT NULL,
  `town` varchar(255) NOT NULL,
  `country` varchar(255) NOT NULL,
  `telephone` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `requestedServices` text NOT NULL,
  `ip` varchar(100) NOT NULL,
  `crdate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `hash` varchar(100) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `requestFurther`
--

CREATE TABLE `requestFurther` (
  `id` int(11) NOT NULL,
  `name` varchar(100) DEFAULT NULL,
  `company` varchar(100) NOT NULL,
  `email` varchar(255) NOT NULL,
  `ip` varchar(100) NOT NULL,
  `requestedServices` text NOT NULL,
  `crdate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `hash` varchar(100) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `requestFurther_reply`
--

CREATE TABLE `requestFurther_reply` (
  `id` int(9) NOT NULL,
  `request_id` int(9) NOT NULL,
  `request_hash` varchar(100) NOT NULL,
  `service_id` int(9) NOT NULL,
  `service_key` varchar(100) NOT NULL,
  `sub_service_id` int(9) DEFAULT NULL,
  `sub_service_key` varchar(100) DEFAULT NULL,
  `level` varchar(100) DEFAULT NULL,
  `allowed` tinyint(1) NOT NULL DEFAULT '0',
  `ip` varchar(100) NOT NULL,
  `crdate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `request_reply`
--

CREATE TABLE `request_reply` (
  `id` int(9) NOT NULL,
  `request_id` int(9) NOT NULL,
  `request_hash` varchar(100) NOT NULL,
  `service_id` int(9) NOT NULL,
  `service_key` varchar(100) NOT NULL,
  `sub_service_id` int(9) DEFAULT NULL,
  `sub_service_key` varchar(100) DEFAULT NULL,
  `level` varchar(100) DEFAULT NULL,
  `allowed` tinyint(1) NOT NULL DEFAULT '0',
  `ip` varchar(100) NOT NULL,
  `crdate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `services`
--

CREATE TABLE `services` (
  `id` int(9) NOT NULL,
  `name` varchar(100) NOT NULL,
  `name_de` varchar(100) NOT NULL,
  `name_fr` varchar(100) DEFAULT NULL,
  `name_es` varchar(100) DEFAULT NULL,
  `description` text,
  `description_de` text,
  `description_fr` text,
  `description_es` text,
  `image` varchar(255) DEFAULT NULL,
  `image_de` varchar(255) DEFAULT NULL,
  `image_fr` varchar(255) DEFAULT NULL,
  `image_es` varchar(255) DEFAULT NULL,
  `icon` varchar(50) NOT NULL,
  `subdomain` varchar(20) DEFAULT NULL,
  `link` varchar(255) NOT NULL,
  `application_key` varchar(100) DEFAULT NULL,
  `secukey` varchar(100) NOT NULL,
  `decision_maker_email` varchar(100) DEFAULT NULL,
  `need_login` tinyint(1) NOT NULL DEFAULT '1',
  `requestable` tinyint(1) NOT NULL DEFAULT '0',
  `sort` int(3) NOT NULL,
  `hidden` tinyint(1) NOT NULL DEFAULT '0',
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `maintenance` tinyint(1) NOT NULL DEFAULT '0',
  `crdate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `sub_services`
--

CREATE TABLE `sub_services` (
  `id` int(9) NOT NULL,
  `service_id` int(9) NOT NULL,
  `teamroom_project_id` int(9) DEFAULT NULL,
  `secukey` varchar(100) NOT NULL,
  `name` varchar(255) NOT NULL,
  `name_de` varchar(100) NOT NULL,
  `name_fr` varchar(100) DEFAULT NULL,
  `name_es` varchar(100) DEFAULT NULL,
  `description` text,
  `description_de` text NOT NULL,
  `description_fr` text,
  `description_es` text,
  `decision_maker_email` varchar(100) NOT NULL,
  `sort` int(11) DEFAULT NULL,
  `crdate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `hidden` tinyint(4) NOT NULL DEFAULT '0',
  `deleted` tinyint(4) NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `technical_data_inform_on_download`
--

CREATE TABLE `technical_data_inform_on_download` (
  `id` int(9) NOT NULL,
  `customernumber` varchar(255) DEFAULT NULL,
  `user_email` varchar(100) NOT NULL,
  `receiver_emails` text NOT NULL,
  `receiver_emails_cc` text,
  `receiver_emails_bcc` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `technical_data_inform_on_download_list`
--

CREATE TABLE `technical_data_inform_on_download_list` (
  `id` int(9) NOT NULL,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `tdata_name` varchar(255) DEFAULT NULL,
  `tdata_email` varchar(255) DEFAULT NULL,
  `tdata_number` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `technical_data_limit_per_month`
--

CREATE TABLE `technical_data_limit_per_month` (
  `email` varchar(255) NOT NULL,
  `limitation` int(5) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `user_settings`
--

CREATE TABLE `user_settings` (
  `id` int(9) NOT NULL,
  `username` varchar(255) NOT NULL,
  `skey` varchar(50) NOT NULL,
  `svalue` varchar(255) NOT NULL,
  `crdate` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `firstname` varchar(255) DEFAULT NULL,
  `lastname` varchar(255) DEFAULT NULL,
  `email` varchar(255) NOT NULL,
  `company` varchar(255) DEFAULT NULL,
  `telephone` varchar(255) DEFAULT NULL,
  `groups` text NOT NULL,
  `teamrooms` text,
  `crdate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `lastvisit` datetime DEFAULT NULL,
  `lastactivity` datetime DEFAULT NULL,
  `lastvisit_teamroom` datetime DEFAULT NULL,
  `lastlogon` datetime DEFAULT NULL,
  `ldapname` varchar(255) DEFAULT NULL,
  `useraccountcontrol` int(9) DEFAULT NULL,
  `download_count_tdata` int(9) NOT NULL DEFAULT '0',
  `download_count_docu` int(9) NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Indizes der exportierten Tabellen
--

--
-- Indizes für die Tabelle `accountmanagement`
--
ALTER TABLE `accountmanagement`
  ADD PRIMARY KEY (`id`);

--
-- Indizes für die Tabelle `accountmanagement_companies`
--
ALTER TABLE `accountmanagement_companies`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`name`);

--
-- Indizes für die Tabelle `accountmanagement_company_permission`
--
ALTER TABLE `accountmanagement_company_permission`
  ADD PRIMARY KEY (`email`);

--
-- Indizes für die Tabelle `accountmanagement_logging`
--
ALTER TABLE `accountmanagement_logging`
  ADD PRIMARY KEY (`id`);

--
-- Indizes für die Tabelle `country`
--
ALTER TABLE `country`
  ADD PRIMARY KEY (`iso`);

--
-- Indizes für die Tabelle `error_log_abumobile`
--
ALTER TABLE `error_log_abumobile`
  ADD PRIMARY KEY (`hash`);

--
-- Indizes für die Tabelle `error_log_count`
--
ALTER TABLE `error_log_count`
  ADD PRIMARY KEY (`tablename`);

--
-- Indizes für die Tabelle `error_log_download`
--
ALTER TABLE `error_log_download`
  ADD PRIMARY KEY (`hash`);

--
-- Indizes für die Tabelle `error_log_portal`
--
ALTER TABLE `error_log_portal`
  ADD PRIMARY KEY (`hash`);

--
-- Indizes für die Tabelle `error_log_website_de`
--
ALTER TABLE `error_log_website_de`
  ADD PRIMARY KEY (`hash`);

--
-- Indizes für die Tabelle `error_log_website_es`
--
ALTER TABLE `error_log_website_es`
  ADD PRIMARY KEY (`hash`);

--
-- Indizes für die Tabelle `error_log_website_fr`
--
ALTER TABLE `error_log_website_fr`
  ADD PRIMARY KEY (`hash`);

--
-- Indizes für die Tabelle `error_log_website_pl`
--
ALTER TABLE `error_log_website_pl`
  ADD PRIMARY KEY (`hash`);

--
-- Indizes für die Tabelle `error_log_website_se`
--
ALTER TABLE `error_log_website_se`
  ADD PRIMARY KEY (`hash`);

--
-- Indizes für die Tabelle `forgotpassword`
--
ALTER TABLE `forgotpassword`
  ADD PRIMARY KEY (`id`);

--
-- Indizes für die Tabelle `ip2nation`
--
ALTER TABLE `ip2nation`
  ADD KEY `ip` (`ip`);

--
-- Indizes für die Tabelle `ldapGroups`
--
ALTER TABLE `ldapGroups`
  ADD PRIMARY KEY (`id`);

--
-- Indizes für die Tabelle `ldapTmp`
--
ALTER TABLE `ldapTmp`
  ADD PRIMARY KEY (`id`);

--
-- Indizes für die Tabelle `log_activity`
--
ALTER TABLE `log_activity`
  ADD PRIMARY KEY (`id`);

--
-- Indizes für die Tabelle `log_authentication`
--
ALTER TABLE `log_authentication`
  ADD PRIMARY KEY (`id`);

--
-- Indizes für die Tabelle `log_sales_download`
--
ALTER TABLE `log_sales_download`
  ADD PRIMARY KEY (`id`);

--
-- Indizes für die Tabelle `log_technical_data_download`
--
ALTER TABLE `log_technical_data_download`
  ADD PRIMARY KEY (`id`);

--
-- Indizes für die Tabelle `logging`
--
ALTER TABLE `logging`
  ADD PRIMARY KEY (`id`);

--
-- Indizes für die Tabelle `logging2`
--
ALTER TABLE `logging2`
  ADD PRIMARY KEY (`id`);

--
-- Indizes für die Tabelle `logging_cycle_2014-11-24`
--
ALTER TABLE `logging_cycle_2014-11-24`
  ADD PRIMARY KEY (`id`);

--
-- Indizes für die Tabelle `logging_tmp`
--
ALTER TABLE `logging_tmp`
  ADD PRIMARY KEY (`id`);

--
-- Indizes für die Tabelle `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`);

--
-- Indizes für die Tabelle `problems`
--
ALTER TABLE `problems`
  ADD PRIMARY KEY (`id`);

--
-- Indizes für die Tabelle `request`
--
ALTER TABLE `request`
  ADD PRIMARY KEY (`id`);

--
-- Indizes für die Tabelle `requestFurther`
--
ALTER TABLE `requestFurther`
  ADD PRIMARY KEY (`id`);

--
-- Indizes für die Tabelle `requestFurther_reply`
--
ALTER TABLE `requestFurther_reply`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `OnlyOnce` (`request_id`,`request_hash`,`service_id`,`service_key`,`sub_service_id`,`sub_service_key`);

--
-- Indizes für die Tabelle `request_reply`
--
ALTER TABLE `request_reply`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `OnlyOnce` (`request_id`,`request_hash`,`service_id`,`service_key`,`sub_service_id`,`sub_service_key`);

--
-- Indizes für die Tabelle `services`
--
ALTER TABLE `services`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `key` (`secukey`);

--
-- Indizes für die Tabelle `sub_services`
--
ALTER TABLE `sub_services`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `service_key` (`secukey`);

--
-- Indizes für die Tabelle `technical_data_inform_on_download`
--
ALTER TABLE `technical_data_inform_on_download`
  ADD PRIMARY KEY (`id`);

--
-- Indizes für die Tabelle `technical_data_inform_on_download_list`
--
ALTER TABLE `technical_data_inform_on_download_list`
  ADD PRIMARY KEY (`id`);

--
-- Indizes für die Tabelle `technical_data_limit_per_month`
--
ALTER TABLE `technical_data_limit_per_month`
  ADD PRIMARY KEY (`email`);

--
-- Indizes für die Tabelle `user_settings`
--
ALTER TABLE `user_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `UniqueSetting` (`username`,`skey`);

--
-- Indizes für die Tabelle `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT für exportierte Tabellen
--

--
-- AUTO_INCREMENT für Tabelle `accountmanagement`
--
ALTER TABLE `accountmanagement`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `accountmanagement_companies`
--
ALTER TABLE `accountmanagement_companies`
  MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `accountmanagement_logging`
--
ALTER TABLE `accountmanagement_logging`
  MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `forgotpassword`
--
ALTER TABLE `forgotpassword`
  MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `ldapGroups`
--
ALTER TABLE `ldapGroups`
  MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `ldapTmp`
--
ALTER TABLE `ldapTmp`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `log_activity`
--
ALTER TABLE `log_activity`
  MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `log_authentication`
--
ALTER TABLE `log_authentication`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `log_sales_download`
--
ALTER TABLE `log_sales_download`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `log_technical_data_download`
--
ALTER TABLE `log_technical_data_download`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `logging`
--
ALTER TABLE `logging`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `logging2`
--
ALTER TABLE `logging2`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `logging_cycle_2014-11-24`
--
ALTER TABLE `logging_cycle_2014-11-24`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `logging_tmp`
--
ALTER TABLE `logging_tmp`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `problems`
--
ALTER TABLE `problems`
  MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `request`
--
ALTER TABLE `request`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `requestFurther`
--
ALTER TABLE `requestFurther`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `requestFurther_reply`
--
ALTER TABLE `requestFurther_reply`
  MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `request_reply`
--
ALTER TABLE `request_reply`
  MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `services`
--
ALTER TABLE `services`
  MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `sub_services`
--
ALTER TABLE `sub_services`
  MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `technical_data_inform_on_download`
--
ALTER TABLE `technical_data_inform_on_download`
  MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `technical_data_inform_on_download_list`
--
ALTER TABLE `technical_data_inform_on_download_list`
  MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `user_settings`
--
ALTER TABLE `user_settings`
  MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;--
-- Datenbank: `portal_montage`
--
CREATE DATABASE IF NOT EXISTS `portal_montage` DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci;
USE `portal_montage`;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `files`
--

CREATE TABLE `files` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `filename` varchar(255) NOT NULL,
  `filesize` int(11) NOT NULL,
  `type` varchar(30) DEFAULT NULL,
  `language` varchar(6) DEFAULT NULL,
  `crdate` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `salesinformation`
--

CREATE TABLE `salesinformation` (
  `id` int(11) NOT NULL,
  `content` mediumtext NOT NULL,
  `receivers` text NOT NULL,
  `categories` text,
  `draft` tinyint(4) NOT NULL DEFAULT '0',
  `published` tinyint(1) NOT NULL DEFAULT '0',
  `email` tinyint(1) NOT NULL,
  `email_sent` tinyint(1) NOT NULL DEFAULT '0',
  `receiver_emails` text,
  `receiver_emails_read` text,
  `receiver_emails_optout` text,
  `author` varchar(255) NOT NULL,
  `author_email` varchar(255) NOT NULL,
  `author_phone` varchar(255) DEFAULT NULL,
  `author_teamroom_id` int(9) DEFAULT NULL,
  `internal_memo` text,
  `release_date` int(11) DEFAULT NULL,
  `publishdate` int(11) DEFAULT NULL,
  `creator` varchar(255) NOT NULL,
  `crdate` int(11) NOT NULL,
  `editor` varchar(255) DEFAULT NULL,
  `editdate` int(11) DEFAULT NULL,
  `retracted` tinyint(1) DEFAULT '0',
  `retractor` varchar(255) DEFAULT NULL,
  `retractdate` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `salesinformation_favorite`
--

CREATE TABLE `salesinformation_favorite` (
  `id` int(11) NOT NULL,
  `salesinformation_id` int(11) NOT NULL,
  `username` varchar(255) NOT NULL,
  `crdate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `salesinformation_files`
--

CREATE TABLE `salesinformation_files` (
  `id` int(11) NOT NULL,
  `salesinformation_id` int(11) NOT NULL,
  `file_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `salesinformation_optin`
--

CREATE TABLE `salesinformation_optin` (
  `username` varchar(255) CHARACTER SET latin1 NOT NULL,
  `categories` text NOT NULL,
  `crdate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `salesinformation_reference`
--

CREATE TABLE `salesinformation_reference` (
  `id` int(9) NOT NULL,
  `salesinformation_id` int(9) NOT NULL,
  `reference_salesinformation_id` int(9) NOT NULL,
  `replacement` tinyint(1) NOT NULL DEFAULT '0',
  `reason_de` varchar(255) DEFAULT NULL,
  `reason_gb` varchar(255) DEFAULT NULL,
  `reason_fr` varchar(255) DEFAULT NULL,
  `creator` varchar(255) NOT NULL,
  `crdate` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `salesinformation_viewed`
--

CREATE TABLE `salesinformation_viewed` (
  `id` int(11) NOT NULL,
  `salesinformation_id` int(11) NOT NULL,
  `username` varchar(255) NOT NULL,
  `crdate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Indizes der exportierten Tabellen
--

--
-- Indizes für die Tabelle `files`
--
ALTER TABLE `files`
  ADD PRIMARY KEY (`id`);

--
-- Indizes für die Tabelle `salesinformation`
--
ALTER TABLE `salesinformation`
  ADD PRIMARY KEY (`id`);

--
-- Indizes für die Tabelle `salesinformation_favorite`
--
ALTER TABLE `salesinformation_favorite`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `favorite_unique` (`salesinformation_id`,`username`);

--
-- Indizes für die Tabelle `salesinformation_files`
--
ALTER TABLE `salesinformation_files`
  ADD PRIMARY KEY (`id`);

--
-- Indizes für die Tabelle `salesinformation_optin`
--
ALTER TABLE `salesinformation_optin`
  ADD PRIMARY KEY (`username`);

--
-- Indizes für die Tabelle `salesinformation_reference`
--
ALTER TABLE `salesinformation_reference`
  ADD PRIMARY KEY (`id`);

--
-- Indizes für die Tabelle `salesinformation_viewed`
--
ALTER TABLE `salesinformation_viewed`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT für exportierte Tabellen
--

--
-- AUTO_INCREMENT für Tabelle `files`
--
ALTER TABLE `files`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `salesinformation`
--
ALTER TABLE `salesinformation`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `salesinformation_favorite`
--
ALTER TABLE `salesinformation_favorite`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `salesinformation_files`
--
ALTER TABLE `salesinformation_files`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `salesinformation_reference`
--
ALTER TABLE `salesinformation_reference`
  MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `salesinformation_viewed`
--
ALTER TABLE `salesinformation_viewed`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;--
-- Datenbank: `portal_sales`
--
CREATE DATABASE IF NOT EXISTS `portal_sales` DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci;
USE `portal_sales`;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `files`
--

CREATE TABLE `files` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `filename` varchar(255) NOT NULL,
  `filesize` int(11) NOT NULL,
  `type` varchar(30) DEFAULT NULL,
  `language` varchar(6) DEFAULT NULL,
  `crdate` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `salesinformation`
--

CREATE TABLE `salesinformation` (
  `id` int(11) NOT NULL,
  `content` mediumtext NOT NULL,
  `receivers` text NOT NULL,
  `categories` text,
  `draft` tinyint(4) NOT NULL DEFAULT '0',
  `published` tinyint(1) NOT NULL DEFAULT '0',
  `email` tinyint(1) NOT NULL,
  `email_sent` tinyint(1) NOT NULL DEFAULT '0',
  `receiver_emails` text,
  `receiver_emails_read` text,
  `receiver_emails_optout` text,
  `author` varchar(255) NOT NULL,
  `author_email` varchar(255) NOT NULL,
  `author_phone` varchar(255) DEFAULT NULL,
  `author_teamroom_id` int(9) DEFAULT NULL,
  `internal_memo` text,
  `release_date` int(11) DEFAULT NULL,
  `publishdate` int(11) DEFAULT NULL,
  `creator` varchar(255) NOT NULL,
  `crdate` int(11) NOT NULL,
  `editor` varchar(255) DEFAULT NULL,
  `editdate` int(11) DEFAULT NULL,
  `retracted` tinyint(1) DEFAULT '0',
  `retractor` varchar(255) DEFAULT NULL,
  `retractdate` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `salesinformation_favorite`
--

CREATE TABLE `salesinformation_favorite` (
  `id` int(11) NOT NULL,
  `salesinformation_id` int(11) NOT NULL,
  `username` varchar(255) NOT NULL,
  `crdate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `salesinformation_files`
--

CREATE TABLE `salesinformation_files` (
  `id` int(11) NOT NULL,
  `salesinformation_id` int(11) NOT NULL,
  `file_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `salesinformation_optin`
--

CREATE TABLE `salesinformation_optin` (
  `username` varchar(255) CHARACTER SET latin1 NOT NULL,
  `categories` text NOT NULL,
  `crdate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `salesinformation_reference`
--

CREATE TABLE `salesinformation_reference` (
  `id` int(9) NOT NULL,
  `salesinformation_id` int(9) NOT NULL,
  `reference_salesinformation_id` int(9) NOT NULL,
  `replacement` tinyint(1) NOT NULL DEFAULT '0',
  `reason_de` varchar(255) DEFAULT NULL,
  `reason_gb` varchar(255) DEFAULT NULL,
  `reason_fr` varchar(255) DEFAULT NULL,
  `creator` varchar(255) NOT NULL,
  `crdate` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `salesinformation_viewed`
--

CREATE TABLE `salesinformation_viewed` (
  `id` int(11) NOT NULL,
  `salesinformation_id` int(11) NOT NULL,
  `username` varchar(255) NOT NULL,
  `crdate` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Indizes der exportierten Tabellen
--

--
-- Indizes für die Tabelle `files`
--
ALTER TABLE `files`
  ADD PRIMARY KEY (`id`);

--
-- Indizes für die Tabelle `salesinformation`
--
ALTER TABLE `salesinformation`
  ADD PRIMARY KEY (`id`);

--
-- Indizes für die Tabelle `salesinformation_favorite`
--
ALTER TABLE `salesinformation_favorite`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `favorite_unique` (`salesinformation_id`,`username`);

--
-- Indizes für die Tabelle `salesinformation_files`
--
ALTER TABLE `salesinformation_files`
  ADD PRIMARY KEY (`id`);

--
-- Indizes für die Tabelle `salesinformation_optin`
--
ALTER TABLE `salesinformation_optin`
  ADD PRIMARY KEY (`username`);

--
-- Indizes für die Tabelle `salesinformation_reference`
--
ALTER TABLE `salesinformation_reference`
  ADD PRIMARY KEY (`id`);

--
-- Indizes für die Tabelle `salesinformation_viewed`
--
ALTER TABLE `salesinformation_viewed`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT für exportierte Tabellen
--

--
-- AUTO_INCREMENT für Tabelle `files`
--
ALTER TABLE `files`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `salesinformation`
--
ALTER TABLE `salesinformation`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `salesinformation_favorite`
--
ALTER TABLE `salesinformation_favorite`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `salesinformation_files`
--
ALTER TABLE `salesinformation_files`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `salesinformation_reference`
--
ALTER TABLE `salesinformation_reference`
  MODIFY `id` int(9) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `salesinformation_viewed`
--
ALTER TABLE `salesinformation_viewed`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;--

-- Datenbank: `tdata_a`
--
CREATE DATABASE IF NOT EXISTS `tdata_a` DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci;
USE `tdata_a`;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `chain_hoists_with_trolley_combinations`
--

CREATE TABLE `chain_hoists_with_trolley_combinations` (
  `hoist_size` smallint(5) UNSIGNED DEFAULT '0',
  `load_capacity` mediumint(8) UNSIGNED DEFAULT '0',
  `hook_path` mediumint(8) UNSIGNED NOT NULL DEFAULT '0',
  `lifting` decimal(3,1) UNSIGNED DEFAULT '0.0',
  `drive` decimal(4,1) UNSIGNED DEFAULT '0.0',
  `fem_iso` char(2) CHARACTER SET latin1 NOT NULL DEFAULT '',
  `reeving` varchar(7) DEFAULT NULL,
  `operation_voltage` varchar(9) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `crane_drives_combinations`
--

CREATE TABLE `crane_drives_combinations` (
  `type` char(3) CHARACTER SET latin1 NOT NULL,
  `type_of_drive` char(50) CHARACTER SET latin1 NOT NULL,
  `wheel` smallint(5) UNSIGNED NOT NULL,
  `wheel_base` smallint(5) UNSIGNED NOT NULL,
  `drive` tinyint(3) UNSIGNED NOT NULL,
  `spacing` tinyint(3) UNSIGNED DEFAULT NULL,
  `fem_group` char(3) CHARACTER SET latin1 DEFAULT NULL,
  `operation_voltage` varchar(9) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `eot_cranes_combinations`
--

CREATE TABLE `eot_cranes_combinations` (
  `crane_type` varchar(30) CHARACTER SET latin1 DEFAULT '',
  `span` mediumint(9) DEFAULT NULL,
  `load_capacity` mediumint(9) DEFAULT NULL,
  `crane_guiding_system` tinyint(3) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `jib_cranes_combinations`
--

CREATE TABLE `jib_cranes_combinations` (
  `crane_type` varchar(30) CHARACTER SET latin1 DEFAULT '',
  `lifting` varchar(45) DEFAULT NULL,
  `load_capacity` smallint(5) UNSIGNED DEFAULT NULL,
  `drive` varchar(45) DEFAULT NULL,
  `jib_length` mediumint(8) UNSIGNED DEFAULT NULL,
  `slewing` double UNSIGNED DEFAULT NULL,
  `height_of_lower_edge_of_jib` mediumint(8) UNSIGNED DEFAULT NULL,
  `mounting` varchar(100) CHARACTER SET latin1 DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `mobile_gantry_cranes_combinations`
--

CREATE TABLE `mobile_gantry_cranes_combinations` (
  `load_capacity` smallint(5) UNSIGNED DEFAULT NULL,
  `total_width` smallint(5) UNSIGNED DEFAULT NULL,
  `trolley_travel` double UNSIGNED DEFAULT NULL,
  `total_height` smallint(5) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `stationary_chain_hoists_combinations`
--

CREATE TABLE `stationary_chain_hoists_combinations` (
  `hoist_size` smallint(5) UNSIGNED DEFAULT '0',
  `load_capacity` mediumint(8) UNSIGNED DEFAULT '0',
  `hook_path` mediumint(8) UNSIGNED NOT NULL DEFAULT '0',
  `lifting` decimal(3,1) UNSIGNED DEFAULT '0.0',
  `fem_iso` char(2) CHARACTER SET latin1 NOT NULL DEFAULT '',
  `reeving` varchar(7) DEFAULT NULL,
  `operation_voltage` varchar(9) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_chain_hoists_drive`
--

CREATE TABLE `tune_chain_hoists_drive` (
  `value` varchar(6) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_chain_hoists_femiso`
--

CREATE TABLE `tune_chain_hoists_femiso` (
  `value` varchar(6) CHARACTER SET latin1 DEFAULT NULL,
  `key` char(2) CHARACTER SET latin1 NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_chain_hoists_hoist_size`
--

CREATE TABLE `tune_chain_hoists_hoist_size` (
  `value` varchar(5) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_chain_hoists_hw`
--

CREATE TABLE `tune_chain_hoists_hw` (
  `value` mediumint(8) UNSIGNED NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_chain_hoists_lifting`
--

CREATE TABLE `tune_chain_hoists_lifting` (
  `value` decimal(3,1) UNSIGNED DEFAULT '0.0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_chain_hoists_load_capacity`
--

CREATE TABLE `tune_chain_hoists_load_capacity` (
  `value` mediumint(8) UNSIGNED DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_chain_hoists_reeving`
--

CREATE TABLE `tune_chain_hoists_reeving` (
  `value` varchar(7) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_chain_hoists_voltage`
--

CREATE TABLE `tune_chain_hoists_voltage` (
  `value` varchar(9) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_crane_drives_drive`
--

CREATE TABLE `tune_crane_drives_drive` (
  `value` tinyint(3) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_crane_drives_fem_group`
--

CREATE TABLE `tune_crane_drives_fem_group` (
  `value` char(3) CHARACTER SET latin1 DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_crane_drives_spacing`
--

CREATE TABLE `tune_crane_drives_spacing` (
  `value` tinyint(3) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_crane_drives_type`
--

CREATE TABLE `tune_crane_drives_type` (
  `value` char(3) CHARACTER SET latin1 NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_crane_drives_type_of_drive`
--

CREATE TABLE `tune_crane_drives_type_of_drive` (
  `key` char(50) CHARACTER SET latin1 NOT NULL,
  `value` varchar(19) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_crane_drives_voltage`
--

CREATE TABLE `tune_crane_drives_voltage` (
  `value` varchar(9) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_crane_drives_wheel`
--

CREATE TABLE `tune_crane_drives_wheel` (
  `value` smallint(5) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_crane_drives_wheel_base`
--

CREATE TABLE `tune_crane_drives_wheel_base` (
  `value` smallint(5) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_eot_cranes_crane_guiding_system`
--

CREATE TABLE `tune_eot_cranes_crane_guiding_system` (
  `key` tinyint(3) UNSIGNED DEFAULT NULL,
  `value` varchar(19) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_eot_cranes_crane_type`
--

CREATE TABLE `tune_eot_cranes_crane_type` (
  `value` varchar(30) CHARACTER SET latin1 DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_eot_cranes_load_capacity`
--

CREATE TABLE `tune_eot_cranes_load_capacity` (
  `value` mediumint(9) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_eot_cranes_span`
--

CREATE TABLE `tune_eot_cranes_span` (
  `value` mediumint(9) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_eot_cranes_voltage`
--

CREATE TABLE `tune_eot_cranes_voltage` (
  `value` varchar(9) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_jib_cranes_crane_type`
--

CREATE TABLE `tune_jib_cranes_crane_type` (
  `value` varchar(30) CHARACTER SET latin1 DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_jib_cranes_drive`
--

CREATE TABLE `tune_jib_cranes_drive` (
  `value` varchar(45) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_jib_cranes_length`
--

CREATE TABLE `tune_jib_cranes_length` (
  `value` mediumint(8) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_jib_cranes_lifting`
--

CREATE TABLE `tune_jib_cranes_lifting` (
  `value` varchar(45) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_jib_cranes_load_capacity`
--

CREATE TABLE `tune_jib_cranes_load_capacity` (
  `value` smallint(5) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_jib_cranes_mounting`
--

CREATE TABLE `tune_jib_cranes_mounting` (
  `key` varchar(100) CHARACTER SET latin1 DEFAULT NULL,
  `value` varchar(28) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_jib_cranes_slewing`
--

CREATE TABLE `tune_jib_cranes_slewing` (
  `key` double UNSIGNED DEFAULT NULL,
  `value` varchar(10) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_jib_cranes_ukdim`
--

CREATE TABLE `tune_jib_cranes_ukdim` (
  `value` mediumint(8) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_jib_cranes_voltage`
--

CREATE TABLE `tune_jib_cranes_voltage` (
  `value` varchar(9) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_mobile_gantry_cranes_drive`
--

CREATE TABLE `tune_mobile_gantry_cranes_drive` (
  `key` double UNSIGNED DEFAULT NULL,
  `value` varchar(10) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_mobile_gantry_cranes_load_capacity`
--

CREATE TABLE `tune_mobile_gantry_cranes_load_capacity` (
  `value` smallint(5) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_mobile_gantry_cranes_total_height`
--

CREATE TABLE `tune_mobile_gantry_cranes_total_height` (
  `value` smallint(5) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_mobile_gantry_cranes_total_width`
--

CREATE TABLE `tune_mobile_gantry_cranes_total_width` (
  `value` smallint(5) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_mobile_gantry_cranes_voltage`
--

CREATE TABLE `tune_mobile_gantry_cranes_voltage` (
  `value` varchar(9) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_stationary_chain_hoists_femiso`
--

CREATE TABLE `tune_stationary_chain_hoists_femiso` (
  `value` varchar(6) CHARACTER SET latin1 DEFAULT NULL,
  `key` char(2) CHARACTER SET latin1 NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_stationary_chain_hoists_hoist_size`
--

CREATE TABLE `tune_stationary_chain_hoists_hoist_size` (
  `value` varchar(5) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_stationary_chain_hoists_hw`
--

CREATE TABLE `tune_stationary_chain_hoists_hw` (
  `value` mediumint(8) UNSIGNED NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_stationary_chain_hoists_lifting`
--

CREATE TABLE `tune_stationary_chain_hoists_lifting` (
  `value` decimal(3,1) UNSIGNED DEFAULT '0.0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_stationary_chain_hoists_load_capacity`
--

CREATE TABLE `tune_stationary_chain_hoists_load_capacity` (
  `value` mediumint(8) UNSIGNED DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_stationary_chain_hoists_reeving`
--

CREATE TABLE `tune_stationary_chain_hoists_reeving` (
  `value` varchar(7) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_stationary_chain_hoists_voltage`
--

CREATE TABLE `tune_stationary_chain_hoists_voltage` (
  `value` varchar(9) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_wire_rope_hoists_crane_type`
--

CREATE TABLE `tune_wire_rope_hoists_crane_type` (
  `value` char(3) CHARACTER SET latin1 DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_wire_rope_hoists_drive`
--

CREATE TABLE `tune_wire_rope_hoists_drive` (
  `value` varchar(6) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_wire_rope_hoists_femiso`
--

CREATE TABLE `tune_wire_rope_hoists_femiso` (
  `value` varchar(6) CHARACTER SET latin1 DEFAULT NULL,
  `key` char(2) CHARACTER SET latin1 NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_wire_rope_hoists_hoist_size`
--

CREATE TABLE `tune_wire_rope_hoists_hoist_size` (
  `value` smallint(5) UNSIGNED DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_wire_rope_hoists_hw`
--

CREATE TABLE `tune_wire_rope_hoists_hw` (
  `value` mediumint(8) UNSIGNED NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_wire_rope_hoists_lifting`
--

CREATE TABLE `tune_wire_rope_hoists_lifting` (
  `value` decimal(3,1) UNSIGNED DEFAULT '0.0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_wire_rope_hoists_load_capacity`
--

CREATE TABLE `tune_wire_rope_hoists_load_capacity` (
  `value` mediumint(8) UNSIGNED DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_wire_rope_hoists_reeving`
--

CREATE TABLE `tune_wire_rope_hoists_reeving` (
  `value` varchar(7) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_wire_rope_hoists_track`
--

CREATE TABLE `tune_wire_rope_hoists_track` (
  `value` smallint(5) UNSIGNED NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_wire_rope_hoists_voltage`
--

CREATE TABLE `tune_wire_rope_hoists_voltage` (
  `value` varchar(9) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `wire_rope_hoists_combinations`
--

CREATE TABLE `wire_rope_hoists_combinations` (
  `type` char(3) CHARACTER SET latin1 DEFAULT '',
  `hoist_size` smallint(5) UNSIGNED DEFAULT '0',
  `load_capacity` mediumint(8) UNSIGNED DEFAULT '0',
  `hook_path` mediumint(8) UNSIGNED NOT NULL DEFAULT '0',
  `lifting` decimal(3,1) UNSIGNED DEFAULT '0.0',
  `drive` decimal(4,1) UNSIGNED DEFAULT '0.0',
  `fem_iso` char(2) CHARACTER SET latin1 NOT NULL DEFAULT '',
  `operation_voltage` varchar(9) NOT NULL DEFAULT '',
  `reeving` varchar(7) DEFAULT NULL,
  `track` smallint(5) UNSIGNED NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `y_reihenrech_erg_zus1`
--

CREATE TABLE `y_reihenrech_erg_zus1` (
  `LoginID` varchar(12) NOT NULL DEFAULT '',
  `RNr` int(10) UNSIGNED NOT NULL DEFAULT '0',
  `PosNr` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `PosNrAnl` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `lfdNr` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `lfdNrKa` smallint(5) UNSIGNED NOT NULL DEFAULT '1',
  `Dat` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `KrBez` varchar(50) NOT NULL DEFAULT '',
  `KaBez` varchar(50) NOT NULL,
  `Erg` text NOT NULL,
  `SW` mediumint(9) DEFAULT NULL,
  `GL` mediumint(9) DEFAULT NULL,
  `Var` varchar(3) DEFAULT NULL,
  `vKrFKl` decimal(4,1) DEFAULT NULL,
  `vKrFGr` decimal(4,1) DEFAULT NULL,
  `vHubKl` decimal(3,1) DEFAULT NULL,
  `vHubGr` decimal(3,1) DEFAULT NULL,
  `TrbwGr` varchar(3) DEFAULT NULL,
  `HW` mediumint(9) DEFAULT NULL,
  `vKaFKl` decimal(3,1) DEFAULT NULL,
  `vKaFGr` decimal(3,1) DEFAULT NULL,
  `RPr` mediumint(8) UNSIGNED DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=latin1 PACK_KEYS=1;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `y_reihenrech_fwt_erg`
--

CREATE TABLE `y_reihenrech_fwt_erg` (
  `LoginID` varchar(12) NOT NULL DEFAULT '',
  `RNr` int(10) UNSIGNED NOT NULL DEFAULT '0',
  `PosNr` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `PosNrAnl` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `lfdNr` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `Dat` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `Erg` text NOT NULL,
  `U1Nenn` smallint(5) UNSIGNED NOT NULL DEFAULT '400',
  `Freq` tinyint(3) UNSIGNED NOT NULL DEFAULT '50',
  `FWTTyp` char(50) NOT NULL,
  `FWTTypKurz` char(3) NOT NULL,
  `vFWT` tinyint(3) UNSIGNED NOT NULL,
  `FWTAusf` char(50) NOT NULL,
  `DL` smallint(5) UNSIGNED NOT NULL,
  `Gen` tinyint(3) UNSIGNED NOT NULL,
  `HPr` smallint(5) UNSIGNED NOT NULL,
  `RKr` smallint(5) UNSIGNED NOT NULL,
  `HSch` smallint(5) UNSIGNED NOT NULL,
  `FWTBez` char(50) DEFAULT NULL,
  `ATyp` char(1) DEFAULT NULL,
  `KArt` char(1) DEFAULT NULL,
  `VPlatte` tinyint(3) UNSIGNED DEFAULT NULL,
  `KSp` smallint(5) UNSIGNED DEFAULT NULL,
  `TrbwGr` char(3) DEFAULT NULL,
  `FMAbst` tinyint(3) UNSIGNED DEFAULT NULL,
  `AnzAnt` tinyint(3) UNSIGNED DEFAULT NULL,
  `Lst` double UNSIGNED DEFAULT NULL,
  `Rmax` smallint(5) UNSIGNED DEFAULT NULL,
  `SWmax` mediumint(8) UNSIGNED DEFAULT NULL,
  `DP` smallint(5) UNSIGNED DEFAULT NULL,
  `BPRmin` smallint(5) UNSIGNED DEFAULT NULL,
  `BPRmax` smallint(5) UNSIGNED DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=latin1 PACK_KEYS=1;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `y_reihenrech_gen`
--

CREATE TABLE `y_reihenrech_gen` (
  `LoginID` varchar(12) NOT NULL DEFAULT '',
  `RNr` mediumint(8) UNSIGNED NOT NULL DEFAULT '0',
  `PosNr` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `PosNrAnl` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `lfdNr` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `lfdNrKa` smallint(5) UNSIGNED NOT NULL DEFAULT '1',
  `BtrSpng` smallint(5) UNSIGNED NOT NULL DEFAULT '400',
  `Freq` tinyint(3) UNSIGNED NOT NULL DEFAULT '50',
  `KTyp` varchar(30) DEFAULT '',
  `SZSystem` tinyint(3) UNSIGNED DEFAULT NULL,
  `KrFuehrSystem` tinyint(3) UNSIGNED DEFAULT NULL,
  `HWMax_1` mediumint(8) UNSIGNED DEFAULT NULL,
  `BefestB_SK` tinyint(3) UNSIGNED DEFAULT '0',
  `Katzelektrik` tinyint(3) UNSIGNED DEFAULT '0',
  `Erg` text
) ENGINE=MyISAM DEFAULT CHARSET=latin1 PACK_KEYS=1;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `y_reihenrech_lkz_docs`
--

CREATE TABLE `y_reihenrech_lkz_docs` (
  `LoginID` char(12) NOT NULL DEFAULT '',
  `RNr` int(10) UNSIGNED NOT NULL DEFAULT '0',
  `Sprache` char(3) NOT NULL,
  `U1Nenn` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `Freq` tinyint(3) UNSIGNED NOT NULL DEFAULT '0',
  `Bez` char(100) NOT NULL,
  `HW` mediumint(8) UNSIGNED NOT NULL,
  `Spur` smallint(5) UNSIGNED NOT NULL,
  `PDFName` char(100) DEFAULT NULL,
  `ZNRName` char(50) DEFAULT NULL,
  `PDF` tinyint(3) UNSIGNED DEFAULT NULL,
  `DWG` tinyint(3) UNSIGNED DEFAULT NULL,
  `DXF` tinyint(3) UNSIGNED DEFAULT NULL,
  `IPT` tinyint(3) UNSIGNED DEFAULT NULL,
  `SAT` tinyint(3) UNSIGNED DEFAULT NULL,
  `GenDate` date NOT NULL DEFAULT '0000-00-00'
) ENGINE=MyISAM DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `y_reihenrech_lkz_gen_2`
--

CREATE TABLE `y_reihenrech_lkz_gen_2` (
  `LoginID` varchar(12) NOT NULL DEFAULT '',
  `RNr` int(10) UNSIGNED NOT NULL DEFAULT '0',
  `PosNr` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `Ident` varchar(150) NOT NULL DEFAULT '',
  `lfdNr` tinyint(3) UNSIGNED NOT NULL DEFAULT '0',
  `HW` mediumint(8) UNSIGNED NOT NULL DEFAULT '0',
  `KaBez` varchar(100) DEFAULT '',
  `Erg` text NOT NULL,
  `U1Nenn` smallint(5) UNSIGNED NOT NULL DEFAULT '400',
  `Freq` tinyint(3) UNSIGNED NOT NULL DEFAULT '50',
  `BArt` char(3) DEFAULT '',
  `MGr` smallint(5) UNSIGNED DEFAULT '0',
  `Gen` tinyint(3) UNSIGNED DEFAULT NULL,
  `Strng` tinyint(3) UNSIGNED DEFAULT '0',
  `Ablf` tinyint(3) UNSIGNED NOT NULL DEFAULT '0',
  `HubLst` mediumint(8) UNSIGNED DEFAULT '0',
  `vHubKl` decimal(3,1) UNSIGNED DEFAULT '0.0',
  `vHubGr` decimal(3,1) UNSIGNED DEFAULT '0.0',
  `vHubGrNenn` decimal(3,1) UNSIGNED DEFAULT '0.0',
  `MotAusf` char(1) DEFAULT NULL,
  `vKfKl` decimal(4,1) UNSIGNED DEFAULT '0.0',
  `vKfGr` decimal(4,1) UNSIGNED DEFAULT '0.0',
  `vKfGrNenn` decimal(4,1) UNSIGNED DEFAULT '0.0',
  `DL` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `BezHebez` varchar(100) DEFAULT '',
  `TrbwGr` char(3) DEFAULT '',
  `ISO` char(2) NOT NULL DEFAULT '',
  `BPRmin` smallint(5) UNSIGNED DEFAULT '0',
  `BPRmax` smallint(6) DEFAULT '0',
  `Spur` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `LHNr` varchar(10) DEFAULT '',
  `LstP1` double UNSIGNED NOT NULL DEFAULT '0',
  `LstP2` double UNSIGNED NOT NULL DEFAULT '0',
  `ED` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `Schlt` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `LL_W21` smallint(5) DEFAULT '0',
  `LL_2W21` smallint(5) DEFAULT '0',
  `LL_W31` smallint(5) DEFAULT '0',
  `LL_W41` smallint(5) DEFAULT '0',
  `LL_W42` smallint(5) DEFAULT '0',
  `GenDate` date NOT NULL DEFAULT '0000-00-00'
) ENGINE=MyISAM DEFAULT CHARSET=latin1 PACK_KEYS=1;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `y_reihenrech_lkz_pos`
--

CREATE TABLE `y_reihenrech_lkz_pos` (
  `LoginID` char(12) NOT NULL DEFAULT '',
  `RNr` int(10) UNSIGNED NOT NULL DEFAULT '0',
  `PosNr` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `U1Nenn` smallint(5) UNSIGNED NOT NULL DEFAULT '400',
  `Freq` tinyint(3) UNSIGNED NOT NULL DEFAULT '50',
  `BArt` char(3) DEFAULT '',
  `MGr` smallint(5) UNSIGNED DEFAULT '0',
  `Traglast_V` mediumint(8) UNSIGNED DEFAULT '0',
  `Traglast_B` mediumint(8) UNSIGNED DEFAULT '0',
  `Traglast_SW` mediumint(8) UNSIGNED DEFAULT '0',
  `TrbwGr` char(4) DEFAULT NULL,
  `HW` char(10) DEFAULT NULL,
  `vHub` double UNSIGNED DEFAULT NULL,
  `vKf` tinyint(3) UNSIGNED DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=latin1 PACK_KEYS=1;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `y_reihenrech_lkz_titel`
--

CREATE TABLE `y_reihenrech_lkz_titel` (
  `LoginID` char(12) NOT NULL DEFAULT '',
  `RNr` int(10) UNSIGNED NOT NULL DEFAULT '0',
  `Bez` char(100) NOT NULL DEFAULT '',
  `RName` char(10) NOT NULL DEFAULT '',
  `Dat` date NOT NULL DEFAULT '0000-00-00'
) ENGINE=MyISAM DEFAULT CHARSET=latin1 PACK_KEYS=1;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `y_reihenrech_lpk_erg`
--

CREATE TABLE `y_reihenrech_lpk_erg` (
  `LoginID` varchar(12) NOT NULL DEFAULT '',
  `RNr` int(10) UNSIGNED NOT NULL DEFAULT '0',
  `PosNr` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `PosNrAnl` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `lfdNr` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `Dat` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `KrBez` varchar(50) NOT NULL DEFAULT '',
  `KaBez` varchar(50) NOT NULL DEFAULT '',
  `Erg` text NOT NULL,
  `GL` smallint(5) UNSIGNED DEFAULT NULL,
  `GesB` smallint(5) UNSIGNED DEFAULT NULL,
  `GesH` smallint(5) UNSIGNED DEFAULT NULL,
  `vKfKl` double UNSIGNED DEFAULT NULL,
  `vKfGr` double UNSIGNED DEFAULT NULL,
  `vHubKl` double UNSIGNED DEFAULT NULL,
  `vHubGr` double UNSIGNED DEFAULT NULL,
  `GesBi` smallint(5) UNSIGNED DEFAULT NULL,
  `Hoeha` smallint(5) UNSIGNED DEFAULT NULL,
  `SZSys` tinyint(3) UNSIGNED DEFAULT NULL,
  `Preis` double UNSIGNED DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=latin1 PACK_KEYS=1;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `y_reihenrech_sk_erg`
--

CREATE TABLE `y_reihenrech_sk_erg` (
  `LoginID` varchar(12) NOT NULL DEFAULT '',
  `RNr` int(10) UNSIGNED NOT NULL DEFAULT '0',
  `PosNr` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `PosNrAnl` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `lfdNr` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `Dat` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `KrBez` varchar(50) NOT NULL DEFAULT '',
  `KaBez` varchar(50) NOT NULL DEFAULT '',
  `Erg` text NOT NULL,
  `GL` smallint(5) UNSIGNED DEFAULT NULL,
  `AL` mediumint(8) UNSIGNED DEFAULT NULL,
  `UKMass` mediumint(8) UNSIGNED DEFAULT NULL,
  `BauHoe` smallint(5) UNSIGNED DEFAULT NULL,
  `Rmax` smallint(5) UNSIGNED DEFAULT NULL,
  `vKfKl` double UNSIGNED DEFAULT NULL,
  `vKfGr` double UNSIGNED DEFAULT NULL,
  `vSchwKl` double UNSIGNED DEFAULT NULL,
  `vSchwGr` double UNSIGNED DEFAULT NULL,
  `vHubKl` double UNSIGNED DEFAULT NULL,
  `vHubGr` double UNSIGNED DEFAULT NULL,
  `Hoeha` smallint(5) UNSIGNED DEFAULT NULL,
  `CHMass` double UNSIGNED DEFAULT NULL,
  `CKMass` double UNSIGNED DEFAULT NULL,
  `AbstA1` double UNSIGNED DEFAULT NULL,
  `AbstA0` double UNSIGNED DEFAULT NULL,
  `Befest` varchar(100) DEFAULT NULL,
  `BefestB` tinyint(3) UNSIGNED DEFAULT '0',
  `RechPr` int(10) UNSIGNED DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=latin1 PACK_KEYS=1;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `y_reihenrech_titel`
--

CREATE TABLE `y_reihenrech_titel` (
  `LoginID` char(12) NOT NULL DEFAULT '',
  `RNr` int(10) UNSIGNED NOT NULL DEFAULT '0',
  `Bez` char(100) NOT NULL DEFAULT '',
  `PTyp` char(30) NOT NULL DEFAULT '',
  `Dat` date NOT NULL DEFAULT '0000-00-00'
) ENGINE=MyISAM DEFAULT CHARSET=latin1 PACK_KEYS=1;

--
-- Indizes der exportierten Tabellen
--

--
-- Indizes für die Tabelle `y_reihenrech_erg_zus1`
--
ALTER TABLE `y_reihenrech_erg_zus1`
  ADD PRIMARY KEY (`LoginID`,`RNr`,`PosNr`,`PosNrAnl`,`lfdNr`,`lfdNrKa`);

--
-- Indizes für die Tabelle `y_reihenrech_fwt_erg`
--
ALTER TABLE `y_reihenrech_fwt_erg`
  ADD PRIMARY KEY (`LoginID`,`RNr`,`PosNr`,`PosNrAnl`,`lfdNr`,`Dat`,`FWTTyp`,`vFWT`,`FWTAusf`,`DL`,`Gen`,`HPr`,`RKr`,`HSch`);

--
-- Indizes für die Tabelle `y_reihenrech_gen`
--
ALTER TABLE `y_reihenrech_gen`
  ADD PRIMARY KEY (`LoginID`,`RNr`,`PosNr`,`PosNrAnl`,`lfdNr`,`lfdNrKa`);

--
-- Indizes für die Tabelle `y_reihenrech_lkz_docs`
--
ALTER TABLE `y_reihenrech_lkz_docs`
  ADD PRIMARY KEY (`LoginID`,`RNr`,`Sprache`,`U1Nenn`,`Freq`,`Bez`,`HW`,`Spur`);

--
-- Indizes für die Tabelle `y_reihenrech_lkz_gen_2`
--
ALTER TABLE `y_reihenrech_lkz_gen_2`
  ADD PRIMARY KEY (`LoginID`,`RNr`,`PosNr`,`Ident`,`lfdNr`,`HW`),
  ADD KEY `INDEX_2` (`LoginID`,`RNr`,`PosNr`,`lfdNr`),
  ADD KEY `RNr` (`RNr`,`PosNr`,`lfdNr`,`HW`,`U1Nenn`,`Freq`,`BArt`,`MGr`,`Strng`,`Ablf`,`HubLst`,`vHubGrNenn`,`vKfGrNenn`,`TrbwGr`,`Spur`),
  ADD KEY `LoginID_2` (`LoginID`,`RNr`,`PosNr`,`U1Nenn`,`Freq`,`BArt`,`vKfGrNenn`,`BezHebez`,`Spur`),
  ADD KEY `LoginID` (`LoginID`,`RNr`,`PosNr`,`BArt`,`BezHebez`,`Spur`);

--
-- Indizes für die Tabelle `y_reihenrech_lkz_pos`
--
ALTER TABLE `y_reihenrech_lkz_pos`
  ADD PRIMARY KEY (`LoginID`,`RNr`,`PosNr`);

--
-- Indizes für die Tabelle `y_reihenrech_lkz_titel`
--
ALTER TABLE `y_reihenrech_lkz_titel`
  ADD PRIMARY KEY (`LoginID`,`RNr`);

--
-- Indizes für die Tabelle `y_reihenrech_lpk_erg`
--
ALTER TABLE `y_reihenrech_lpk_erg`
  ADD PRIMARY KEY (`LoginID`,`RNr`,`PosNr`,`PosNrAnl`,`Dat`);

--
-- Indizes für die Tabelle `y_reihenrech_sk_erg`
--
ALTER TABLE `y_reihenrech_sk_erg`
  ADD PRIMARY KEY (`LoginID`,`RNr`,`PosNr`,`PosNrAnl`,`Dat`);

--
-- Indizes für die Tabelle `y_reihenrech_titel`
--
ALTER TABLE `y_reihenrech_titel`
  ADD PRIMARY KEY (`LoginID`,`RNr`);
--
-- Datenbank: `tdata_b`
--
CREATE DATABASE IF NOT EXISTS `tdata_b` DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci;
USE `tdata_b`;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `chain_hoists_with_trolley_combinations`
--

CREATE TABLE `chain_hoists_with_trolley_combinations` (
  `hoist_size` smallint(5) UNSIGNED DEFAULT '0',
  `load_capacity` mediumint(8) UNSIGNED DEFAULT '0',
  `hook_path` mediumint(8) UNSIGNED NOT NULL DEFAULT '0',
  `lifting` decimal(3,1) UNSIGNED DEFAULT '0.0',
  `drive` decimal(4,1) UNSIGNED DEFAULT '0.0',
  `fem_iso` char(2) CHARACTER SET latin1 NOT NULL DEFAULT '',
  `reeving` varchar(7) DEFAULT NULL,
  `operation_voltage` varchar(9) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `crane_drives_combinations`
--

CREATE TABLE `crane_drives_combinations` (
  `type` char(3) CHARACTER SET latin1 NOT NULL,
  `type_of_drive` char(50) CHARACTER SET latin1 NOT NULL,
  `wheel` smallint(5) UNSIGNED NOT NULL,
  `wheel_base` smallint(5) UNSIGNED NOT NULL,
  `drive` tinyint(3) UNSIGNED NOT NULL,
  `spacing` tinyint(3) UNSIGNED DEFAULT NULL,
  `fem_group` char(3) CHARACTER SET latin1 DEFAULT NULL,
  `operation_voltage` varchar(9) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `eot_cranes_combinations`
--

CREATE TABLE `eot_cranes_combinations` (
  `crane_type` varchar(30) CHARACTER SET latin1 DEFAULT '',
  `span` mediumint(9) DEFAULT NULL,
  `load_capacity` mediumint(9) DEFAULT NULL,
  `crane_guiding_system` tinyint(3) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `jib_cranes_combinations`
--

CREATE TABLE `jib_cranes_combinations` (
  `crane_type` varchar(30) CHARACTER SET latin1 DEFAULT '',
  `lifting` varchar(45) DEFAULT NULL,
  `load_capacity` smallint(5) UNSIGNED DEFAULT NULL,
  `drive` varchar(45) DEFAULT NULL,
  `jib_length` mediumint(8) UNSIGNED DEFAULT NULL,
  `slewing` double UNSIGNED DEFAULT NULL,
  `height_of_lower_edge_of_jib` mediumint(8) UNSIGNED DEFAULT NULL,
  `mounting` varchar(100) CHARACTER SET latin1 DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `mobile_gantry_cranes_combinations`
--

CREATE TABLE `mobile_gantry_cranes_combinations` (
  `load_capacity` smallint(5) UNSIGNED DEFAULT NULL,
  `total_width` smallint(5) UNSIGNED DEFAULT NULL,
  `trolley_travel` double UNSIGNED DEFAULT NULL,
  `total_height` smallint(5) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `stationary_chain_hoists_combinations`
--

CREATE TABLE `stationary_chain_hoists_combinations` (
  `hoist_size` smallint(5) UNSIGNED DEFAULT '0',
  `load_capacity` mediumint(8) UNSIGNED DEFAULT '0',
  `hook_path` mediumint(8) UNSIGNED NOT NULL DEFAULT '0',
  `lifting` decimal(3,1) UNSIGNED DEFAULT '0.0',
  `fem_iso` char(2) CHARACTER SET latin1 NOT NULL DEFAULT '',
  `reeving` varchar(7) DEFAULT NULL,
  `operation_voltage` varchar(9) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_chain_hoists_drive`
--

CREATE TABLE `tune_chain_hoists_drive` (
  `value` varchar(6) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_chain_hoists_femiso`
--

CREATE TABLE `tune_chain_hoists_femiso` (
  `value` varchar(6) CHARACTER SET latin1 DEFAULT NULL,
  `key` char(2) CHARACTER SET latin1 NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_chain_hoists_hoist_size`
--

CREATE TABLE `tune_chain_hoists_hoist_size` (
  `value` varchar(5) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_chain_hoists_hw`
--

CREATE TABLE `tune_chain_hoists_hw` (
  `value` mediumint(8) UNSIGNED NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_chain_hoists_lifting`
--

CREATE TABLE `tune_chain_hoists_lifting` (
  `value` decimal(3,1) UNSIGNED DEFAULT '0.0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_chain_hoists_load_capacity`
--

CREATE TABLE `tune_chain_hoists_load_capacity` (
  `value` mediumint(8) UNSIGNED DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_chain_hoists_reeving`
--

CREATE TABLE `tune_chain_hoists_reeving` (
  `value` varchar(7) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_chain_hoists_voltage`
--

CREATE TABLE `tune_chain_hoists_voltage` (
  `value` varchar(9) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_crane_drives_drive`
--

CREATE TABLE `tune_crane_drives_drive` (
  `value` tinyint(3) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_crane_drives_fem_group`
--

CREATE TABLE `tune_crane_drives_fem_group` (
  `value` char(3) CHARACTER SET latin1 DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_crane_drives_spacing`
--

CREATE TABLE `tune_crane_drives_spacing` (
  `value` tinyint(3) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_crane_drives_type`
--

CREATE TABLE `tune_crane_drives_type` (
  `value` char(3) CHARACTER SET latin1 NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_crane_drives_type_of_drive`
--

CREATE TABLE `tune_crane_drives_type_of_drive` (
  `key` char(50) CHARACTER SET latin1 NOT NULL,
  `value` varchar(19) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_crane_drives_voltage`
--

CREATE TABLE `tune_crane_drives_voltage` (
  `value` varchar(9) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_crane_drives_wheel`
--

CREATE TABLE `tune_crane_drives_wheel` (
  `value` smallint(5) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_crane_drives_wheel_base`
--

CREATE TABLE `tune_crane_drives_wheel_base` (
  `value` smallint(5) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_eot_cranes_crane_guiding_system`
--

CREATE TABLE `tune_eot_cranes_crane_guiding_system` (
  `key` tinyint(3) UNSIGNED DEFAULT NULL,
  `value` varchar(19) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_eot_cranes_crane_type`
--

CREATE TABLE `tune_eot_cranes_crane_type` (
  `value` varchar(30) CHARACTER SET latin1 DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_eot_cranes_load_capacity`
--

CREATE TABLE `tune_eot_cranes_load_capacity` (
  `value` mediumint(9) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_eot_cranes_span`
--

CREATE TABLE `tune_eot_cranes_span` (
  `value` mediumint(9) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_eot_cranes_voltage`
--

CREATE TABLE `tune_eot_cranes_voltage` (
  `value` varchar(9) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_jib_cranes_crane_type`
--

CREATE TABLE `tune_jib_cranes_crane_type` (
  `value` varchar(30) CHARACTER SET latin1 DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_jib_cranes_drive`
--

CREATE TABLE `tune_jib_cranes_drive` (
  `value` varchar(45) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_jib_cranes_length`
--

CREATE TABLE `tune_jib_cranes_length` (
  `value` mediumint(8) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_jib_cranes_lifting`
--

CREATE TABLE `tune_jib_cranes_lifting` (
  `value` varchar(45) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_jib_cranes_load_capacity`
--

CREATE TABLE `tune_jib_cranes_load_capacity` (
  `value` smallint(5) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_jib_cranes_mounting`
--

CREATE TABLE `tune_jib_cranes_mounting` (
  `key` varchar(100) CHARACTER SET latin1 DEFAULT NULL,
  `value` varchar(28) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_jib_cranes_slewing`
--

CREATE TABLE `tune_jib_cranes_slewing` (
  `key` double UNSIGNED DEFAULT NULL,
  `value` varchar(10) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_jib_cranes_ukdim`
--

CREATE TABLE `tune_jib_cranes_ukdim` (
  `value` mediumint(8) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_jib_cranes_voltage`
--

CREATE TABLE `tune_jib_cranes_voltage` (
  `value` varchar(9) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_mobile_gantry_cranes_drive`
--

CREATE TABLE `tune_mobile_gantry_cranes_drive` (
  `key` double UNSIGNED DEFAULT NULL,
  `value` varchar(10) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_mobile_gantry_cranes_load_capacity`
--

CREATE TABLE `tune_mobile_gantry_cranes_load_capacity` (
  `value` smallint(5) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_mobile_gantry_cranes_total_height`
--

CREATE TABLE `tune_mobile_gantry_cranes_total_height` (
  `value` smallint(5) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_mobile_gantry_cranes_total_width`
--

CREATE TABLE `tune_mobile_gantry_cranes_total_width` (
  `value` smallint(5) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_mobile_gantry_cranes_voltage`
--

CREATE TABLE `tune_mobile_gantry_cranes_voltage` (
  `value` varchar(9) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_stationary_chain_hoists_femiso`
--

CREATE TABLE `tune_stationary_chain_hoists_femiso` (
  `value` varchar(6) CHARACTER SET latin1 DEFAULT NULL,
  `key` char(2) CHARACTER SET latin1 NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_stationary_chain_hoists_hoist_size`
--

CREATE TABLE `tune_stationary_chain_hoists_hoist_size` (
  `value` varchar(5) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_stationary_chain_hoists_hw`
--

CREATE TABLE `tune_stationary_chain_hoists_hw` (
  `value` mediumint(8) UNSIGNED NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_stationary_chain_hoists_lifting`
--

CREATE TABLE `tune_stationary_chain_hoists_lifting` (
  `value` decimal(3,1) UNSIGNED DEFAULT '0.0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_stationary_chain_hoists_load_capacity`
--

CREATE TABLE `tune_stationary_chain_hoists_load_capacity` (
  `value` mediumint(8) UNSIGNED DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_stationary_chain_hoists_reeving`
--

CREATE TABLE `tune_stationary_chain_hoists_reeving` (
  `value` varchar(7) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_stationary_chain_hoists_voltage`
--

CREATE TABLE `tune_stationary_chain_hoists_voltage` (
  `value` varchar(9) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_wire_rope_hoists_crane_type`
--

CREATE TABLE `tune_wire_rope_hoists_crane_type` (
  `value` char(3) CHARACTER SET latin1 DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_wire_rope_hoists_drive`
--

CREATE TABLE `tune_wire_rope_hoists_drive` (
  `value` varchar(6) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_wire_rope_hoists_femiso`
--

CREATE TABLE `tune_wire_rope_hoists_femiso` (
  `value` varchar(6) CHARACTER SET latin1 DEFAULT NULL,
  `key` char(2) CHARACTER SET latin1 NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_wire_rope_hoists_hoist_size`
--

CREATE TABLE `tune_wire_rope_hoists_hoist_size` (
  `value` smallint(5) UNSIGNED DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_wire_rope_hoists_hw`
--

CREATE TABLE `tune_wire_rope_hoists_hw` (
  `value` mediumint(8) UNSIGNED NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_wire_rope_hoists_lifting`
--

CREATE TABLE `tune_wire_rope_hoists_lifting` (
  `value` decimal(3,1) UNSIGNED DEFAULT '0.0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_wire_rope_hoists_load_capacity`
--

CREATE TABLE `tune_wire_rope_hoists_load_capacity` (
  `value` mediumint(8) UNSIGNED DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_wire_rope_hoists_reeving`
--

CREATE TABLE `tune_wire_rope_hoists_reeving` (
  `value` varchar(7) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_wire_rope_hoists_track`
--

CREATE TABLE `tune_wire_rope_hoists_track` (
  `value` smallint(5) UNSIGNED NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `tune_wire_rope_hoists_voltage`
--

CREATE TABLE `tune_wire_rope_hoists_voltage` (
  `value` varchar(9) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `wire_rope_hoists_combinations`
--

CREATE TABLE `wire_rope_hoists_combinations` (
  `type` char(3) CHARACTER SET latin1 DEFAULT '',
  `hoist_size` smallint(5) UNSIGNED DEFAULT '0',
  `load_capacity` mediumint(8) UNSIGNED DEFAULT '0',
  `hook_path` mediumint(8) UNSIGNED NOT NULL DEFAULT '0',
  `lifting` decimal(3,1) UNSIGNED DEFAULT '0.0',
  `drive` decimal(4,1) UNSIGNED DEFAULT '0.0',
  `fem_iso` char(2) CHARACTER SET latin1 NOT NULL DEFAULT '',
  `operation_voltage` varchar(9) NOT NULL DEFAULT '',
  `reeving` varchar(7) DEFAULT NULL,
  `track` smallint(5) UNSIGNED NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `y_reihenrech_erg_zus1`
--

CREATE TABLE `y_reihenrech_erg_zus1` (
  `LoginID` varchar(12) NOT NULL DEFAULT '',
  `RNr` int(10) UNSIGNED NOT NULL DEFAULT '0',
  `PosNr` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `PosNrAnl` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `lfdNr` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `lfdNrKa` smallint(5) UNSIGNED NOT NULL DEFAULT '1',
  `Dat` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `KrBez` varchar(50) NOT NULL DEFAULT '',
  `KaBez` varchar(50) NOT NULL,
  `Erg` text NOT NULL,
  `SW` mediumint(9) DEFAULT NULL,
  `GL` mediumint(9) DEFAULT NULL,
  `Var` varchar(3) DEFAULT NULL,
  `vKrFKl` decimal(4,1) DEFAULT NULL,
  `vKrFGr` decimal(4,1) DEFAULT NULL,
  `vHubKl` decimal(3,1) DEFAULT NULL,
  `vHubGr` decimal(3,1) DEFAULT NULL,
  `HW` mediumint(9) DEFAULT NULL,
  `vKaFKl` decimal(3,1) DEFAULT NULL,
  `vKaFGr` decimal(3,1) DEFAULT NULL,
  `RPr` mediumint(8) UNSIGNED DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=latin1 ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `y_reihenrech_fwt_erg`
--

CREATE TABLE `y_reihenrech_fwt_erg` (
  `LoginID` varchar(12) NOT NULL DEFAULT '',
  `RNr` int(10) UNSIGNED NOT NULL DEFAULT '0',
  `PosNr` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `PosNrAnl` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `lfdNr` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `Dat` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `Erg` text NOT NULL,
  `U1Nenn` smallint(5) UNSIGNED NOT NULL DEFAULT '400',
  `Freq` tinyint(3) UNSIGNED NOT NULL DEFAULT '50',
  `FWTTyp` char(50) NOT NULL,
  `FWTTypKurz` char(3) NOT NULL,
  `vFWT` tinyint(3) UNSIGNED NOT NULL,
  `FWTAusf` char(50) NOT NULL,
  `DL` smallint(5) UNSIGNED NOT NULL,
  `Gen` tinyint(3) UNSIGNED NOT NULL,
  `HPr` smallint(5) UNSIGNED NOT NULL,
  `RKr` smallint(5) UNSIGNED NOT NULL,
  `HSch` smallint(5) UNSIGNED NOT NULL,
  `FWTBez` char(50) DEFAULT NULL,
  `ATyp` char(1) DEFAULT NULL,
  `KArt` char(1) DEFAULT NULL,
  `VPlatte` tinyint(3) UNSIGNED DEFAULT NULL,
  `KSp` smallint(5) UNSIGNED DEFAULT NULL,
  `TrbwGr` char(3) DEFAULT NULL,
  `FMAbst` tinyint(3) UNSIGNED DEFAULT NULL,
  `AnzAnt` tinyint(3) UNSIGNED DEFAULT NULL,
  `Lst` double UNSIGNED DEFAULT NULL,
  `Rmax` smallint(5) UNSIGNED DEFAULT NULL,
  `SWmax` mediumint(8) UNSIGNED DEFAULT NULL,
  `DP` smallint(5) UNSIGNED DEFAULT NULL,
  `BPRmin` smallint(5) UNSIGNED DEFAULT NULL,
  `BPRmax` smallint(5) UNSIGNED DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=latin1 PACK_KEYS=1;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `y_reihenrech_gen`
--

CREATE TABLE `y_reihenrech_gen` (
  `LoginID` varchar(12) NOT NULL DEFAULT '',
  `RNr` mediumint(8) UNSIGNED NOT NULL DEFAULT '0',
  `PosNr` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `PosNrAnl` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `lfdNr` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `lfdNrKa` smallint(5) UNSIGNED NOT NULL DEFAULT '1',
  `BtrSpng` smallint(5) UNSIGNED NOT NULL DEFAULT '400',
  `Freq` tinyint(3) UNSIGNED NOT NULL DEFAULT '50',
  `KTyp` varchar(30) DEFAULT '',
  `SZSystem` tinyint(3) UNSIGNED DEFAULT NULL,
  `KrFuehrSystem` tinyint(3) UNSIGNED DEFAULT NULL,
  `HWMax_1` mediumint(8) UNSIGNED DEFAULT NULL,
  `BefestB_SK` tinyint(3) UNSIGNED DEFAULT '0',
  `Katzelektrik` tinyint(3) UNSIGNED DEFAULT '0',
  `Erg` text
) ENGINE=MyISAM DEFAULT CHARSET=latin1 PACK_KEYS=1;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `y_reihenrech_lkz_docs`
--

CREATE TABLE `y_reihenrech_lkz_docs` (
  `LoginID` char(12) NOT NULL DEFAULT '',
  `RNr` int(10) UNSIGNED NOT NULL DEFAULT '0',
  `Sprache` char(3) NOT NULL,
  `U1Nenn` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `Freq` tinyint(3) UNSIGNED NOT NULL DEFAULT '0',
  `Bez` char(100) NOT NULL,
  `HW` mediumint(8) UNSIGNED NOT NULL,
  `Spur` smallint(5) UNSIGNED NOT NULL,
  `PDFName` char(100) DEFAULT NULL,
  `ZNRName` char(50) DEFAULT NULL,
  `PDF` tinyint(3) UNSIGNED DEFAULT NULL,
  `DWG` tinyint(3) UNSIGNED DEFAULT NULL,
  `DXF` tinyint(3) UNSIGNED DEFAULT NULL,
  `IPT` tinyint(3) UNSIGNED DEFAULT NULL,
  `SAT` tinyint(3) UNSIGNED DEFAULT NULL,
  `GenDate` date NOT NULL DEFAULT '0000-00-00'
) ENGINE=MyISAM DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `y_reihenrech_lkz_gen_2`
--

CREATE TABLE `y_reihenrech_lkz_gen_2` (
  `LoginID` varchar(12) NOT NULL DEFAULT '',
  `RNr` int(10) UNSIGNED NOT NULL DEFAULT '0',
  `PosNr` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `Ident` varchar(150) NOT NULL DEFAULT '',
  `lfdNr` tinyint(3) UNSIGNED NOT NULL DEFAULT '0',
  `HW` mediumint(8) UNSIGNED NOT NULL DEFAULT '0',
  `KaBez` varchar(100) DEFAULT '',
  `Erg` text NOT NULL,
  `U1Nenn` smallint(5) UNSIGNED NOT NULL DEFAULT '400',
  `Freq` tinyint(3) UNSIGNED NOT NULL DEFAULT '50',
  `BArt` char(3) DEFAULT '',
  `MGr` smallint(5) UNSIGNED DEFAULT '0',
  `Gen` tinyint(3) UNSIGNED DEFAULT NULL,
  `Strng` tinyint(3) UNSIGNED DEFAULT '0',
  `Ablf` tinyint(3) UNSIGNED NOT NULL DEFAULT '0',
  `HubLst` mediumint(8) UNSIGNED DEFAULT '0',
  `vHubKl` decimal(3,1) UNSIGNED DEFAULT '0.0',
  `vHubGr` decimal(3,1) UNSIGNED DEFAULT '0.0',
  `vHubGrNenn` decimal(3,1) UNSIGNED DEFAULT '0.0',
  `MotAusf` char(1) DEFAULT NULL,
  `vKfKl` decimal(4,1) UNSIGNED DEFAULT '0.0',
  `vKfGr` decimal(4,1) UNSIGNED DEFAULT '0.0',
  `vKfGrNenn` decimal(4,1) UNSIGNED DEFAULT '0.0',
  `DL` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `BezHebez` varchar(100) DEFAULT '',
  `TrbwGr` char(3) DEFAULT '',
  `ISO` char(2) NOT NULL DEFAULT '',
  `BPRmin` smallint(5) UNSIGNED DEFAULT '0',
  `BPRmax` smallint(6) DEFAULT '0',
  `Spur` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `LHNr` varchar(10) DEFAULT '',
  `LstP1` double UNSIGNED NOT NULL DEFAULT '0',
  `LstP2` double UNSIGNED NOT NULL DEFAULT '0',
  `ED` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `Schlt` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `GenDate` date NOT NULL DEFAULT '0000-00-00'
) ENGINE=MyISAM DEFAULT CHARSET=latin1 PACK_KEYS=1;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `y_reihenrech_lkz_pos`
--

CREATE TABLE `y_reihenrech_lkz_pos` (
  `LoginID` char(12) NOT NULL DEFAULT '',
  `RNr` int(10) UNSIGNED NOT NULL DEFAULT '0',
  `PosNr` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `U1Nenn` smallint(5) UNSIGNED NOT NULL DEFAULT '400',
  `Freq` tinyint(3) UNSIGNED NOT NULL DEFAULT '50',
  `BArt` char(3) DEFAULT '',
  `MGr` smallint(5) UNSIGNED DEFAULT '0',
  `Traglast_V` mediumint(8) UNSIGNED DEFAULT '0',
  `Traglast_B` mediumint(8) UNSIGNED DEFAULT '0',
  `Traglast_SW` mediumint(8) UNSIGNED DEFAULT '0',
  `TrbwGr` char(4) DEFAULT NULL,
  `HW` char(10) DEFAULT NULL,
  `vHub` double UNSIGNED DEFAULT NULL,
  `vKf` tinyint(3) UNSIGNED DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=latin1 PACK_KEYS=1;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `y_reihenrech_lkz_titel`
--

CREATE TABLE `y_reihenrech_lkz_titel` (
  `LoginID` char(12) NOT NULL DEFAULT '',
  `RNr` int(10) UNSIGNED NOT NULL DEFAULT '0',
  `Bez` char(100) NOT NULL DEFAULT '',
  `RName` char(10) NOT NULL DEFAULT '',
  `Dat` date NOT NULL DEFAULT '0000-00-00'
) ENGINE=MyISAM DEFAULT CHARSET=latin1 PACK_KEYS=1;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `y_reihenrech_lpk_erg`
--

CREATE TABLE `y_reihenrech_lpk_erg` (
  `LoginID` varchar(12) NOT NULL DEFAULT '',
  `RNr` int(10) UNSIGNED NOT NULL DEFAULT '0',
  `PosNr` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `PosNrAnl` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `lfdNr` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `Dat` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `KrBez` varchar(50) NOT NULL DEFAULT '',
  `KaBez` varchar(50) NOT NULL DEFAULT '',
  `Erg` text NOT NULL,
  `GL` smallint(5) UNSIGNED DEFAULT NULL,
  `GesB` smallint(5) UNSIGNED DEFAULT NULL,
  `GesH` smallint(5) UNSIGNED DEFAULT NULL,
  `vKfKl` double UNSIGNED DEFAULT NULL,
  `vKfGr` double UNSIGNED DEFAULT NULL,
  `vHubKl` double UNSIGNED DEFAULT NULL,
  `vHubGr` double UNSIGNED DEFAULT NULL,
  `GesBi` smallint(5) UNSIGNED DEFAULT NULL,
  `Hoeha` smallint(5) UNSIGNED DEFAULT NULL,
  `SZSys` tinyint(3) UNSIGNED DEFAULT NULL,
  `Preis` double UNSIGNED DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=latin1 PACK_KEYS=1;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `y_reihenrech_sk_erg`
--

CREATE TABLE `y_reihenrech_sk_erg` (
  `LoginID` varchar(12) NOT NULL DEFAULT '',
  `RNr` int(10) UNSIGNED NOT NULL DEFAULT '0',
  `PosNr` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `PosNrAnl` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `lfdNr` smallint(5) UNSIGNED NOT NULL DEFAULT '0',
  `Dat` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `KrBez` varchar(50) NOT NULL DEFAULT '',
  `KaBez` varchar(50) NOT NULL DEFAULT '',
  `Erg` text NOT NULL,
  `GL` smallint(5) UNSIGNED DEFAULT NULL,
  `AL` mediumint(8) UNSIGNED DEFAULT NULL,
  `UKMass` mediumint(8) UNSIGNED DEFAULT NULL,
  `BauHoe` smallint(5) UNSIGNED DEFAULT NULL,
  `Rmax` smallint(5) UNSIGNED DEFAULT NULL,
  `vKfKl` double UNSIGNED DEFAULT NULL,
  `vKfGr` double UNSIGNED DEFAULT NULL,
  `vSchwKl` double UNSIGNED DEFAULT NULL,
  `vSchwGr` double UNSIGNED DEFAULT NULL,
  `vHubKl` double UNSIGNED DEFAULT NULL,
  `vHubGr` double UNSIGNED DEFAULT NULL,
  `Hoeha` smallint(5) UNSIGNED DEFAULT NULL,
  `CHMass` double UNSIGNED DEFAULT NULL,
  `CKMass` double UNSIGNED DEFAULT NULL,
  `AbstA1` double UNSIGNED DEFAULT NULL,
  `AbstA0` double UNSIGNED DEFAULT NULL,
  `Befest` varchar(100) DEFAULT NULL,
  `RechPr` int(10) UNSIGNED DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=latin1 PACK_KEYS=1;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `y_reihenrech_titel`
--

CREATE TABLE `y_reihenrech_titel` (
  `LoginID` char(12) NOT NULL DEFAULT '',
  `RNr` int(10) UNSIGNED NOT NULL DEFAULT '0',
  `Bez` char(100) NOT NULL DEFAULT '',
  `PTyp` char(30) NOT NULL DEFAULT '',
  `Dat` date NOT NULL DEFAULT '0000-00-00'
) ENGINE=MyISAM DEFAULT CHARSET=latin1 PACK_KEYS=1;

--
-- Indizes der exportierten Tabellen
--

--
-- Indizes für die Tabelle `y_reihenrech_erg_zus1`
--
ALTER TABLE `y_reihenrech_erg_zus1`
  ADD PRIMARY KEY (`LoginID`,`RNr`,`PosNr`,`PosNrAnl`,`lfdNr`,`lfdNrKa`);

--
-- Indizes für die Tabelle `y_reihenrech_fwt_erg`
--
ALTER TABLE `y_reihenrech_fwt_erg`
  ADD PRIMARY KEY (`LoginID`,`RNr`,`PosNr`,`PosNrAnl`,`lfdNr`,`Dat`,`FWTTyp`,`vFWT`,`FWTAusf`,`DL`,`Gen`,`HPr`,`RKr`,`HSch`);

--
-- Indizes für die Tabelle `y_reihenrech_gen`
--
ALTER TABLE `y_reihenrech_gen`
  ADD PRIMARY KEY (`LoginID`,`RNr`,`PosNr`,`PosNrAnl`,`lfdNr`,`lfdNrKa`);

--
-- Indizes für die Tabelle `y_reihenrech_lkz_docs`
--
ALTER TABLE `y_reihenrech_lkz_docs`
  ADD PRIMARY KEY (`LoginID`,`RNr`,`Sprache`,`U1Nenn`,`Freq`,`Bez`,`HW`,`Spur`);

--
-- Indizes für die Tabelle `y_reihenrech_lkz_gen_2`
--
ALTER TABLE `y_reihenrech_lkz_gen_2`
  ADD PRIMARY KEY (`LoginID`,`RNr`,`PosNr`,`Ident`,`lfdNr`,`HW`),
  ADD KEY `INDEX_2` (`LoginID`,`RNr`,`PosNr`,`lfdNr`),
  ADD KEY `RNr` (`RNr`,`PosNr`,`lfdNr`,`HW`,`U1Nenn`,`Freq`,`BArt`,`MGr`,`Strng`,`Ablf`,`HubLst`,`vHubGrNenn`,`vKfGrNenn`,`TrbwGr`,`Spur`),
  ADD KEY `LoginID_2` (`LoginID`,`RNr`,`PosNr`,`U1Nenn`,`Freq`,`BArt`,`vKfGrNenn`,`BezHebez`,`Spur`),
  ADD KEY `LoginID` (`LoginID`,`RNr`,`PosNr`,`BArt`,`BezHebez`,`Spur`);

--
-- Indizes für die Tabelle `y_reihenrech_lkz_pos`
--
ALTER TABLE `y_reihenrech_lkz_pos`
  ADD PRIMARY KEY (`LoginID`,`RNr`,`PosNr`);

--
-- Indizes für die Tabelle `y_reihenrech_lkz_titel`
--
ALTER TABLE `y_reihenrech_lkz_titel`
  ADD PRIMARY KEY (`LoginID`,`RNr`);

--
-- Indizes für die Tabelle `y_reihenrech_lpk_erg`
--
ALTER TABLE `y_reihenrech_lpk_erg`
  ADD PRIMARY KEY (`LoginID`,`RNr`,`PosNr`,`PosNrAnl`,`Dat`);

--
-- Indizes für die Tabelle `y_reihenrech_sk_erg`
--
ALTER TABLE `y_reihenrech_sk_erg`
  ADD PRIMARY KEY (`LoginID`,`RNr`,`PosNr`,`PosNrAnl`,`Dat`);

--
-- Indizes für die Tabelle `y_reihenrech_titel`
--
ALTER TABLE `y_reihenrech_titel`
  ADD PRIMARY KEY (`LoginID`,`RNr`);
--
-- Datenbank: `teams`
--
CREATE DATABASE IF NOT EXISTS `teams` DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci;
USE `teams`;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `acx_categories`
--

CREATE TABLE `acx_categories` (
  `id` int(10) UNSIGNED NOT NULL,
  `type` varchar(50) NOT NULL DEFAULT 'Category',
  `parent_type` varchar(50) DEFAULT NULL,
  `parent_id` int(10) UNSIGNED DEFAULT NULL,
  `name` varchar(100) DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  `created_by_id` int(10) UNSIGNED DEFAULT NULL,
  `created_by_name` varchar(100) DEFAULT NULL,
  `created_by_email` varchar(150) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `acx_companies`
--

CREATE TABLE `acx_companies` (
  `id` int(10) UNSIGNED NOT NULL,
  `state` tinyint(3) UNSIGNED NOT NULL DEFAULT '0',
  `original_state` tinyint(3) UNSIGNED DEFAULT NULL,
  `name` varchar(100) DEFAULT NULL,
  `note` varchar(255) DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  `created_by_id` int(10) UNSIGNED DEFAULT NULL,
  `created_by_name` varchar(100) DEFAULT NULL,
  `created_by_email` varchar(150) DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  `updated_by_id` int(10) UNSIGNED DEFAULT NULL,
  `updated_by_name` varchar(100) DEFAULT NULL,
  `updated_by_email` varchar(150) DEFAULT NULL,
  `is_owner` tinyint(1) UNSIGNED NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `acx_project_users`
--

CREATE TABLE `acx_project_users` (
  `user_id` int(5) UNSIGNED NOT NULL DEFAULT '0',
  `project_id` int(10) UNSIGNED NOT NULL DEFAULT '0',
  `permissions` text,
  `role_id` int(3) UNSIGNED NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `acx_projects`
--

CREATE TABLE `acx_projects` (
  `id` int(10) UNSIGNED NOT NULL,
  `slug` varchar(50) DEFAULT NULL,
  `template_id` int(10) UNSIGNED DEFAULT NULL,
  `based_on_type` varchar(50) DEFAULT NULL,
  `based_on_id` int(10) UNSIGNED DEFAULT NULL,
  `company_id` int(5) UNSIGNED NOT NULL DEFAULT '0',
  `category_id` int(10) UNSIGNED DEFAULT NULL,
  `label_id` int(5) UNSIGNED DEFAULT NULL,
  `currency_id` int(5) UNSIGNED DEFAULT NULL,
  `budget` decimal(13,3) UNSIGNED NOT NULL DEFAULT '0.000',
  `state` tinyint(3) UNSIGNED NOT NULL DEFAULT '0',
  `original_state` tinyint(3) UNSIGNED DEFAULT NULL,
  `name` varchar(150) DEFAULT NULL,
  `leader_id` int(10) UNSIGNED DEFAULT NULL,
  `leader_name` varchar(100) DEFAULT NULL,
  `leader_email` varchar(150) DEFAULT NULL,
  `overview` text,
  `default_visibility` tinyint(1) UNSIGNED NOT NULL DEFAULT '0',
  `completed_on` datetime DEFAULT NULL,
  `completed_by_id` int(10) UNSIGNED DEFAULT NULL,
  `completed_by_name` varchar(100) DEFAULT NULL,
  `completed_by_email` varchar(150) DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  `created_by_id` int(10) UNSIGNED DEFAULT NULL,
  `created_by_name` varchar(100) DEFAULT NULL,
  `created_by_email` varchar(150) DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  `updated_by_id` int(10) UNSIGNED DEFAULT NULL,
  `updated_by_name` varchar(100) DEFAULT NULL,
  `updated_by_email` varchar(150) DEFAULT NULL,
  `custom_field_1` varchar(255) DEFAULT NULL,
  `custom_field_2` varchar(255) DEFAULT NULL,
  `custom_field_3` varchar(255) DEFAULT NULL,
  `mail_to_project_code` varchar(10) NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `acx_users`
--

CREATE TABLE `acx_users` (
  `id` int(10) UNSIGNED NOT NULL,
  `type` varchar(50) NOT NULL DEFAULT 'Client',
  `state` tinyint(3) UNSIGNED NOT NULL DEFAULT '0',
  `original_state` tinyint(3) UNSIGNED DEFAULT NULL,
  `first_name` varchar(50) DEFAULT NULL,
  `last_name` varchar(50) DEFAULT NULL,
  `email` varchar(150) NOT NULL,
  `password` varchar(255) NOT NULL DEFAULT '',
  `password_hashed_with` enum('pbkdf2','sha1') NOT NULL DEFAULT 'pbkdf2',
  `password_expires_on` date DEFAULT NULL,
  `password_reset_key` varchar(20) DEFAULT NULL,
  `password_reset_on` datetime DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  `created_by_id` int(10) UNSIGNED DEFAULT NULL,
  `created_by_name` varchar(100) DEFAULT NULL,
  `created_by_email` varchar(150) DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  `updated_by_id` int(10) UNSIGNED DEFAULT NULL,
  `updated_by_name` varchar(100) DEFAULT NULL,
  `updated_by_email` varchar(150) DEFAULT NULL,
  `invited_on` datetime DEFAULT NULL,
  `last_login_on` datetime DEFAULT NULL,
  `last_visit_on` datetime DEFAULT NULL,
  `last_activity_on` datetime DEFAULT NULL,
  `raw_additional_properties` longtext,
  `company_id` int(5) UNSIGNED NOT NULL DEFAULT '0',
  `auto_assign` tinyint(1) UNSIGNED NOT NULL DEFAULT '0',
  `auto_assign_role_id` int(3) UNSIGNED DEFAULT NULL,
  `auto_assign_permissions` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Indizes der exportierten Tabellen
--

--
-- Indizes für die Tabelle `acx_categories`
--
ALTER TABLE `acx_categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`parent_type`,`parent_id`,`type`,`name`),
  ADD KEY `type` (`type`),
  ADD KEY `parent` (`parent_type`,`parent_id`);

--
-- Indizes für die Tabelle `acx_companies`
--
ALTER TABLE `acx_companies`
  ADD PRIMARY KEY (`id`),
  ADD KEY `name` (`name`),
  ADD KEY `state` (`state`);

--
-- Indizes für die Tabelle `acx_project_users`
--
ALTER TABLE `acx_project_users`
  ADD PRIMARY KEY (`user_id`,`project_id`);

--
-- Indizes für die Tabelle `acx_projects`
--
ALTER TABLE `acx_projects`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `mail_to_project_code` (`mail_to_project_code`),
  ADD UNIQUE KEY `slug` (`slug`),
  ADD KEY `leader_id` (`leader_id`),
  ADD KEY `completed_on` (`completed_on`),
  ADD KEY `created_on` (`created_on`),
  ADD KEY `company_id` (`company_id`),
  ADD KEY `category_id` (`category_id`),
  ADD KEY `label_id` (`label_id`);

--
-- Indizes für die Tabelle `acx_users`
--
ALTER TABLE `acx_users`
  ADD PRIMARY KEY (`id`),
  ADD KEY `last_activity_on` (`last_activity_on`),
  ADD KEY `company_id` (`company_id`),
  ADD KEY `email` (`email`),
  ADD KEY `type` (`type`),
  ADD KEY `state` (`state`);

--
-- AUTO_INCREMENT für exportierte Tabellen
--

--
-- AUTO_INCREMENT für Tabelle `acx_categories`
--
ALTER TABLE `acx_categories`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `acx_companies`
--
ALTER TABLE `acx_companies`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `acx_projects`
--
ALTER TABLE `acx_projects`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT für Tabelle `acx_users`
--
ALTER TABLE `acx_users`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
