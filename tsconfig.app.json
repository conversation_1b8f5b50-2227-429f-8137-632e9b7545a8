{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["env.d.ts", "src/**/*", "src/**/*.vue"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "paths": {"Assets*": ["assets*"], "@assets/*": ["assets/*"], "EventBuses*": ["assets/protected/vue/eventBuses*"], "Flags*": ["node_modules/svg-country-flags*"], "@flags/*": ["node_modules/svg-country-flags/*"], "@translations/*": ["translations/*"]}}}