<template>
  <div class="store-test">
    <h3>Pinia Store Migration Test</h3>
    
    <!-- Modal Store Test -->
    <div class="test-section">
      <h4>Modal Store Test</h4>
      <button @click="testModal" class="btn btn-primary">Test Modal</button>
      <p>Modal Show: {{ modalStore.getShow }}</p>
    </div>

    <!-- Box Store Test -->
    <div class="test-section">
      <h4>Box Store Test</h4>
      <button @click="testBox" class="btn btn-secondary">Toggle Download Mode</button>
      <p>Download Mode: {{ boxStore.getDownloadMode }}</p>
      <p>Download Count: {{ boxStore.getSelectedForDownloadCount }}</p>
    </div>

    <!-- Services Store Test -->
    <div class="test-section">
      <h4>Services Store Test</h4>
      <button @click="testServices" class="btn btn-info">Set Company</button>
      <p>Company: {{ servicesStore.getCompany.name }} ({{ servicesStore.getCompany.number }})</p>
    </div>

    <!-- News Store Test -->
    <div class="test-section">
      <h4>News Store Test</h4>
      <button @click="testNews" class="btn btn-warning">Test News</button>
      <p>Draft: {{ newsStore.getDraft }}</p>
      <p>Loaded: {{ newsStore.getIsLoaded }}</p>
    </div>

    <!-- Bookings Store Test -->
    <div class="test-section">
      <h4>Bookings Store Test</h4>
      <button @click="testBookings" class="btn btn-success">Test Bookings</button>
      <p>Show Table: {{ bookingsStore.getShowTable }}</p>
      <p>Loading: {{ bookingsStore.getLoading }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  useModalStore, 
  useBoxStore, 
  useServicesStore, 
  useNewsStore, 
  useBookingsStore 
} from '@assets/protected/vue/stores';

const modalStore = useModalStore();
const boxStore = useBoxStore();
const servicesStore = useServicesStore();
const newsStore = useNewsStore();
const bookingsStore = useBookingsStore();

const testModal = () => {
  modalStore.showModal({
    cssClass: 'info',
    title: 'Test Modal',
    message: 'This is a test modal from Pinia store!',
  });
};

const testBox = () => {
  boxStore.toggleDownloadMode();
};

const testServices = () => {
  servicesStore.setCompany({
    name: 'Test Company',
    number: '12345',
  });
};

const testNews = () => {
  newsStore.setDraft(!newsStore.getDraft);
};

const testBookings = () => {
  bookingsStore.setShowTable(!bookingsStore.getShowTable);
  bookingsStore.setLoading(!bookingsStore.getLoading);
};
</script>

<style scoped>
.store-test {
  padding: 20px;
}

.test-section {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.test-section h4 {
  margin-top: 0;
}

.btn {
  margin-right: 10px;
}
</style>
