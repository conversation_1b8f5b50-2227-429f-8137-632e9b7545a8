<template>
  <div v-cloak>
    <panel panel-class="panel-default profile">
      <template v-slot:title>
        <i class="fas fa-sliders-h mr5" aria-hidden="true"></i>
        {{ $t('frame/settings/settings') }}
      </template>
      <template v-if="loaded" v-slot:body>
        <div class="row mb10">
          <label class="col-md-4 control-label">
            {{ $t('frame/settings/mailLocale') }}
            <i
              class="fa fa-question-circle"
              data-toggle="tooltip"
              data-placement="top"
              aria-hidden="true"
              :title="$t('frame/settings/mailLocaleHelp')"></i>
          </label>
          <div class="col-md-8">
            <select v-model="settings.mailLocale" class="form-control">
              <option value=""></option>
              <option value="en_GB" v-html="$t('language/english')"></option>
              <option value="de_DE" v-html="$t('language/german')"></option>
              <option value="fr_FR" v-html="$t('language/french')"></option>
              <option value="es_ES" v-html="$t('language/spanish')"></option>
            </select>
          </div>
        </div>
      </template>
    </panel>

    <panel v-if="showAbukonfis" panel-class="panel-default profile">
      <template v-slot:title>
        <i class="fas fa-sliders-h mr5" aria-hidden="true"></i>
        {{ $t('frame/settings/abukonfis') }}
      </template>
      <template v-if="loaded" v-slot:body>
        <div class="row mb10">
          <label class="col-md-4 control-label">
            {{ $t('frame/settings/abukonfisOutputLanguage') }}
            <i
              class="fa fa-question-circle"
              data-toggle="tooltip"
              data-placement="top"
              aria-hidden="true"
              :title="$t('frame/settings/abukonfisOutputLanguageHelp')"></i>
          </label>
          <div class="col-md-8">
            <select v-model="settings.abukonfisOutputLanguage" class="form-control">
              <option value=""></option>
              <option v-for="option in abukonfisOutputLanguageOptions" :key="option.value" :value="option.value">
                {{ option.name }}
              </option>
            </select>
          </div>
        </div>

        <div class="row mb10">
          <label class="col-md-4 control-label">
            {{ $t('frame/settings/abukonfisBuiltInCountry') }}
            <i
              class="fa fa-question-circle"
              data-toggle="tooltip"
              data-placement="top"
              aria-hidden="true"
              :title="$t('frame/settings/abukonfisBuiltInCountryHelp')"></i>
          </label>
          <div class="col-md-8">
            <select v-model="settings.abukonfisBuiltInCountry" class="form-control">
              <option value=""></option>
              <option v-for="option in abukonfisBuiltInCountryOptions" :key="option.value" :value="option.value">
                {{ option.name }}
              </option>
            </select>
          </div>
        </div>
      </template>
    </panel>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, getCurrentInstance } from 'vue';
import Axios from 'axios';

import Panel from '@assets/protected/vue/component/helper/Card.vue';

import { useModalStore } from '@assets/protected/vue/stores';
import { useToast } from '@assets/protected/vue/composables/useToast';

const props = defineProps<{ abukonfis?: string }>();
const showAbukonfis = computed(() => props.abukonfis === '1');

const { proxy } = getCurrentInstance()!;
const t = proxy.$t;
const toast = useToast();
const modalStore = useModalStore();

const loaded = ref<boolean>(false);
const settings = reactive({
  mailLocale: '',
  abukonfisOutputLanguage: '',
  abukonfisBuiltInCountry: '',
});

const abukonfisOutputLanguageOptions = ref<object[]>([
  { value: 49, name: t('language/german') },
  { value: 44, name: t('language/english') },
  { value: 33, name: t('language/french') },
  { value: 34, name: t('language/spanish') },
  { value: 31, name: t('language/dutch') },
  { value: 45, name: t('language/danish') },
  { value: 46, name: t('language/swedish') },
  { value: 420, name: t('language/czech') },
  { value: 55, name: t('language/brazilian') },
  { value: 358, name: t('language/finnish') },
  { value: 48, name: t('language/polish') },
  { value: 47, name: t('language/norwegian') },
  { value: 30, name: t('language/greek') },
  { value: 39, name: t('language/italian') },
]);

const abukonfisBuiltInCountryOptions = ref<object[]>([
  { value: 1, name: t('country/germany') },
  { value: 2, name: t('country/sweden') },
  { value: 3, name: t('country/switzerland') },
  { value: 4, name: t('country/france') },
  { value: 5, name: t('country/united_states') },
  { value: 6, name: t('country/australia') },
  { value: 7, name: t('country/belgium') },
  { value: 8, name: t('country/taiwan') },
  { value: 9, name: t('country/austria') },
  { value: 10, name: t('country/netherlands') },
  { value: 11, name: t('country/canada') },
  { value: 12, name: t('country/denmark') },
  { value: 13, name: t('country/great_britain') },
  { value: 14, name: t('country/finland') },
  { value: 15, name: t('country/greece') },
  { value: 16, name: t('country/ireland') },
  { value: 17, name: t('country/italy') },
  { value: 18, name: t('country/luxembourg') },
  { value: 19, name: t('country/portugal') },
  { value: 20, name: t('country/spain') },
  { value: 21, name: t('country/egypt') },
  { value: 22, name: t('country/argentina') },
  { value: 23, name: t('country/brazil') },
  { value: 24, name: t('country/chile') },
  { value: 25, name: t('country/china') },
  { value: 26, name: t('country/guatemala') },
  { value: 27, name: t('country/hong_kong') },
  { value: 28, name: t('country/indonesia') },
  { value: 29, name: t('country/iran') },
  { value: 30, name: t('country/israel') },
  { value: 31, name: t('country/japan') },
  { value: 32, name: t('country/colombia') },
  { value: 33, name: t('country/malaysia') },
  { value: 34, name: t('country/norway') },
  { value: 35, name: t('country/philippines') },
  { value: 36, name: t('country/poland') },
  { value: 37, name: t('country/saudi_arabia') },
  { value: 38, name: t('country/singapore') },
  { value: 39, name: t('country/slovakia') },
  { value: 40, name: t('country/slovenia') },
  { value: 41, name: t('country/thailand') },
  { value: 42, name: t('country/czech_republic') },
  { value: 43, name: t('country/turkey') },
  { value: 44, name: t('country/hungary') },
  { value: 45, name: t('country/united_arab_emirates') },
  { value: 46, name: t('country/venezuela') },
  { value: 47, name: t('country/iceland') },
  { value: 48, name: t('country/jordan') },
  { value: 49, name: t('country/korea,_republic_of_(south_korea)') },
  { value: 50, name: t('country/kuwait') },
  { value: 51, name: t('country/malta') },
  { value: 52, name: t('country/mexico') },
  { value: 53, name: t('country/new_zealand') },
  { value: 54, name: t('country/oman') },
  { value: 55, name: t('country/vietnam') },
  { value: 56, name: t('country/cyprus') },
  { value: 57, name: t('country/bahrain') },
  { value: 58, name: t('country/bolivia') },
  { value: 59, name: t('country/dominican_republic') },
  { value: 60, name: t('country/ecuador') },
  { value: 61, name: t('country/french_polynesia') },
  { value: 62, name: t('country/bosnia_and_herzegovina') },
  { value: 63, name: t('country/yemen') },
  { value: 64, name: t('country/qatar') },
  { value: 65, name: t('country/kenya') },
  { value: 66, name: t('country/croatia') },
  { value: 67, name: t('country/latvia') },
  { value: 68, name: t('country/lithuania') },
  { value: 69, name: t('country/morocco') },
  { value: 70, name: t('country/nigeria') },
  { value: 71, name: t('country/ireland') },
  { value: 72, name: t('country/panama') },
  { value: 73, name: t('country/peru') },
  { value: 74, name: t('country/romania') },
  { value: 75, name: t('country/russia') },
  { value: 76, name: t('country/sudan') },
  { value: 77, name: t('country/tanzania') },
  { value: 78, name: t('country/tunisia') },
  { value: 79, name: t('country/ukraine') },
  { value: 80, name: t('country/estonia') },
  { value: 81, name: t('country/south_africa') },
  { value: 82, name: t('country/honduras') },
  { value: 83, name: t('country/costa_rica') },
  { value: 84, name: t('country/uruguay') },
  { value: 85, name: t('country/india') },
  { value: 86, name: t('country/myanmar') },
  { value: 87, name: t('country/nicaragua') },
  { value: 88, name: t('country/el_salvador') },
]);

watch(settings, () => {
  if (loaded.value) {
    saveSettings();
  }
});

const loadSettings = (): void => {
  Axios.get('/mySettings')
    .then((response) => {
      settings.abukonfisBuiltInCountry = response.data.abukonfisBuiltInCountry;
      settings.abukonfisOutputLanguage = response.data.abukonfisOutputLanguage;
      settings.mailLocale = response.data.mailLocale;
    })
    .catch((error) => {
      modalStore.showModal({ cssClass: 'error', message: error.response.data });
    })
    .finally(() => (loaded.value = true));
};

const saveSettings = (): void => {
  const formData = new URLSearchParams();
  Object.entries(settings).forEach(([key, value]) => formData.append(`settings[${key}]`, value ?? ''));

  Axios.post('/mySettings/save', formData, { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } })
    .then(() => {
      toast.success({
        title: t('frame/settings/saved'),
        body: t('frame/setingssuccessfullySaved'),
      });
    })
    .catch((error) => {
      modalStore.showModal({ cssClass: 'error', message: error.response.data });
    });
};

onMounted(() => {
  loadSettings();

  // Sortierung der Optionen
  abukonfisBuiltInCountryOptions.value.sort((a, b) => a.name.localeCompare(b.name));
  abukonfisOutputLanguageOptions.value.sort((a, b) => a.name.localeCompare(b.name));
});
</script>

<style scoped>
label {
  line-height: 30px;
}
</style>
