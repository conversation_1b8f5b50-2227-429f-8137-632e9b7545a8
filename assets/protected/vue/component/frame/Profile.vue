<template>
  <div>
    <panel panel-class="panel-default profile">
      <template v-slot:title>
        <i class="fa fa-user mr5" aria-hidden="true"></i>
        {{ $t('frame/profile/profile_details') }}
      </template>
      <template v-if="loaded" v-slot:body>
        <div class="row">
          <div class="col-md-10">
            <div class="profile-name pb10">
              <h3 class="mt0" v-html="profile.firstName + ' ' + profile.lastName"></h3>
              <p class="job-title mb0">
                <i class="fa fa-building"></i>
                {{ profile.company }}
              </p>
            </div>
          </div>
          <div class="col-md-2"></div>
        </div>

        <div class="contact-info bt">
          <div class="row">
            <div class="col-md-6">
              <dl class="mt20">
                <dt class="text-muted">{{ $t('frame/profile/profile_street') }}</dt>
                <dd v-html="profile.street"></dd>
                <dt class="text-muted">{{ $t('frame/profile/profile_postcode') }}</dt>
                <dd v-html="profile.postcode"></dd>
                <dt class="text-muted">{{ $t('frame/profile/profile_town') }}</dt>
                <dd v-html="profile.town"></dd>
                <dt class="text-muted">{{ $t('frame/profile/profile_country') }}</dt>
                <dd v-html="profile.country"></dd>
              </dl>
            </div>

            <div class="col-md-6">
              <dl class="mt20">
                <dt class="text-muted">{{ $t('frame/profile/profile_email') }}</dt>
                <dd v-html="profile.mail"></dd>
                <dt class="text-muted">{{ $t('frame/profile/profile_phone') }}</dt>
                <dd v-html="profile.telephone"></dd>
                <dt class="text-muted">{{ $t('frame/profile/profile_mobile') }}</dt>
                <dd v-html="profile.mobile"></dd>
                <dt class="text-muted">{{ $t('frame/profile/profile_fax') }}</dt>
                <dd v-html="profile.fax"></dd>
              </dl>
            </div>
          </div>
        </div>
      </template>
    </panel>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import axios from 'axios';

import { useModalStore } from '@assets/protected/vue/stores';
import Panel from '@assets/protected/vue/component/helper/Card.vue';

interface ProfileData {
  firstName: string;
  lastName: string;
  company: string;
  mail: string;
  telephone: string;
  mobile: string;
  fax: string;
  street: string;
  town: string;
  postcode: string;
  country: string;
}

const loaded = ref<boolean>(false);
const profile = ref<ProfileData>({} as ProfileData);

const modalStore = useModalStore();

const loadProfile = (): void => {
  axios
    .get<ProfileData>('/myProfile')
    .then((response) => {
      profile.value = response.data;
      loaded.value = true;
    })
    .catch((error: any) => {
      modalStore.showModal({ cssClass: 'error', message: error.response.data });
    });
};

onMounted(loadProfile);
</script>

<style lang="scss" scoped>
.profile {
  .contact-info {
    dd {
      margin-bottom: 10px;
    }
  }
}
</style>
