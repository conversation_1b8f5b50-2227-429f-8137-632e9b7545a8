<template>
  <div v-cloak>
    <div v-if="settings.mailLocale === ''" class="bs-callout bs-callout-danger">
      <h4>
        <i class="fa fa-exclamation-circle" aria-hidden="true"></i>
        {{ $t('frame/dashboard/settings/email_headline') }}
      </h4>
      <p v-html="$t('frame/dashboard/settings/email_text')"></p>
    </div>

    <div
      v-if="abukonfis && (settings.abukonfisOutputLanguage === '' || settings.abukonfisBuiltInCountry === '')"
      class="bs-callout bs-callout-danger">
      <h4>
        <i class="fa fa-exclamation-circle" aria-hidden="true"></i>
        {{ $t('frame/dashboard/settings/abukonfis_headline') }}
      </h4>
      <p v-html="$t('frame/dashboard/settings/abukonfis_text')"></p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps } from 'vue';
import axios from 'axios';
import { useModalStore } from '@assets/protected/vue/stores';

defineProps<{
  abukonfis?: boolean;
}>();

const settings = ref<Record<string, any>>({});
const modalStore = useModalStore();

onMounted(async () => {
  try {
    const { data } = await axios.get('/mySettings');
    settings.value = data;
  } catch (error: any) {
    modalStore.showModal({
      cssClass: 'error',
      message: error?.response?.data || 'Unknown error',
    });
  }
});
</script>
