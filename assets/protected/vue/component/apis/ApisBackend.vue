<template>
  <div>
    <div id="index">
      <Card card-class="bg-danger text-white mt20">
        <template v-slot:title>{{ t('apis/admin/drafts') }}</template>
        <template v-slot:body>
          <div
            v-if="information.draft === null || information.draft.length === 0"
            v-html="t('apis/nothingFound')"></div>
          <ApisInformation
            v-for="informationBuilder in information.draft"
            :key="informationBuilder.information.number"
            :information-builder="informationBuilder"
            :locale="locale"
            :backend="true" />
          <Spinner v-show="busy.draft" size="small" :message="t('apis/loading')" />
        </template>
      </Card>

      <Card card-class="bg-warning text-white mt20">
        <template v-slot:title>{{ t('apis/admin/waitingfortranslation') }}</template>
        <template v-slot:body>
          <div
            v-if="information.wait_for_translation === null || information.wait_for_translation.length === 0"
            v-html="t('apis/nothingFound')"></div>
          <ApisInformation
            v-for="informationBuilder in information.wait_for_translation"
            :key="informationBuilder.information.number"
            :information-builder="informationBuilder"
            :locale="locale"
            :backend="true" />
          <Spinner v-show="busy.wait_for_translation" size="small" :message="t('apis/loading')" />
        </template>
      </Card>

      <Card card-class="bg-success text-white mt20">
        <template v-slot:title>{{ t('apis/admin/waitingforpublishing') }}</template>
        <template v-slot:body>
          <div
            v-if="information.wait_for_publishing === null || information.wait_for_publishing.length === 0"
            v-html="t('apis/nothingFound')"></div>
          <ApisInformation
            v-for="informationBuilder in information.wait_for_publishing"
            :key="informationBuilder.information.number"
            :information-builder="informationBuilder"
            :locale="locale"
            :backend="true" />
          <spinner v-show="busy.wait_for_publishing" size="small" :message="t('apis/loading')" />
        </template>
      </Card>

      <Card card-class="bg-info text-white mt20">
        <template v-slot:title>{{ t('apis/admin/waitingforrelease') }}</template>
        <template v-slot:body>
          <div
            v-if="information.wait_for_release === null || information.wait_for_release.length === 0"
            v-html="t('apis/nothingFound')"></div>
          <ApisInformation
            v-for="informationBuilder in information.wait_for_release"
            :key="informationBuilder.information.number"
            :information-builder="informationBuilder"
            :locale="locale"
            :backend="true" />
          <Spinner v-show="busy.wait_for_release" size="small" :message="t('apis/loading')" />
        </template>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, getCurrentInstance } from 'vue';
import axios from 'axios';

import Card from '@assets/protected/vue/component/helper/Card.vue';
import ApisInformation from '@assets/protected/vue/component/apis/ApisInformation.vue';
import Spinner from '@assets/protected/vue/component/helper/Spinner.vue';

import { useModalStore, useApisStore } from '@assets/protected/vue/stores';

const props = defineProps<{
  section: string;
  categories?: object;
  possibleReceivers?: string[];
}>();

const { proxy } = getCurrentInstance()!;
const t = proxy.$t;
const locale = proxy.$i18n.locale;

const modalStore = useModalStore();
const apisStore = useApisStore();

interface InformationState {
  [key: string]: any[] | null;
  wait_for_publishing: any[] | null;
  wait_for_translation: any[] | null;
  wait_for_release: any[] | null;
  draft: any[] | null;
}

interface BusyState {
  [key: string]: boolean;
  wait_for_publishing: boolean;
  wait_for_translation: boolean;
  wait_for_release: boolean;
  draft: boolean;
}

const information = ref<InformationState>({
  wait_for_publishing: null,
  wait_for_translation: null,
  wait_for_release: null,
  draft: null,
});

const busy = ref<BusyState>({
  wait_for_publishing: true,
  wait_for_translation: true,
  wait_for_release: true,
  draft: true,
});

const load = async (state: keyof InformationState) => {
  try {
    busy.value[state] = true;
    const { data } = await axios.get(`/apisadmin/load/${props.section}/${state}`);
    information.value[state] = data;
  } catch (error) {
    modalStore.showModal({
      cssClass: 'error',
      message: error.response?.data || t('errors.generic'),
    });
  } finally {
    busy.value[state] = false;
  }
};

onMounted(() => {
  apisStore.setSidebar({
    section: props.section,
    showIndexButton: true,
    showBackendIndexButton: false,
    showNewInformationButton: true,
  });
  (['draft', 'wait_for_translation', 'wait_for_publishing', 'wait_for_release'] as const).forEach(load);
});
</script>
