<template>
  <div id="modal">
    <b-modal
      ref="modal"
      class="primary"
      size="lg"
      centered
      no-fade
      no-close-on-backdrop
      :hide-header-close="true"
      :cancel-title="$t('apis/cancel')"
      :title="$t('apis/settings')"
      @ok="storeSettings">
      <p v-html="$t('apis/settingsDescription')"></p>

      <div class="row mt0 mb0 ml0">
        <div v-for="(category, key, index) in categories" class="col-md-6">
          <div class="custom-control custom-checkbox">
            <input
              :id="'setting' + index"
              v-model="settings[key]"
              class="custom-control-input"
              type="checkbox"
              value="true"
              :value="category" />
            <label class="custom-control-label" :for="'setting' + index"
              ><span class="label label-default" v-html="category"></span
            ></label>
          </div>
        </div>
      </div>

      <template v-slot:modal-ok>
        <div v-html="$t('apis/save')"></div>
      </template>
    </b-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import axios from 'axios';
import { useModalStore, useApisStore } from '@assets/protected/vue/stores';

const modalStore = useModalStore();
const apisStore = useApisStore();

const section = ref<string>('');
const categories = ref<string[]>([]);
const settings = ref<string[]>([]);
const modal = ref();

const loadSettings = (): void => {
  axios
    .get(`/apis/settings/${section.value}`)
    .then((response: any) => {
      settings.value = response.data;
      modal.value.show();
    })
    .catch((error: any) => {
      modalStore.showModal({ cssClass: 'error', message: error.response.data });
    });
};

const storeSettings = (): void => {
  const params = new URLSearchParams();
  for (const category in settings.value) {
    params.append(`settings[${category}]`, String(settings.value[category]));
  }

  axios
    .post(`/apis/settings/${section.value}`, params, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    })
    .then((response: any) => {
      // Alles Ok, nichts machen
    })
    .catch((error: any) => {
      modalStore.showModal({ cssClass: 'error', message: error.response.data });
    });
};

// Watch for settings events
watch(
  () => apisStore.getSettingsTrigger,
  (data) => {
    if (data) {
      section.value = data.section;
      categories.value = data.categories;
      loadSettings();
      apisStore.resetSettingsTrigger();
    }
  },
);
</script>

<style lang="scss" scoped>
div.checkbox-custom label {
  padding-left: 15px;
}
</style>
