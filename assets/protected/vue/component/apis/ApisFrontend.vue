<template>
  <div
    v-cloak
    id="apisFrontend"
    v-infinite-scroll="loadInformationPaged"
    infinite-scroll-disabled="busy"
    infinite-scroll-distance="100">
    <div v-if="nothingFound && !busy" class="bs-callout bs-callout-danger">
      <h4 v-html="t('apis/nothingFound')"></h4>
      <p v-html="t('apis/nothingFoundDescription')"></p>
    </div>

    <ApisInformation
      v-for="informationBuilder in information"
      :key="informationBuilder.information.number"
      :information-builder="informationBuilder"
      :locale="locale"
      :categoryFilter="filter"
      :fullView="fullView">
    </ApisInformation>
    <Spinner v-show="busy" size="big" :message="t('apis/loading')" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, getCurrentInstance, watch } from 'vue';
import axios from 'axios';

import { useModalStore, useApisStore } from '@assets/protected/vue/stores';

const { proxy } = getCurrentInstance()!;
const t = proxy.$t;
const locale = proxy.$i18n.locale;

const modalStore = useModalStore();
const apisStore = useApisStore();

interface Props {
  section: string;
  single?: number | null;
}

const props = withDefaults(defineProps<Props>(), {
  single: null,
});

const page = ref(0);
const information = ref<any[]>([]);
const busy = ref(false);
const everythingLoaded = ref(false);
const query = ref('');
const fromDate = ref('');
const untilDate = ref('');
const filter = ref<any>({});
const favorites = ref(false);
const nothingFound = ref(false);
const fullView = ref(false);

// Wieviele Informationen sollen pro AJAX Call geladen werden?
const informationPerLoad = 20;

const loadInformationPaged = () => {
  // Es gibt nichts mehr nachzuladen
  if (everythingLoaded.value === true) return;

  busy.value = true;
  page.value += 1;

  const url = `/apis/load/${props.section}/${informationPerLoad}/${page.value}`;

  const params = new URLSearchParams({
    query: query.value,
    fromDate: fromDate.value,
    untilDate: untilDate.value,
    filter: JSON.stringify(filter.value),
    favories: JSON.stringify(favorites.value),
  });

  axios
    .post(url, params, { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } })
    .then((response: any) => {
      const data: Array<object> = response.data;

      // Nichts gefunden
      nothingFound.value = data.length === 0;

      // Komplett geladen
      if (data.length < informationPerLoad) {
        everythingLoaded.value = true;
      }

      information.value.push(...data);
      busy.value = false;
    })
    .catch((error: any) => {
      busy.value = false;
      everythingLoaded.value = true;
      modalStore.showModal({ cssClass: 'error', message: error.response.data });
    });
};

const reset = () => {
  everythingLoaded.value = false;
  page.value = 0;
  information.value = [];
};

/**
 * Store Watcher für Events
 */
watch(
  () => apisStore.getSearchTrigger,
  (data) => {
    if (data) {
      query.value = data.query;
      fromDate.value = data.fromDate;
      untilDate.value = data.untilDate;
      filter.value = data.filter;
      reset(); // Bisherige Anzeige leeren
      loadInformationPaged();
      apisStore.resetSearchTrigger();
    }
  },
);

watch(
  () => apisStore.getFavoritesTrigger,
  (data) => {
    if (data) {
      favorites.value = data.showFavorites;
      reset();
      loadInformationPaged();
      apisStore.resetFavoritesTrigger();
    }
  },
);

onMounted(() => {
  if (props.single !== null && props.single?.toString() !== '') {
    fullView.value = true;
  }
});
</script>

<script lang="ts">
import ApisInformation from './ApisInformation.vue';
import Spinner from '@assets/protected/vue/component/helper/Spinner.vue';

// Register the infinite scroll directive for this component
// TODO: Use https://vueuse.org/core/useInfiniteScroll/
// const infiniteScrollDirective = require('vue-infinite-scroll/vue-infinite-scroll.js');

export default {
  name: 'ApisFrontendComponent',
  components: {
    ApisInformation,
    Spinner,
  },
  directives: {
    infiniteScroll: infiniteScrollDirective,
  },
};
</script>

<style lang="scss">
div#apisFrontend {
  div.abus-spinner {
    margin-top: 30px !important;
  }

  div.abus-spinner + div {
    margin-bottom: 30px !important;
  }
}
</style>
