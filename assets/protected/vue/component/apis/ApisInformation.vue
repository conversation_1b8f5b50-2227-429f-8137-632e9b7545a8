<template>

  <div class="card card-apis vue-card mb-3" :class="{ 'backend': backend }">
    <div class="card-header bg-abus-blue-dark text-white">

      <div class="w-100">

        <div class="float-left ps-3">
          <span @click="showFullView(informationBuilder.information)">{{
              informationBuilder.sectionPrefix
            }} - {{ informationBuilder.information.number }}</span>
        </div>

        <div class="float-left ps-3">
          <span class="title" @click="showFullView(informationBuilder.information)"
                v-html="informationBuilder.information.title"></span>
          <span
            class="badge badge-danger ml20"
            @click="showFullView(informationBuilder.information)"
            v-show="informationBuilder.markedAsRead === false && !backend"
            v-html="t('apis/admin/new')"></span>
        </div>

        <div class="float-right px-3 d-none d-sm-block">
          <span class="published">{{ formatDate(informationBuilder.information.releaseDate) }}</span>
        </div>

        <div class="icons float-right px-3 d-none d-md-block">
          <FontAwesomeIcon v-show="informationBuilder.favorite" class="favorite mr5"
                           @click="removeFavorite(informationBuilder.information)" :title="t('apis/removeFavorite')"
                           icon="star" aria-hidden="true" style="color: #FFD800;" />
          <FontAwesomeIcon v-show="!informationBuilder.favorite" class="favorite mr5"
                           @click="addFavorite(informationBuilder.information)" :title="t('apis/addFavorite')"
                           icon="star" aria-hidden="true" />

          <a :href="'/apis/' + informationBuilder.information.section + '/pdf/' + informationBuilder.information.number"
             target="_blank">
            <FontAwesomeIcon class="fa-lg mr5" :icon="['far', 'file-pdf']" :title="t('apis/saveAsPDF')" />
          </a>
          <a
            :href="'/apis/' + informationBuilder.information.section + '/print/' + informationBuilder.information.number"
            target="_blank">
            <FontAwesomeIcon class="fa-lg" icon="print" :title="t('apis/print')" />
          </a>
        </div>

        <div class="flags float-right px-3 d-none d-md-block">
          <img v-for="(language, key) in informationBuilder.information.availableLanguages" v-if="language"
               :src="languageFlag(key)">
        </div>
      </div>

    </div>
    <div class="card-body bg-white text-dark">

      <!-- Full View Start-->

      <div class="markAsReadExplanation"
           v-if="internalFullView & !backend && informationBuilder.information.content !== ''"
           v-show="informationBuilder.markedAsRead === false">
        <div class="bs-callout bs-callout-danger mt-0">
          <!--                <h4>{{ t('apis/markAsRead') }}</h4>-->
          <p v-html="t('apis/markAsReadExplanation')" />
          <!--                        <span v-html="t('apis/markAsReadTip')" />-->
          <!--                        <font-awesome-icon icon="lightbulb"></font-awesome-icon>-->
          <!--                        <span v-html="t('apis/markAsReadExplanation')" />-->
          <!--                    </p>-->
          <button type="button" class="btn btn-success mr5 mb10" @click="markAsRead(informationBuilder.information)"
                  v-html="t('apis/markAsRead')"></button>
        </div>
      </div>

      <div class="content" v-if="internalFullView && informationBuilder.information.content !== ''"
           @click="showFullView(informationBuilder.information)">
        <p v-html="t('apis/salutation')" />
        <p v-html="informationBuilder.information.content" />
        <p>{{ t('apis/goodbye') }}<br />
          ABUS Kransysteme GmbH<br />
          {{ informationBuilder.information.author }}
        </p>
        <p>
          <table class="contact">
            <tr>
              <td>E-Mail:&nbsp;</td>
              <td><a :href="'mailto: ' + informationBuilder.information.authorEmail"
                     onclick="event.stopImmediatePropagation();"
                     v-html="informationBuilder.information.authorEmail"></a></td>
            </tr>
            <tr v-if="informationBuilder.information.authorPhone !== ''">
              <td>Telefon:&nbsp;</td>
              <td>+49 2261 37-{{ informationBuilder.information.authorPhone }}</td>
            </tr>
          </table>
        </p>
      </div>
      <!-- Full View Ende-->

      <!-- Short View Start-->
      <div class="content" v-show="!internalFullView && informationBuilder.information.contentShort !== ''"
           @click="showFullView(informationBuilder.information)"
           v-html="informationBuilder.information.contentShort + '... <span class=\'readMore\'>' + t('apis/readMore') + '</span>'"></div>
      <!-- Short View Ende-->

      <div class="attachment" v-if="internalFullView">
        <div class="row" v-for="file in informationBuilder.files">
          <div class="col-md-12 p-0">
            <a
              :href="'/apis/' + informationBuilder.information.section + '/' + informationBuilder.information.number + '/attachment/' + file.name"
              target="_blank"><img :src="getFileIcon(file.extension)" class="icon"></a>
            <a
              :href="'/apis/' + informationBuilder.information.section + '/' + informationBuilder.information.number + '/attachment/' + file.name"
              target="_blank" v-html="file.filename"></a>
            <a
              :href="'/apis/' + informationBuilder.information.section + '/' + informationBuilder.information.number + '/attachment/' + file.name"
              target="_blank" class="attachementFlags">
              <img v-for="language in file.language" v-if="language" :src="languageFlag(language)">
            </a>
          </div>
        </div>
      </div>

      <div class="categories" v-if="Object.keys(informationBuilder.information.categories).length > 0 || backend">
        <div class="row">
          <!--TODO größer, wenn kein ADMIN-->
          <div class="col-md-9">
            <span v-for="(category, key) in informationBuilder.information.categories"
                  v-html="t('apis/' + informationBuilder.information.section + '/' + key.toLowerCase().replace(/ /g, '_'))"
                  @click="filterCategory(key)" :title="t('apis/filter') + ': ' + category"
                  class="badge badge-secondary mr10 mb10 clickable"
                  :class="{ 'badge-success': categoryFilter[key] }"></span>
          </div>

          <div class="col-md-3 admin">

            <div v-show="informationBuilder.isAdmin || informationBuilder.isEditor || informationBuilder.isTranslator"
                 class="adminContainer">
              <div>
                <a
                  v-show="(informationBuilder.canEdit || informationBuilder.canTranslate) && informationBuilder.canEditForHours === -1"
                  v-html="t('apis/admin/edit')" class="btn btn-primary btn-xs"
                  :href="'/apisadmin/edit/' + informationBuilder.information.section + '/' + informationBuilder.information.number"></a>
                <a v-show="informationBuilder.canEdit === false && informationBuilder.canEditForHours >= 0"
                   v-html="t('apis/admin/edit') + ' ' + (24 - informationBuilder.canEditForHours) + 'h'"
                   class="btn btn-primary btn-xs"
                   :href="'/apisadmin/edit/' + informationBuilder.information.section + '/' + informationBuilder.information.number"></a>
                <a v-show="informationBuilder.canUnpublish && backend" v-html="t('apis/admin/edit')"
                   class="btn btn-warning btn-xs" @click="unpublishModal()"></a>
                <!--<a v-show="informationBuilder.canRetract && !backend" v-html="t('retract')" class="btn btn-danger btn-xs" @click="retractModal()"></a>-->
              </div>
              <div v-show="!backend" class="report"
                   :title="informationBuilder.viewedPercentage + '% ' + t('apis/markedByReceivers')"
                   @click="loadReport(informationBuilder.information)">
                <div class="bar"
                     :style="'width: ' + informationBuilder.viewedPercentage + '%; background-color: ' + percentageToHsl(parseInt(informationBuilder.viewedPercentage) / 100, 0, 120)"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Spinner v-show="loadingReport" size="medium" :message="t('apis/loading')" />

      <div class="reportTable" v-if="report">

        <div class="receivers">
          <span v-for="(value, key) in informationBuilder.information.receivers"
                class="badge badge-success ml10 mr10 mb20" v-html="t(key.toUpperCase())"></span>
        </div>

        <b-container fluid class="mt10" style="clear: both;">

          <b-row>
            <b-col md="6">
              <b-input-group>
                <b-form-input v-model="filter" :placeholder="t('apis/search')" />
                <b-input-group-append>
                  <b-btn :disabled="!filter" @click="filter = ''" v-html="t('apis/clear')"></b-btn>
                </b-input-group-append>
              </b-input-group>
            </b-col>
          </b-row>

          <br />

          <b-table striped hover :items="report?.users" :fields="fields" v-model:sort-by="sortBy" :filter="filter">
            <template v-slot:cell(index)="data">
              {{ data.index + 1 }}
            </template>
            <template v-slot:cell(mailReceiver)="data">
              <i v-if="data.value === 'gotMail'" class="fas fa-envelope" aria-hidden="true"
                 :title="t('apis/admin/gotEmail')"></i>
              <i v-if="data.value === 'optOut'" class="fas fa-envelope" aria-hidden="true"
                 :title="t('apis/admin/optout')" style="color: #dfdfdf;"></i>
            </template>
          </b-table>
        </b-container>

      </div>

      <div class="row references"
           v-show="informationBuilder.complements.length > 0 || informationBuilder.complementedBy.length > 0 || informationBuilder.replaces.length > 0 || informationBuilder.replacedBy.length > 0">

        <div class="col-md-12 reference" v-for="complements in informationBuilder.complements">

          <div class="closeReference"
               v-show="reference[informationBuilder.information.number + '_' + complements.number]">
            <FontAwesomeIcon icon="times" @click="closeReference()"></FontAwesomeIcon>
          </div>

          <div class="linkDescription">
            {{ t('apis/referenceBeginning') }} {{ t('apis/complements') }}
            <span class="link" @click="loadReference(informationBuilder.information.section, complements.number)">{{
                informationBuilder.sectionPrefix
              }} {{ complements.number }} {{ complements.title }}</span>
            <span v-if="complements.reason">: {{ complements.reason }}</span>
          </div>

          <ApisInformation
            v-if="reference[informationBuilder.information.number + '_' + complements.number]"
            :information-builder="reference[informationBuilder.information.number + '_' + complements.number]"
            :locale="locale"
            :fullView="true"
          />
        </div>

        <div class="col-md-12 reference" v-for="complementedBy in informationBuilder.complementedBy">

          <div class="closeReference"
               v-show="reference[informationBuilder.information.number + '_' + complementedBy.referencedByNumber]">
            <FontAwesomeIcon icon="times" @click="closeReference()"></FontAwesomeIcon>
          </div>

          <div class="linkDescription">
            {{ t('apis/referenceBeginningPast') }} {{ t('apis/complementedBy') }}
            <span class="link"
                  @click="loadReference(informationBuilder.information.section, complementedBy.referencedByNumber)">{{
                informationBuilder.sectionPrefix
              }} {{ complementedBy.referencedByNumber }} {{ complementedBy.referencedByTitle }}</span>
            <span v-if="complementedBy.reason">: {{ complementedBy.reason }}</span>
          </div>

          <ApisInformation
            v-if="reference[informationBuilder.information.number + '_' + complementedBy.referencedByNumber]"
            :information-builder="reference[informationBuilder.information.number + '_' + complementedBy.referencedByNumber]"
            :locale="locale"
          />
        </div>

        <div class="col-md-12 reference" v-for="replaces in informationBuilder.replaces">

          <div class="closeReference" v-show="reference[informationBuilder.information.number + '_' + replaces.number]">
            <FontAwesomeIcon icon="times" @click="closeReference()"></FontAwesomeIcon>
          </div>

          <div class="linkDescription">
            {{ t('apis/referenceBeginning') }} {{ t('apis/replaces') }}
            <span class="link" @click="loadReference(informationBuilder.information.section, replaces.number)"
                  v-if="informationBuilder.isAdmin || informationBuilder.isEditor">{{
                informationBuilder.sectionPrefix
              }} {{ replaces.number }} {{ replaces.title }}</span>
            <span class="strong" v-else>{{ informationBuilder.sectionPrefix }} {{ replaces.number }} {{
                replaces.title
              }}</span>
            <span v-if="replaces.reason">: {{ replaces.reason }}</span>
          </div>

          <ApisInformation
            v-if="reference[informationBuilder.information.number + '_' + replaces.number]"
            :information-builder="reference[informationBuilder.information.number + '_' + replaces.number]"
            :locale="locale"
          />
        </div>

        <div class="col-md-12 reference" v-for="replacedBy in informationBuilder.replacedBy">

          <div class="closeReference"
               v-show="reference[informationBuilder.information.number + '_' + replacedBy.referencedByNumber]">
            <FontAwesomeIcon icon="times" @click="closeReference()"></FontAwesomeIcon>
          </div>

          <div class="linkDescription">
            {{ t('apis/referenceBeginningPast') }} {{ t('apis/replacedBy') }}
            <span class="link"
                  @click="loadReference(informationBuilder.information.section, replacedBy.referencedByNumber)">{{
                informationBuilder.sectionPrefix
              }} {{ replacedBy.referencedByNumber }} {{ replacedBy.referencedByTitle }}</span>
            <span v-if="replacedBy.reason">: {{ replacedBy.reason }}</span>
          </div>

          <ApisInformation
            v-if="reference[informationBuilder.information.number + '_' + replacedBy.referencedByNumber]"
            :information-builder="reference[informationBuilder.information.number + '_' + replacedBy.referencedByNumber]"
            :locale="props.locale"
          />
        </div>
      </div>

    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, getCurrentInstance } from 'vue';
import axios from 'axios';
import FontAwesomeIcon from '@fortawesome/vue-fontawesome';

import Spinner from '@assets/protected/vue/component/helper/Spinner.vue';
import { useModalStore, useApisStore } from '@assets/protected/vue/stores';
import { useDateHelpers } from '@assets/protected/vue/composables/useDateHelpers';

// Import flag images
import flagDe from '@flags/png100px/de.png';
import flagEs from '@flags/png100px/es.png';
import flagFr from '@flags/png100px/fr.png';
import flagGb from '@flags/png100px/gb.png';

const { formatDate } = useDateHelpers();

const { proxy } = getCurrentInstance()!;
const t = proxy.$t;

const modalStore = useModalStore();
const apisStore = useApisStore();

interface Props {
  informationBuilder: any;
  locale: string;
  fullView: boolean;
  categoryFilter?: any;
  backend?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  categoryFilter: () => ({}),
  backend: false,
});

// Reactive data
const internalFullView = ref<boolean>(false);
const reference = ref<any>({});
const report = ref<any>(false);
const loadingReport = ref<boolean>(false);
const filter = ref<string>('');
const sortBy = ref<string>('name');

// Computed fields for table
const fields = computed(() => [
  'index',
  { key: 'name', label: t('apis/name'), sortable: true },
  { key: 'company', label: t('apis/company'), sortable: true },
  { key: 'username', label: t('apis/email'), sortable: true },
  { key: 'firstViewed', label: t('apis/admin/firstViewed'), formatter: 'formatDate', sortable: true },
  { key: 'lastViewed', label: t('apis/admin/lastViewed'), formatter: 'formatDate', sortable: true },
  { key: 'markedAsRead', label: t('apis/markedAsRead'), formatter: 'formatDate', sortable: true },
  {
    key: 'mailReceiver',
    label: ' ',
    sortable: false,
    formatter: (value: any, key: any, item: any) => {
      if (value && !item.optOut) return 'gotMail';
      if (value && item.optOut) return 'optOut';
      return '';
    },
  },
]);

watch(() => props.fullView, (newVal) => {
  internalFullView.value = newVal;
}, { immediate: true });

const loadReference = (section: string, number: number): void => {
  const url = '/apis/load/' + section + '/1/1';

  // Create URLSearchParams for form data
  const params = new URLSearchParams();
  params.append('number', JSON.stringify(number));

  axios.post(url, params, { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } })
    .then((response: any) => {
      const oldRef = reference.value;
      reference.value = {
        ...oldRef,
        [props.informationBuilder.information.number + '_' + number]: response.data[0],
      };
    })
    .catch((error: any) => {
      // Silent error handling as in original
    });
};

const closeReference = (): void => {
  reference.value = {};
};

const filterCategory = (key: string): void => {
  apisStore.setFilter({ filter: key, byBadge: true });
};

const markAsRead = (information: any): void => {
  const url = '/apis/read/' + information.section + '/' + information.number;

  axios.post(url)
    .then((response: any) => {
      props.informationBuilder.markedAsRead = true;
    })
    .catch((error: any) => {
      modalStore.showModal({ cssClass: 'error', message: error.response.data });
    });
};

// Fügt eine Information zu den Favoriten hinzu
const addFavorite = (information: any): void => {
  const url = '/apis/favorites/' + information.section + '/' + information.number;

  axios.post(url)
    .then((response: any) => {
      props.informationBuilder.favorite = true;
    })
    .catch((error: any) => {
      modalStore.showModal({ cssClass: 'error', message: error.response.data });
    });
};

// Löscht eine Information aus den Favoriten
const removeFavorite = (information: any): void => {
  const url = '/apis/favorites/' + information.section + '/' + information.number;

  axios.delete(url)
    .then((response: any) => {
      props.informationBuilder.favorite = false;
    })
    .catch((error: any) => {
      modalStore.showModal({ cssClass: 'error', message: error.response.data });
    });
};

const showFullView = (information: any): void => {
  internalFullView.value = !internalFullView.value;

  const url = '/apis/viewed/' + information.section + '/' + information.number;

  axios.post(url)
    .then((response: any) => {
      // Stille Aktion, dient nur dem Logging
    });
};

const percentageToHsl = (percentage: number, hue0: number, hue1: number): string => {
  const hue = Math.round((percentage * (hue1 - hue0)) + hue0);
  return 'hsl(' + hue + ', 100%, 50%)';
};

const loadReport = (information: any): void => {
  // Wenn der Report schon geöffnet ist, wieder schliessen
  if (report.value) {
    report.value = false;
  } else {
    loadingReport.value = true;

    const url = '/apis/report/' + information.section + '/' + information.number;

    axios.get(url)
      .then((response: any) => {
        report.value = response.data;
        loadingReport.value = false;
      })
      .catch((error: any) => {
        loadingReport.value = false;
        modalStore.showModal({ cssClass: 'error', message: error.response.data });
      });
  }
};

const unpublishModal = (): void => {
  modalStore.showModal({
    cssClass: 'danger',
    title: t('apis/admin/unpublishTitle'),
    message: t('apis/admin/unpublishMessage'),
    buttons: [
      {
        title: t('apis/cancel'),
        variant: 'secondary',
        clickIdentifier: 'dismiss'
      },
      {
        title: t('apis/admin/unpublish'),
        variant: 'danger',
        clickIdentifier: 'unpublish',
        returnValue: props.informationBuilder.information.section + '_' + props.informationBuilder.information.number
      }
    ]
  });
};


const languageFlag = (key: string): string => {
  switch (key) {
    case 'de':
    case 'ger':
      return flagDe;
    case 'es':
    case 'esl':
      return flagEs;
    case 'fr':
    case 'fre':
      return flagFr;
    case 'gb':
    case 'eng':
      return flagGb;
    default:
      return '';
  }
};

const getFileIcon = (extension: string): string => {
  try {
    return new URL(`@assets/public/images/fileIcons/32px/${extension}.png`, import.meta.url).href;
  } catch {
    return new URL('@assets/public/images/fileIcons/32px/default.png', import.meta.url).href;
  }
};

const handleUnpublish = (data: any) => {
  if (data.id === props.informationBuilder.information.section + '_' + props.informationBuilder.information.number) {
    const url = '/apisadmin/state/change/' + props.informationBuilder.information.section + '/' + props.informationBuilder.information.number + '/unpublish';

    axios.post(url)
      .then((response: any) => {
        modalStore.hideModal();
        window.location.href = '/apisadmin/' + props.informationBuilder.information.section;
      })
      .catch((error: any) => {
        modalStore.hideModal();
        modalStore.showModal({ cssClass: 'error', message: error.response.data });
      });
  }
};

// Watch for modal clicks
watch(() => modalStore.getClick, (clickIdentifier) => {
  if (clickIdentifier === 'unpublish') {
    handleUnpublish({ id: modalStore.getValue });
    modalStore.resetClick();
  }
});

onMounted(() => {
  // Per Prop() übergebene Werte in interne Variablen speichern, sonst meckert Vue (Warning)
  internalFullView.value = props.fullView;
});
</script>

<style lang="scss" scoped>

$hoverColor: #dfdfdf;

div.card-apis {

  div.card-header {
    /*color: #fff;*/
    /*background-color: #2d3644;*/
    /*border-color: #424f60;*/

    div.number span,
    span.title {
      &:hover {
        cursor: pointer;
      }
    }

    div.flags {
      img {
        margin-right: 5px;
        width: 18px;
        height: 13px;
      }
    }

    .favorite {
      position: relative;

      &:hover {
        cursor: pointer;
      }
    }

    div.icons {

      a:link, a:visited {
        color: #ffffff;
      }

      a:hover {
        color: $hoverColor;
      }

      img:hover, i:hover {
        cursor: pointer;
      }

      i {
        font-size: 1em;
        margin-left: 5px;
      }
    }
  }

  div.card-body {
    padding-top: 0;
    padding-bottom: 0;

    div.markAsReadExplanation {
      padding-top: 15px;
    }

    div.content {
      padding-top: 15px;
      padding-bottom: 15px;

      p, ul, li, span {
        font-family: inherit;
        font-size: inherit;
      }

      &:hover {
        cursor: pointer;
      }
    }

    div.categories {

      span.category.label {
        cursor: pointer;
      }

      div.admin {
        text-align: right;

        div {
          float: right;
        }

        div.adminContainer {

          margin-bottom: 10px;

          div.report {
            width: 50px;
            height: 20px;
            border: #dfdfdf 1px solid;
            background-color: #000000;
            padding: 2px;
            cursor: pointer;
            margin-right: 5px;
            margin-top: 1px;

            div.bar {
              float: left;
              height: 100%;
            }
          }

          a:link, a:hover, a:visited {
            //color: #fff0ff;
          }

          a.btn {
            margin-left: 10px;
          }
        }
      }
    }

    div.attachment {
      div.row {

        &:last-child {
          margin-bottom: 20px;
        }

        margin: 5px 0;

        img {
          height: 20px;
        }

        a.attachementFlags {
          margin-left: 10px;

          img {
            margin-right: 5px;
            width: 18px;
            height: 13px;
          }
        }
      }
    }

    div.reportTable {
      border: #dfdfdf 1px solid;
      border-radius: 3px;
      margin: 10px 0;


      div.receivers {
        float: right;
        padding: 10px 10px 0 10px;

        div {
          float: left;
          margin-right: 20px;
        }
      }

      table {
        thead th {
          &.nowrap {
            white-space: nowrap;
          }
        }

        tbody {
          tr {

            &.table-danger {
              background-color: #f5c6cb;

              &:hover {
                background-color: #f1b0b7;
              }
            }

            &.deletedFromAD {
              background-color: #a20000;
              color: #ffffff;

              td:nth-child(2) {
                font-size: 12px;
                line-height: 20px;
              }
            }

            td {
              &.nowrap {
                white-space: nowrap;
              }
            }
          }
        }
      }
    }

    div.references {
      color: #fff;
      background-color: #455264;
      border-bottom-left-radius: 3px;
      border-bottom-right-radius: 3px;
      margin-right: -16px;
      margin-left: -16px;

      div.reference {
        padding: 5px 15px;

        div.closeReference {
          position: absolute;
          right: 15px;

          i {
            font-size: 20px;
            color: #000000;
            border: 1px solid #e25d5d;
            background-color: #eabdbd;
            border-radius: 3px;
            padding: 0 10px;

            &:hover {
              border: 1px solid #e25d5d;
              background-color: #dc7878;
              cursor: pointer;
            }
          }
        }

        div.linkDescription {
          padding-right: 50px;
          font-size: 0.875rem;
        }

        .card {
          margin-top: 20px;
        }

        span.link {
          font-weight: bold;

          &:hover {
            color: $hoverColor;
            text-decoration: underline;
            cursor: pointer;
          }
        }

        div.panel-apis {
          margin-top: 15px;

          div.panel-body {
            color: #000000;
          }
        }
      }
    }
  }

  &.backend {
    div.adminContainer {
      i {
        padding-right: 10px !important;
      }
    }
  }
}

</style>

<style lang="scss">

div.card-apis {

  th {
    text-align: center;
  }

  span.readMore {
    color: #337ab7;

    &:hover {
      text-decoration: underline;
    }
  }

  strong {
    font-weight: bold;
  }

  div.reportTable {
    table {
      tbody {
        tr {

          &.table-danger {
            background-color: #f5c6cb;

            td:nth-child(2) {
              font-size: 12px;
              line-height: 20px;
            }

            &:hover {
              background-color: #f1b0b7;
            }
          }

          &.table-warning {
            background-color: #ffeeba;

            &:hover {
              background-color: #ffe8a1;
            }
          }
        }
      }
    }
  }
}

.card div.card-apis div.references {
  margin-right: -16px !important;
  margin-left: -16px !important;
}
</style>
