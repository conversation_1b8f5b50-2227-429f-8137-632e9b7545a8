<template>
  <div>
    <a
      v-show="showIndexButton"
      class="btn btn-danger btn-block btn-sm mr5"
      :href="'/apis/' + section"
      v-html="$t('apis/admin/leave_adminsitration')"></a>
    <a
      v-show="showBackendIndexButton"
      class="btn btn-primary btn-block btn-sm mr5"
      :href="'/apisadmin/' + section"
      v-html="$t('apis/admin/backendIndex')"></a>

    <hr />

    <a
      v-show="showNewInformationButton"
      class="btn btn-success btn-block btn-sm mr5"
      :href="'/apisadmin/create/' + section"
      v-html="$t('apis/admin/create_new_information')"></a>

    <button
      v-show="requestTranslationButton !== ''"
      class="btn btn-success btn-block btn-sm mr5"
      style="white-space: normal"
      @click="changeState(requestTranslationButton)"
      v-html="$t('apis/admin/ready') + '<br />' + $t('apis/admin/pleaseTranslate')"></button>
    <button
      v-show="requestPublishingButton !== ''"
      class="btn btn-success btn-block btn-sm mr5"
      style="white-space: normal"
      @click="changeState(requestPublishingButton)"
      v-html="$t('apis/admin/ready') + '<br />' + $t('apis/admin/pleasePublish')"></button>
    <button
      v-show="publishButton !== ''"
      class="btn btn-success btn-block btn-sm mr5"
      style="white-space: normal"
      @click="changeState(publishButton)"
      v-html="$t('apis/admin/publish')"></button>
    <button
      v-show="rejectButton !== ''"
      class="btn btn-danger btn-block btn-sm mr5"
      style="white-space: normal"
      @click="changeState(rejectButton)"
      v-html="$t('apis/admin/backToAuthor')"></button>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useApisStore } from '@assets/protected/vue/stores';

const apisStore = useApisStore();

const section = ref('');
const showIndexButton = ref(false);
const showBackendIndexButton = ref(false);
const showNewInformationButton = ref(false);

// String ist die gewünschte Transition
const requestTranslationButton = ref('');
const requestPublishingButton = ref('');
const publishButton = ref('');
const rejectButton = ref('');

const changeState = (transition: string): void => {
  apisStore.triggerTransition(transition);
};

const onSetSidebar = (data: any) => {
  section.value = data.section;
  showIndexButton.value = data.showIndexButton;
  showBackendIndexButton.value = data.showBackendIndexButton;
  showNewInformationButton.value = data.showNewInformationButton;

  if (data.requestTranslationButton !== undefined) {
    requestTranslationButton.value = data.requestTranslationButton;
  }
  if (data.requestPublishingButton !== undefined) {
    requestPublishingButton.value = data.requestPublishingButton;
  }
  if (data.publishButton !== undefined) {
    publishButton.value = data.publishButton;
  }
  if (data.rejectButton !== undefined) {
    rejectButton.value = data.rejectButton;
  }
};

// Watch for sidebar data changes from the store
watch(
  () => apisStore.getSidebarData,
  (data) => {
    if (data) {
      onSetSidebar(data);
    }
  },
  { immediate: true },
);
</script>
