<template>
  <div v-cloak class="sidebar-panel">
    <div class="row">
      <button v-if="!favorites" class="btn btn-abus-blue-light btn-block btn-sm" @click="showFavorites()">
        <font-awesome-icon icon="star" style="color: #ffd800"></font-awesome-icon> {{ $t('apis/my_favorites') }}
      </button>
      <button v-if="favorites" class="btn btn-danger btn-block btn-sm" @click="showFavorites()">
        {{ $t('apis/show_all') }}
      </button>
    </div>

    <div class="row">
      <button class="btn btn-secondary btn-block btn-sm" @click="showSettings()">{{ $t('apis/settings') }}</button>
    </div>

    <h5 class="sidebar-panel-title">
      <i class="fa fa-search mr5"></i>{{ $t('apis/search') }}
      <button class="btn btn-success btn-xs" @click="resetSearch()">{{ $t('apis/reset') }}</button>
    </h5>

    <div class="row">
      <DebouncedInput
        :placeholder="$t('apis/searchtext')"
        :min-chars="3"
        :callback="setQuery"
        :default-value="query"
        :disableLoading="true"
        :disableReset="true"
        :disableResetButton="true"
        :queryOnEmptyString="true"
        :delay="500" />
    </div>

    <div class="row">
      <div class="input-group">
        <div class="input-group-prepend">
          <div class="input-group-text">
            <font-awesome-icon fixed-width icon="calendar"></font-awesome-icon>
          </div>
        </div>
        <!-- TODO: Replace date-picker component -->
        <!--        <date-picker v-model="fromDate" :config="config" :wrap="true" :placeholder="$t('apis/fromDate')"></date-picker>-->
      </div>
    </div>

    <div class="row">
      <div class="input-group">
        <div class="input-group-prepend">
          <div class="input-group-text">
            <font-awesome-icon fixed-width icon="calendar"></font-awesome-icon>
          </div>
        </div>
        <!-- TODO: Replace date-picker component -->
        <!--        <date-picker
          v-model="untilDate"
          :config="config"
          :wrap="true"
          :placeholder="$t('apis/untilDate')"></date-picker>-->
      </div>
    </div>

    <h5 class="sidebar-panel-title mt25"><i class="fa fa-filter mr5"></i>{{ $t('apis/filter') }}</h5>

    <div class="mt5"></div>

    <div v-for="(category, key, index) in categories" class="row mt0 mb0">
      <div>
        <div class="custom-control custom-checkbox mt-1">
          <input
            :id="'checkbox_category_' + index"
            v-model="categoryFilter[key]"
            class="custom-control-input"
            type="checkbox"
            :value="true"
            @click="setCategory(key)" />
          <label class="custom-control-label" :for="'checkbox_category_' + index">
            <span class="badge badge-secondary" v-html="category"></span>
          </label>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, getCurrentInstance } from 'vue';
import FontAwesomeIcon from '@fortawesome/vue-fontawesome';
import { useDateHelpers } from '@assets/protected/vue/composables/useDateHelpers';

import { useApisStore } from '@assets/protected/vue/stores';
import DebouncedInput from '../helper/DebouncedInput.vue';
//import datePicker from '@node_modules/vue-bootstrap-datetimepicker/src/component.vue';
//import 'pc-bootstrap4-datetimepicker/build/css/bootstrap-datetimepicker.css';
// import 'vue-bootstrap-datetimepicker-css';

interface Props {
  section: string;
  single?: number | null;
  categories: object;
}

const props = withDefaults(defineProps<Props>(), {
  single: null,
});

const apisStore = useApisStore();

const { formatDate } = useDateHelpers();

const { proxy } = getCurrentInstance()!;
const locale = proxy.$i18n.locale;

const favorites = ref<boolean>(false);
const query = ref<string>('');
const fromDate = ref<string>('');
const fromDateNormalized = ref<string>('');
const untilDate = ref<string>('');
const untilDateNormalized = ref<string>('');
const categoryFilter = ref<any>({});

const config = ref<any>({
  locale: locale,
  format: 'L',
  showClear: true,
  useCurrent: false,
  allowInputToggle: true,
  icons: {
    time: 'fa fa-clock-o',
    date: 'fa fa-calendar',
    up: 'fa fa-chevron-up',
    down: 'fa fa-chevron-down',
    previous: 'fa fa-chevron-left',
    next: 'fa fa-chevron-right',
    today: 'fa fa-sun-o',
    clear: 'fa fa-trash',
    close: 'fa fa-remove',
  },
  widgetPositioning: {
    horizontal: 'right',
    vertical: 'top',
  },
});

// Beim Reset müssen die Watcher aus sein, sonst wird die Suche mehrfach getriggert
const watchQuery = ref<boolean>(true);
const watchFilter = ref<boolean>(true);
const watchFromDate = ref<boolean>(true);
const watchUntilDate = ref<boolean>(true);

// Watchers
watch(query, () => {
  watchQuery.value ? search() : (watchQuery.value = true);
});

watch(fromDate, () => {
  if (watchFromDate.value) {
    fromDateNormalized.value = formatDate(fromDate.value);
    search();
  } else {
    watchFromDate.value = true;
  }
});

watch(untilDate, () => {
  if (watchUntilDate.value) {
    untilDateNormalized.value = formatDate(untilDate.value);
    search();
  } else {
    watchUntilDate.value = true;
  }
});

// Methods
function setQuery(queryValue: string): void {
  query.value = queryValue;
}

function setCategory(key: string): void {
  apisStore.setFilter({ filter: key, byBadge: true });
}

function search(): void {
  apisStore.triggerSearch({
    query: query.value,
    fromDate: fromDateNormalized.value,
    untilDate: untilDateNormalized.value,
    filter: categoryFilter.value,
  });
}

function resetSearch(): void {
  if (query.value !== '') {
    watchQuery.value = false;
    query.value = '';
  }

  if (Object.keys(categoryFilter.value).length > 0) {
    watchFilter.value = false;
    categoryFilter.value = {};
  }

  if (fromDate.value !== '') {
    watchFromDate.value = false;
    fromDate.value = '';
    fromDateNormalized.value = '';
  }

  if (untilDate.value !== '') {
    watchUntilDate.value = false;
    untilDate.value = '';
    untilDateNormalized.value = '';
  }

  search();
}

function showFavorites(): void {
  favorites.value = !favorites.value;
  apisStore.showFavorites({ showFavorites: favorites.value });
}

function showSettings(): void {
  apisStore.showSettings({ section: props.section, categories: props.categories });
}

function setFilter(data: any): void {
  if (data.byBadge !== undefined && data.byBadge === true) {
    if (categoryFilter.value[data.filter] === undefined) {
      categoryFilter.value[data.filter] = true;
    } else {
      categoryFilter.value[data.filter] = !categoryFilter.value[data.filter];
    }
  } else {
    categoryFilter.value[data.filter] = categoryFilter.value[data.filter];
  }

  search();
}

/**
 * Store Watcher für Filter Events
 */
watch(
  () => apisStore.getFilterTrigger,
  (data) => {
    if (data) {
      setFilter(data);
      apisStore.resetFilterTrigger();
    }
  },
);

onMounted(() => {
  // Wenn eine bestimmte Nummer per URL Parameter (single) übergeben wurde, dann wird die query entsprechend angepasst, damit nur nach der einen Nummer gesucht wird
  if (props.single && props.single !== null) {
    query.value = 'number: ' + props.single;
  }
});
</script>

<style lang="scss" scoped>
div.sidebar-panel {
  background-color: #262624;

  div.row {
    margin: 10px;

    div.checkbox-custom {
      label span.label {
        padding: 3px 6px;
        font-size: 85%;
      }
    }
  }

  h5 {
    button {
      float: right;
      padding: 0 5px !important;
      margin-top: -3px !important;
    }
  }
}
</style>
