<template>
  <div id="modal" class="receiverListModal">
    <b-modal
      ref="modal"
      size="lg"
      centered
      no-fade
      no-close-on-backdrop
      :hide-header-close="true"
      :ok-only="true"
      :title="t('apis/receiverlist')">
      <b-table :sort-by="sortBy" striped hover :items="receivers" :fields="fields" :filter="filter">
        <template v-slot:index="data">
          {{ data.index + 1 }}
        </template>
      </b-table>
    </b-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, getCurrentInstance, watch } from 'vue';
import axios from 'axios';
import { useModalStore, useApisStore } from '@assets/protected/vue/stores';

const { proxy } = getCurrentInstance()!;
const t = proxy.$t;

const modalStore = useModalStore();
const apisStore = useApisStore();

const receivers = ref([]);
const filter = ref('');
const sortBy = ref('name');

const fields = [
  'index',
  { key: 'name', label: t('apis/name'), sortable: true },
  { key: 'company', label: t('apis/company'), sortable: true },
  { key: 'mail', label: t('apis/email'), sortable: true },
  // { key: 'mailReceiver', label: ' ', sortable: false,
  //     formatter: (value: any, key: any, item: any) => {
  //         if (value && !item.optOut) return '<i class="fa fa-envelope" aria-hidden="true" title="' + t('gotEmail') + '"></i>';
  //         if (value && item.optOut) return '<i class="fa fa-envelope" aria-hidden="true"  title="' + t('optout') + '" style="color: #dfdfdf;"></i>';
  //         return '';
  //     }}
];

const modal = ref();

// Watch for receiver list events
watch(
  () => apisStore.getReceiverListTrigger,
  (payload) => {
    if (payload) {
      const section = payload.section;
      const payloadReceivers = payload.receivers;

      const keys = Object.keys(payloadReceivers).filter((key) => payloadReceivers[key] !== false);
      const receiversString = keys.length ? `?${keys.join('&')}` : '';

      axios
        .get(`/apisadmin/receiverlist/${section}${receiversString}`)
        .then((response: any) => {
          receivers.value = response.data;
          if (modal.value && typeof modal.value.show === 'function') {
            modal.value.show();
          } else if (modal.value && typeof modal.value.show === 'function') {
            modal.value.show();
          }
        })
        .catch((error: any) => {
          modalStore.showModal({ cssClass: 'error', message: error.response.data });
        });

      apisStore.resetReceiverListTrigger();
    }
  },
);
</script>

<style lang="scss" scoped>
div.checkbox-custom label {
  padding-left: 15px;
}
</style>

<style lang="scss">
@import '@assets/scss/abstract/variables';

div.receiverListModal {
  header.modal-header {
    color: $white;
    background-color: $brand-primary;
  }
}
</style>
