<template>
  <div id="modal">
    <b-modal ref="modal" size="lg" centered no-fade no-close-on-backdrop :title="title" :class="[typeClass]">
      <div v-html="message"></div>
    </b-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, getCurrentInstance, watch } from 'vue';
import { useModalStore } from '@assets/protected/vue/stores';
import ghostImage from '@assets/public/images/abus/debug/ghost.svg';

interface ModalData {
  type?: string;
  title?: string;
  message?: string;
  template?: string;
  footer?: string;
}

interface ButtonCallbackData {
  id: string;
}

const { proxy } = getCurrentInstance()!;
const t = proxy.$t;

const modalStore = useModalStore();

const typeClass = ref<string>('');
const title = ref<string>('');
const template = ref<string>('');
const message = ref<string>('');
const footer = ref<string>('');
const modal = ref<any>(null);

const initializeDefaultFooter = () => {
  footer.value = `<button type="button" class="btn btn-secondary" data-callback="dismiss" data-callback-id="dismiss">${t('vue/helper/modal/close')}</button>`;
};

const buttonClickedCallback = (callback: string, data: ButtonCallbackData) => {
  if (callback === 'dismiss') {
    modal.value?.hide();
  } else {
    modalStore.clickModal({ identifier: callback, value: data });
  }
};

const handleShowModal = (modalData: ModalData) => {
  if (modalData.type !== undefined) {
    typeClass.value = modalData.type;
  }

  if (modalData.type === 'error') {
    let text = t('modal/' + modalData.message);
    if (text === 'modal/' + modalData.message) {
      text = modalData.message;
    }

    title.value = t('vue/helper/modal/errorTitle');
    message.value = `
        <div class="row">
            <div class="col-md-10 message">${text}</div>
            <div class="col-md-2 ghost">
                <img src="${ghostImage}">
            </div>
        </div>
    `;
  } else {
    if (modalData.message !== undefined) {
      message.value = modalData.message;
    }
  }

  if (modalData.template !== undefined) {
    template.value = modalData.template;
  }

  if (modalData.title !== undefined) {
    title.value = modalData.title;
  }

  if (modalData.footer !== undefined) {
    footer.value = modalData.footer;
  }

  /**
   * Leider kann man keine Callback Funktionen per v-html übergeben, da diese dann einfach ignoriert werden
   * Deshalb müssen die Callbacks manuell per jQuery dem DOM zur Laufzeit hinzugefügt werden.
   */
  const modalFooterElement = document.querySelector('#modal footer');
  if (modalFooterElement) {
    modalFooterElement.innerHTML = footer.value;

    const buttons = modalFooterElement.querySelectorAll('button');
    buttons.forEach((button: HTMLButtonElement) => {
      const callback = button.getAttribute('data-callback');
      const callbackId = button.getAttribute('data-callback-id');

      if (callback && callbackId) {
        button.addEventListener('click', () => {
          buttonClickedCallback(callback, { id: callbackId });
        });
      }
    });
  }

  modal.value?.show();
};

const handleHideModal = () => modal.value?.hide();

// Watch for store changes to show/hide modal
watch(
  () => modalStore.getShow,
  (shouldShow) => {
    if (shouldShow) {
      handleShowModal({
        type: modalStore.getCssClass,
        title: modalStore.getTitle,
        message: modalStore.getMessage,
      });
    } else {
      handleHideModal();
    }
  },
);

onMounted(() => {
  initializeDefaultFooter();
});
</script>

<style lang="scss">
@import '@assets/scss/abstract/variables';

div#modal {
  .modal-backdrop {
    z-index: 1000;
    opacity: 0.6;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }

  header.modal-header {
    min-height: 50px;
    h5 {
      float: left;
    }
  }

  .modal-content {
    background-color: transparent;
  }

  div.modal-body {
    //overflow: scroll;
    overflow: auto;
    width: 100%;
    max-height: 50vh !important;
    background-color: $white;

    div.message {
      font-size: 24px;
    }

    div.ghost {
      img {
        width: 100%;
      }
    }
  }

  footer.modal-footer {
    background-color: $abus-creme;
    border-bottom-left-radius: 0.3rem;
    border-bottom-right-radius: 0.3rem;
  }

  div.error,
  div.danger {
    header.modal-header {
      color: $white;
      background-color: $brand-danger;

      button {
        color: $white;
      }
    }
  }

  div.info {
    header.modal-header {
      color: $white;
      background-color: $brand-info;

      button {
        color: $white;
      }
    }
  }

  div.abus-blue-dark {
    header.modal-header {
      color: $white;
      background-color: $abus-blue-dark;

      button {
        color: $white;
      }
    }
  }
}
</style>
