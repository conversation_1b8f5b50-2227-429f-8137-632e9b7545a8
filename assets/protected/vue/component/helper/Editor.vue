<template>
  <ckeditor v-model="data" :editor="ClassicEditor" :config="config" v-bind="$attrs" />
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import {
  ClassicEditor,
  Essentials,
  Heading,
  Paragraph,
  Font,
  Bold,
  Italic,
  Underline,
  Strikethrough,
  Undo,
  Link,
  Image,
  Table,
  List,
  TableToolbar,
  ImageToolbar,
  ImageUpload,
  ImageStyle,
  ImageCaption,
  ImageResize,
  ImageInsert,
  HorizontalLine,
  SpecialCharacters,
  FontColor,
  FontBackgroundColor,
  SourceEditing,
  FindAndReplace,
  Indent,
  Alignment,
  Subscript,
  Superscript,
  RemoveFormat,
  Clipboard,
  SelectAll,
  SpecialCharactersEssentials,
} from 'ckeditor5';
import { Ckeditor } from '@ckeditor/ckeditor5-vue';

import 'ckeditor5/ckeditor5.css';

interface Props {
  value?: string;
  preset?: 'basic' | 'full';
}

const props = withDefaults(defineProps<Props>(), {
  value: '',
  preset: 'basic',
});

const emit = defineEmits<{
  input: [value: string];
}>();

const data = ref(props.value);

const config = computed(() => {
  const fullToolbar = [
    'undo',
    'redo',
    '|',
    'cut',
    'copy',
    '|',
    'bold',
    'italic',
    'underline',
    'strikethrough',
    'subscript',
    'superscript',
    '|',
    'removeFormat',
    '|',
    'findAndReplace',
    '|',
    'numberedList',
    'bulletedList',
    '|',
    'outdent',
    'indent',
    '|',
    'alignment:left',
    'alignment:center',
    'alignment:right',
    'alignment:justify',
    '|',
    'link',
    'unlink',
    '|',
    'insertImage',
    'insertTable',
    'horizontalLine',
    'specialCharacters',
    '|',
    'fontColor',
    'fontBackgroundColor',
    '|',
    'sourceEditing',
    '|',
    'maximize',
  ];

  const basicToolbar = [
    'heading',
    '|',
    'undo',
    'redo',
    '|',
    'bold',
    'italic',
    'underline',
    '|',
    'numberedList',
    'bulletedList',
    '|',
    'insertImage',
    'insertTable',
  ];

  return {
    licenseKey: 'GPL',
    plugins: [
      Essentials,
      Heading,
      Paragraph,
      Font,
      Bold,
      Italic,
      Underline,
      Strikethrough,
      Undo,
      Link,
      Image,
      Table,
      HorizontalLine,
      SpecialCharacters,
      FontColor,
      FontBackgroundColor,
      SourceEditing,
      FindAndReplace,
      Indent,
      Alignment,
      Subscript,
      Superscript,
      RemoveFormat,
      List,
      TableToolbar,
      ImageToolbar,
      ImageUpload,
      ImageStyle,
      ImageCaption,
      ImageResize,
      ImageInsert,
      Clipboard,
      SelectAll,
      SpecialCharactersEssentials,
    ],
    toolbar: props.preset === 'full' ? fullToolbar : basicToolbar,
  };
});

watch(
  () => props.value,
  (newVal) => {
    if (newVal !== data.value) {
      data.value = newVal;
    }
  },
);

watch(data, (newVal) => {
  emit('input', newVal); // v-model compatibility
});
</script>
