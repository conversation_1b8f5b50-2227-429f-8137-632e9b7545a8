<template>
  <div id="box">
    <nextcloud-inline-search
      v-if="inlineSearch && documentationMode === false"
      :start-folder="startFolder"
      :folders="content.childs"
      :documentation-mode="documentationMode" />

    <nextcloud-inline-search
      v-if="inlineSearch && documentationMode"
      :start-folder="startFolder"
      :folders="documentationFolders"
      :documentation-mode="documentationMode" />

    <Spinner v-show="loading" size="medium" :message="$t('apis/loading')" />

    <nextcloud-row
      v-for="folder in content.childs"
      :key="folder.fileid"
      :folder="folder"
      :is-admin="isAdmin === '1'"
      :show-flags-on-root="startFolder === '/TechDokuDMZ/'" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import axios from 'axios';
import { encode as base64Encode } from 'base-64';
import { useBoxStore, useModalStore } from '@assets/protected/vue/stores';

import NextcloudRow from './NextcloudRow.vue';
import NextcloudInlineSearch from './NextcloudInlineSearch.vue';
import Spinner from '@assets/protected/vue/component/helper/Spinner.vue';

const props = defineProps<{
  isAdmin?: string;
  startFolder?: string;
  openFolder?: string;
  inlineSearch?: boolean;
  documentationMode?: boolean;
  languages?: string[];
}>();

const boxStore = useBoxStore();
const modalStore = useModalStore();

const content = ref<any>({});
const documentationFolders = ref<any>({});
const loading = ref(true);

function loadContent(startFolder?: string, openFolder?: string) {
  let url = '/box/nextcloud/get' + (startFolder ?? '/');
  url = url.replace(/\/$/, '');

  axios
    .get(url)
    .then((response) => {
      content.value = response.data;
      loading.value = false;

      if (openFolder && openFolder !== '') {
        boxStore.setOpenFolder(base64Encode(openFolder));
      }
    })
    .catch((error) => {
      modalStore.showModal({ cssClass: 'error', message: error.response?.data?.message });
    });
}

function loadDocumentationFolders() {
  const url = '/box/nextcloud/getDocumentationFolders';

  axios
    .get(url)
    .then((response) => {
      documentationFolders.value = response.data;
    })
    .catch((error) => {
      modalStore.showModal({ cssClass: 'error', message: error.response?.data?.message });
    });
}

onMounted(() => {
  loadContent(props.startFolder, props.openFolder);

  if (props.documentationMode) {
    loadDocumentationFolders();
  }
});
</script>

<style lang="scss" scoped>
#box {
  width: 100%;
  margin: 10px 0;
  border-radius: 3px;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
  display: inline-block;
}
</style>

<style lang="scss">
#box {
  .vue-simple-spinner {
    margin-top: 20px !important;
  }
}
</style>
