<template>
  <div id="box">
    <box-inline-search
      v-if="inlineSearch"
      :start-folder="startFolder"
      :folders="content.childFolders"
      :languages="languages" />
    <box-row v-for="folder in content.childFolders" :key="folder.code" :folder="folder" :is-admin="isAdmin" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import Axios from 'axios';

import { useModalStore } from '@assets/protected/vue/stores';

import BoxRow from './BoxRow.vue';
import BoxInlineSearch from './BoxInlineSearch.vue';

interface Props {
  isAdmin: boolean;
  startFolder: string;
  inlineSearch: boolean;
  languages: string[];
}

const props = withDefaults(defineProps<Props>(), {
  isAdmin: false,
  startFolder: '/',
  inlineSearch: false,
  languages: () => [],
});

type Folder = { code: string } & Record<string, unknown>;

const modalStore = useModalStore();

const content = ref<{ childFolders: Folder[] }>({
  childFolders: [],
});

const loadContent = async (): Promise<void> => {
  const url = '/box/api/get' + props.startFolder;

  try {
    const response = await Axios.get(url);
    content.value = response.data;
  } catch (error) {
    const message =
      // @ts-expect-error — optional chaining across unknown error shapes
      error?.response?.data ?? 'Unknown error';
    modalStore.showModal({ cssClass: 'error', message });
  }
};

onMounted(loadContent);
</script>

<style lang="scss" scoped>
#box {
  width: 100%;
  margin: 10px 0;
  border-radius: 3px;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
  display: inline-block;
}
</style>
