<template>
  <card card-class="primary mt20">
    <template #title>
      <div class="container p0">
        <div class="row mb-2">
          <div class="col-12"><i class="fas fa-cloud"></i> {{ $t('box/dashboard/title') }}</div>
        </div>
        <div class="row pe-3">
          <div class="col-12 col-lg-4 pb-1 languageCol">
            <div class="dropdown">
              <button
                id="dropdownLanguage"
                class="btn btn-sm btn-block btn-light dropdown-toggle pe-5"
                data-toggle="dropdown"
                type="button"
                aria-haspopup="true"
                aria-expanded="false"
                v-html="languageFlag + language"></button>
              <div class="dropdown-menu" aria-labelledby="dropdownLanguage">
                <a
                  class="dropdown-item font-weight-bold"
                  @click="selectLanguage({ name: $t('box/search/all_languages'), tld: null })">
                  {{ $t('box/search/all_languages') }}
                </a>
                <a v-for="lang in languages" :key="lang.tld" class="dropdown-item" @click="selectLanguage(lang)">
                  <img class="flag" :src="lang.flag" />&nbsp;&nbsp;{{ lang.name }}
                </a>
              </div>
            </div>
          </div>

          <div class="col-12 col-lg-5 pb-1 timeCol">
            <div class="dropdown">
              <button
                id="dropdownTime"
                class="btn btn-sm btn-block btn-light dropdown-toggle"
                data-toggle="dropdown"
                type="button"
                aria-haspopup="true"
                aria-expanded="false"
                v-html="time"></button>
              <div class="dropdown-menu" aria-labelledby="dropdownTime">
                <a
                  v-for="(label, val) in timeOptions"
                  :key="val"
                  class="dropdown-item font-weight-bold"
                  @click="selectTime({ name: $t(label), value: val })">
                  {{ $t(label) }}
                </a>
              </div>
            </div>
          </div>

          <div class="col-12 col-lg-3 pb-1 limitCol">
            <div class="dropdown">
              <button
                id="dropdownlimit"
                class="btn btn-sm btn-block btn-light dropdown-toggle pe-5"
                data-toggle="dropdown"
                type="button"
                aria-haspopup="true"
                aria-expanded="false"
                v-html="limit"></button>
              <div class="dropdown-menu" aria-labelledby="dropdownlimit">
                <a
                  v-for="(label, val) in limitOptions"
                  :key="val"
                  class="dropdown-item font-weight-bold"
                  @click="selectLimit({ name: $t(label), value: val })">
                  {{ $t(label) }}
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>

    <template #body>
      <Spinner v-if="busy" size="medium" :message="$t('box/dashboard/loading')" />

      <ul v-else class="list-group recent-comments">
        <div v-if="loadingError" v-html="$t('box/dashboard/loadingError')"></div>
        <li v-for="file in filesArray" :key="file.fullpath" class="list-group-item clearfix">
          <a @click="openFile(file.fullpath)">
            <div class="container">
              <div class="row">
                <div class="col-10 filename">
                  <span v-html="file.filename + '.' + file.extension"></span>
                </div>
                <div class="col-2 flags">
                  <img v-for="lang in file.languages" :key="lang.flag" class="flag" :src="lang.flag" />
                </div>
              </div>
              <div class="row">
                <div class="col-12">
                  <span class="path" v-html="file.pathOnlyURLDecoded"></span>
                </div>
              </div>
            </div>
          </a>
        </li>
      </ul>
    </template>
  </card>
</template>

<script lang="ts" setup>
import { ref, onMounted, getCurrentInstance } from 'vue';
import axios from 'axios';
import Spinner from '@assets/protected/vue/component/helper/Spinner.vue';
import Card from '@assets/protected/vue/component/helper/Card.vue';
import { useModalStore } from '@assets/protected/vue/stores';

import flagBr from '@flags/png100px/br.png';
import flagDe from '@flags/png100px/de.png';
import flagEn from '@flags/png100px/gb.png';
import flagDk from '@flags/png100px/dk.png';
import flagNl from '@flags/png100px/nl.png';
import flagCz from '@flags/png100px/cz.png';
import flagFi from '@flags/png100px/fi.png';
import flagFr from '@flags/png100px/fr.png';
import flagGr from '@flags/png100px/gr.png';
import flagIt from '@flags/png100px/it.png';
import flagNo from '@flags/png100px/no.png';
import flagPl from '@flags/png100px/pl.png';
import flagSe from '@flags/png100px/se.png';
import flagEs from '@flags/png100px/es.png';
import flagTr from '@flags/png100px/tr.png';

const { proxy } = getCurrentInstance()!;
const t = proxy.$t;

const modalStore = useModalStore();

const busy = ref(false);
const loadingError = ref(false);
const filesArray = ref<any[]>([]);

const language = ref('');
const languageTld = ref<string | null>('');
const languageFlag = ref('');

const time = ref('');
const timeValue = ref<number>(1);
const limit = ref('');
const limitValue = ref<number>(5);

const languages = [
  { name: t('language/brazilian'), tld: 'br', flag: flagBr },
  { name: t('language/german'), tld: 'de', flag: flagDe },
  { name: t('language/english'), tld: 'en', flag: flagEn },
  { name: t('language/danish'), tld: 'dk', flag: flagDk },
  { name: t('language/dutch'), tld: 'nl', flag: flagNl },
  { name: t('language/czech'), tld: 'cz', flag: flagCz },
  { name: t('language/finnish'), tld: 'fi', flag: flagFi },
  { name: t('language/french'), tld: 'fr', flag: flagFr },
  { name: t('language/greek'), tld: 'gr', flag: flagGr },
  { name: t('language/italian'), tld: 'it', flag: flagIt },
  { name: t('language/norwegian'), tld: 'no', flag: flagNo },
  { name: t('language/polish'), tld: 'pl', flag: flagPl },
  { name: t('language/swedish'), tld: 'se', flag: flagSe },
  { name: t('language/spanish'), tld: 'es', flag: flagEs },
  { name: t('language/turkish'), tld: 'tr', flag: flagTr },
];

const timeOptions = {
  1: 'box/dashboard/lastWeek',
  2: 'box/dashboard/last2Weeks',
  3: 'box/dashboard/last3Weeks',
  4: 'box/dashboard/last4Weeks',
};

const limitOptions = {
  5: 'box/dashboard/show5',
  20: 'box/dashboard/show20',
  50: 'box/dashboard/show50',
  100: 'box/dashboard/show100',
  10000: 'box/dashboard/showAll',
};

function loadLatestFiles(lang: string | null, time: number, limit: number) {
  busy.value = true;
  axios
    .get(`/box/nextcloud/latestFiles/${lang}/${time}/${limit}`)
    .then((res) => {
      filesArray.value = res.data;
      loadingError.value = false;
    })
    .catch(() => {
      loadingError.value = true;
    })
    .finally(() => (busy.value = false));
}

function saveSettings() {
  busy.value = true;

  // TODO: Test parameters
  const params = new URLSearchParams();
  params.append('settings[language]', languageTld.value ?? '');
  params.append('settings[time]', timeValue.value.toString());
  params.append('settings[limit]', limitValue.value.toString());

  axios
    .post('/box/nextcloud/latestFiles/saveSettings', params, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    })
    .then(() => loadLatestFiles(languageTld.value, timeValue.value, limitValue.value))
    .catch((error) =>
      modalStore.showModal({
        cssClass: 'error',
        message: error.response.data,
      }),
    );
}

function loadSettings() {
  busy.value = true;
  axios
    .get('/box/nextcloud/latestFiles/loadSettings')
    .then((res) => {
      const data = res.data;
      const lang = languages.find((l) => l.tld === data.language);
      language.value = lang ? lang.name : t('box/search/all_languages');
      languageTld.value = data.language || '';
      languageFlag.value = lang && lang.flag ? `<img src="${lang.flag}" class="flag" />` : '';

      time.value = t(timeOptions[data.time]);
      timeValue.value = data.time;

      limit.value = t(limitOptions[data.limit]);
      limitValue.value = data.limit;

      loadLatestFiles(languageTld.value, timeValue.value, limitValue.value);
    })
    .catch((error) => modalStore.showModal({ cssClass: 'error', message: error.response.data }));
}

function selectLanguage(lang: any) {
  language.value = lang.name;
  languageTld.value = lang.tld;
  languageFlag.value = lang.tld ? `<img src="${lang.flag}" class="flag" />` : '';
  saveSettings();
}

function selectTime(t: any) {
  time.value = t.name;
  timeValue.value = t.value;
  saveSettings();
}

function selectLimit(l: any) {
  limit.value = l.name;
  limitValue.value = l.value;
  saveSettings();
}

function openFile(fullpath: string, target: string = '_blank') {
  window.open('/box/nextcloud/download' + fullpath, target);
}

onMounted(loadSettings);
</script>

<style lang="scss" scoped>
.languageCol,
.timeCol,
.limitCol {
  padding: 0 0 0 15px;
}
.dropdown button {
  height: 37px;
  background-color: #fff;
  border-color: #ced4da;
}
.flag {
  margin-top: -5px;
}
.btn {
  text-align: left;
}
li {
  padding: 0;
  &:hover {
    background-color: darken(#ffffff, 5%);
  }
  a {
    color: #333;
    &:hover {
      text-decoration: none;
      cursor: pointer;
    }
    .container {
      padding: 10px;
      .filename {
        font-size: 0.9rem;
      }
      .date {
        font-size: 0.7rem;
        margin: auto;
        text-align: right;
        padding-right: 15px;
      }
      .path {
        font-size: 0.7rem;
      }
      .flags {
        text-align: right;
        img {
          width: 20px;
          height: 12px;
          margin-left: 10px;
        }
      }
    }
  }
}
</style>
