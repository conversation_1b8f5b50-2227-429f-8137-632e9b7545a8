<template>
  <div id="sideSearch" class="sidebar-panel">
    <h5 class="sidebar-panel-title">
      <i class="fas fa-cloud-download-alt mr5"></i>{{ $t('box/download_mode/download_mode') }}
    </h5>

    <div class="content">
      <button
        v-if="!getDownloadMode"
        class="btn btn-sm btn-success btn-block"
        type="button"
        @click="toggleDownloadMode"
        v-html="$t('box/download_mode/activate')"></button>
      <button
        v-if="getDownloadMode"
        class="btn btn-sm btn-warning btn-block"
        type="button"
        @click="toggleDownloadMode"
        v-html="$t('box/download_mode/deactivate')"></button>

      <button
        v-if="getDownloadMode"
        class="btn btn-abus-blue-dark btn-sm btn-block mt10"
        @click="downloadZip"
        v-html="
          $t('box/download_mode/download_files') +
          '<br />(' +
          getSelectedForDownloadCount +
          ') - ' +
          $t('box/download_mode/ca') +
          ' ' +
          getDownloadSize +
          ' MB'
        "></button>

      <div v-if="getDownloadMode" class="info" v-html="$t('box/download_mode/info')"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import axios from 'axios';
import { saveAs } from 'file-saver';
import { useBoxStore, useModalStore } from '@assets/protected/vue/stores';

const boxStore = useBoxStore();
const modalStore = useModalStore();

const getDownloadMode = computed(() => boxStore.getDownloadMode);
const getDownloadSize = computed(() => boxStore.getDownloadSize);
const getSelectedForDownloadCount = computed(() => boxStore.getSelectedForDownloadCount);
const getSelectedForDownload = computed(() => boxStore.getSelectedForDownload);

const toggleDownloadMode = () => boxStore.toggleDownloadMode();

const downloadZip = async (): Promise<void> => {
  const url = '/box/api/downloadZip';

  const selected = getSelectedForDownload.value;
  const params = new URLSearchParams();

  const ids = Array.isArray(selected) ? selected : selected != null ? [selected] : [];
  ids.forEach((id) => params.append('files[]', String(id)));

  try {
    const response = await axios.post(url, params, {
      responseType: 'arraybuffer',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    });

    const blob = new Blob([response.data], { type: 'application/zip' });
    saveAs(blob, `abus_box_${Date.now()}.zip`);
  } catch (error: any) {
    const message = error?.response?.data ?? (error?.message ? String(error.message) : 'Unknown error');
    modalStore.showModal({ cssClass: 'error', message });
  }
};
</script>

<style lang="scss" scoped>
@import '@assets/scss/abstract/variables';

.info {
  background-color: $abus-creme;
  color: #000000;
  padding: 5px;
  margin-top: 10px;
  border-radius: 0.3em;
}
</style>
