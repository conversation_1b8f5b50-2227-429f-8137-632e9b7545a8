<template>
  <div>
    <div
      :id="id"
      class="col-md-12 boxRow"
      :class="{
        isDir: folder.isDir,
        isFile: folder.isFile,
        isPublic: folder.isPublic === true && isAdmin,
        notAccessible: folder.isPublic === null && isAdmin,
        highlight: highlight,
        selectedForDownload: getDownloadMode && selected,
      }">
      <div class="row clickable" @click="loadContent()">
        <div class="col-md-9 folderFileName" :style="'padding-left: ' + 10 * recursionLoop + 'px'">
          <div>
            <font-awesome-icon
              v-if="folder.isDir"
              :icon="content === false ? 'folder' : 'folder-open'"></font-awesome-icon>
            <!--<i v-if="folder.isDir" class="fas fa-folder" :data-icon="{ 'folder': content === false, 'folder-open': content !== false }" aria-hidden="true"></i>-->

            <div class="checkboxContainer">
              <div v-if="getDownloadMode && folder.isFile" class="custom-control custom-checkbox">
                <input
                  :id="'downloadMode_' + id"
                  class="custom-control-input"
                  type="checkbox"
                  :value="true"
                  :checked="isSelectedForDownload(folder.code)"
                  @click.stop.prevent />
                <label class="custom-control-label" :for="'downloadMode_' + id"></label>
              </div>
            </div>

            <span v-if="folder.isDir || (folder.isFile && !folder.show.fileExtension)" v-html="folder.name"></span>
            <span
              v-if="folder.show.fileExtension && folder.extension !== ''"
              v-html="folder.name + '.' + folder.extension"></span>

            <span v-if="isAdmin && folder.isFile" class="downloads">0 Downloads</span>
          </div>

          <Spinner v-show="loading" class="loadingIndicator" size="small" />
        </div>

        <div class="col-md-3">
          <!--<div v-if="isAdmin" class="adminButton" @click="showAdministration($event)">-->
          <!--<i class="fas fa-ellipsis-v"></i>-->
          <!--</div>-->

          <!--<div v-if="folder.show.copyLink" class="externalLink" @click="showDirectlink($event)">-->
          <!--<i class="far fa-external-link" :title="$t('box/frontend/show_link')"></i>-->
          <!--</div>-->

          <b-dropdown
            id="ddown1"
            class="m-md-2 actions"
            right
            size="xs"
            :text="$t('box/admin/dropdown/actions')"
            variant="abus-blue-dark">
            <b-dropdown-item @click="showAdministration($event)">
              <!--<i class="far fa-cogs"></i>-->
              {{ $t('box/admin/dropdown/settings') }}
            </b-dropdown-item>
            <b-dropdown-item>
              <!--<i class="far fa-external-link"></i>-->
              {{ $t('box/admin/dropdown/show_link') }}
            </b-dropdown-item>
            <b-dropdown-divider></b-dropdown-divider>
            <b-dropdown-item>
              <font-awesome-layers class="mr10" @click="uploadFile($event)">
                <font-awesome-icon :icon="['far', 'file']"></font-awesome-icon>
                <font-awesome-icon icon="arrow-alt-up" transform="shrink-6 right-10 down-10"></font-awesome-icon>
              </font-awesome-layers>
              {{ $t('box/admin/dropdown/upload_file') }}
            </b-dropdown-item>
            <b-dropdown-item>
              <font-awesome-layers
                class="mr10"
                @click="switchSubfolderCreationMode($event)"
                :title="$t('box/frontend/create_subfolder')">
                <font-awesome-icon :icon="['far', 'folder']"></font-awesome-icon>
                <font-awesome-icon icon="plus" transform="shrink-6 right-10 down-10"></font-awesome-icon>
              </font-awesome-layers>
              {{ $t('box/admin/dropdown/create_folder') }}
            </b-dropdown-item>
          </b-dropdown>

          <!--<div class="dropdown actions">-->
          <!--<button class="btn btn-secondary btn-xs dropdown-toggle" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" @click="dropdown($event)" ref="myButton">-->
          <!--Aktionen-->
          <!--</button>-->
          <!--<div class="dropdown-menu dropdown-menu-right">-->
          <!--<a v-if="isAdmin" class="dropdown-item" @click="showAdministration($event)">Settings</a>-->
          <!--<a v-if="folder.show.copyLink" class="dropdown-item" @click="showDirectlink($event)">Show link</a>-->
          <!--<div class="dropdown-divider"></div>-->
          <!--<a class="dropdown-item" href="#">Upload file</a>-->
          <!--<a class="dropdown-item" href="#">Create folder</a>-->
          <!--</div>-->
          <!--</div>-->

          <div class="flags">
            <img v-for="language in folder.language.languages" :key="language" :src="getLanguageFlagPath(language)" />
          </div>

          <div v-if="folder.isFile && folder.show.fileDate" class="date">
            <span v-html="formatDate(folder.date)"></span>
          </div>
        </div>
      </div>

      <div v-if="displayDirectlink">
        <div class="alert alert-info fade in folderLink" v-html="folder.urlpath"></div>
      </div>

      <div v-if="displayAdministration" class="row">
        <div class="col-md-12">
          <box-admin :folder="folder" :administration="administration"></box-admin>
        </div>
      </div>
    </div>

    <div v-show="createFolderMode" class="subFolderCreation">
      <div class="col-md-12 row" :style="'padding-left: ' + 10 * recursionLoop + 'px'">
        <div class="container">
          <div
            v-show="createFolderResponseMsg"
            class="alert"
            :class="createFolderSuccess ? 'alert-success' : 'alert-warning'">
            <button class="close" type="button" aria-hidden="true" @click="createFolderResponseMsg = null">×</button>
            <font-awesome-icon v-show="createFolderSuccess" class="mr10" icon="check"></font-awesome-icon>
            <font-awesome-icon
              v-show="false === createFolderSuccess"
              class="mr10"
              icon="exclamation-triangle"></font-awesome-icon>
            <strong>{{ createFolderResponseMsg }}</strong>
          </div>
        </div>

        <div class="col-md-8 row">
          <label class="col-3 col-form-label" for="subFolderNameInput">
            <!--<font-awesome-layers class="mr5">-->
            <!--<font-awesome-icon :icon="['far','folder']"></font-awesome-icon>-->
            <!--<font-awesome-icon icon="plus" transform="shrink-6 right-10 down-10"></font-awesome-icon>-->
            <!--</font-awesome-layers>-->

            {{ $t('box/frontend/create_subfolder') }}:
          </label>
          <div class="col-9">
            <input
              id="subFolderNameInput"
              v-model="subFolderName"
              class="form-control w-100"
              type="text"
              :placeholder="$t('box/frontend/subfolder_name')" />
          </div>
        </div>

        <div class="col-md-3">
          <button class="btn btn-primary" @click="createSubfolder($event)">{{ $t('box/frontend/create') }}</button>
          <button
            class="btn btn-secondary"
            @click="
              () => {
                subFolderName = '';
                switchSubfolderCreationMode($event);
              }
            ">
            {{ $t('box/frontend/abort') }}
          </button>
        </div>
      </div>
    </div>

    <box-row
      v-for="folder in childFolders"
      :key="folder.code"
      :folder="folder"
      :is-admin="isAdmin"
      :downloadMode="getDownloadMode"
      :recursionLoop="recursionLoop + 1" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, getCurrentInstance, nextTick, watch } from 'vue';
import Axios from 'axios';
import base64 from 'base-64';
import { FontAwesomeIcon, FontAwesomeLayers } from '@fortawesome/vue-fontawesome';
import Spinner from '@assets/protected/vue/component/helper/Spinner.vue';
import { useModalStore, useBoxStore } from '@assets/protected/vue/stores';
import { useScroll } from '@assets/protected/vue/composables/useScroll';
import { useDateHelpers } from '@assets/protected/vue/composables/useDateHelpers';
import BoxAdmin from './admin/BoxAdmin.vue';
import { useLanguageHelpers } from '@assets/protected/vue/composables/useLanguageHelpers';

const { proxy } = getCurrentInstance()!;
const t = proxy.$t;

const { getLanguageFlagPath } = useLanguageHelpers();
const { formatDate } = useDateHelpers();

const boxStore = useBoxStore();
const modalStore = useModalStore();

interface FolderShow {
  fileExtension: boolean;
  fileDate: boolean;
  copyLink?: boolean;
}

interface Folder {
  code: string;
  urlpath: string;
  isDir: boolean;
  isFile: boolean;
  isPublic: boolean | null;
  name: string;
  extension: string;
  show: FolderShow;
  language: { languages: string[] };
  date?: number | null; // unix seconds
}

const props = defineProps<{
  folder: Folder;
  isAdmin?: boolean;
  recursionLoop?: number;
}>();

const isAdmin = computed(() => props.isAdmin === true);
const recursionLoop = computed(() => (typeof props.recursionLoop === 'number' ? props.recursionLoop : 1));

const scroll = useScroll();
const loading = ref(false);
const content = ref<any | false>(false);
const administration = ref<any | false>(false);
const displayDirectlink = ref(false);
const displayAdministration = ref(false);
const highlight = ref(false);
const id = ref(base64.encode(props.folder.urlpath));
const selected = ref(false);

const subFolderName = ref('');
const createFolderResponseMsg = ref<string | null>(null);
const createFolderSuccess = ref(false);
const createFolderMode = ref(false);

const childFolders = computed(() => (content.value && content.value.childFolders ? content.value.childFolders : []));

const getDownloadMode = computed<boolean>(() => boxStore.getDownloadMode);
const isSelectedForDownload = (code: string): boolean => boxStore.isSelectedForDownload(code);
const addDownload = (payload: any) => boxStore.addDownload(payload);
const removeDownload = (payload: any) => boxStore.removeDownload(payload);

/**
 * Zeigt die Administration
 */
function showAdministration($event: Event): void {
  $event.preventDefault();
  $event.stopPropagation();

  const url = '/box/api/administration' + props.folder.urlpath;

  loading.value = true;
  Axios.get(url)
    .then((response) => {
      administration.value = response.data;
      loading.value = false;
      displayAdministration.value = !displayAdministration.value;
    })
    .catch((error) => {
      loading.value = false;
      modalStore.showModal({ cssClass: 'error', message: error.response?.data });
    });
}

/**
 * Zeigt den Direktlink
 */
function showDirectlink($event: Event): void {
  $event.preventDefault();
  $event.stopPropagation();
  displayDirectlink.value = !displayDirectlink.value;
}

/**
 * Lädt den Content in einem Folder
 *
 * @param {string} path
 */
function loadContent(path = ''): void {
  // Offenen Content wieder zuklappen
  if (path === '' && content.value !== false) {
    content.value = false;
    return;
  }

  if (content.value !== false) return;

  // Ordnerinhalt laden
  if (props.folder.isDir) {
    loading.value = true;
    const url = '/box/api/get' + props.folder.urlpath;

    Axios.get(url)
      .then((response) => {
        content.value = response.data;
        loading.value = false;
        // Ensure nested open after DOM update
        nextTickOpen(path);
      })
      .catch((error) => {
        loading.value = false;
        modalStore.showModal({ cssClass: 'error', message: error.response?.data });
      });
  } else {
    if (!getDownloadMode.value) {
      // Extensions die in einem neuen Fenster geöffnet werden können
      const extensions = ['pdf', 'jpg', 'jpeg', 'gif', 'png'];
      const target = extensions.indexOf(props.folder.extension) !== -1 ? '_blank' : '_self';
      window.open('/box' + props.folder.urlpath, target);
    } else {
      isSelectedForDownload(props.folder.code) ? removeDownload(props.folder) : addDownload(props.folder);
      selected.value = isSelectedForDownload(props.folder.code);
    }
  }
}

function nextTickOpen(path: string) {
  nextTick(() => {
    if (path !== '') boxStore.openFolderPath(path);
  });
}

function switchSubfolderCreationMode($event?: Event) {
  if ($event) {
    $event.preventDefault();
    $event.stopPropagation();
  }
  createFolderMode.value = !createFolderMode.value;
}

function createSubfolder($event?: Event): void {
  if ($event) {
    $event.preventDefault();
    $event.stopPropagation();
  }

  if (subFolderName.value.trim() !== '') {
    const params = new URLSearchParams();
    params.set('folderPath', props.folder.urlpath);
    params.set('folderName', subFolderName.value.trim());

    Axios.post('/box/api/createFolder', params)
      .then((response) => {
        createFolderResponseMsg.value = response.data.message;
        createFolderSuccess.value = true;
      })
      .catch((error) => {
        createFolderResponseMsg.value = error.response?.data?.message || t('common/error');
        createFolderSuccess.value = false;
      });
  }
}

function uploadFile($event: Event) {
  $event.preventDefault();
  $event.stopPropagation();

  console.log(props.folder.urlpath);

  //Axios...
}

// Watch for folder open events from the store
watch(
  () => boxStore.getOpenFolderTrigger,
  (path: string) => {
    if (!path) return;

    const escaped = props.folder.urlpath.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g, '\\$&');
    const regexp = new RegExp('^' + escaped);

    if (path.match(regexp)) {
      loadContent(path);
    }

    if (path === props.folder.urlpath) {
      highlight.value = true;
      scroll.scrollToBase64Element(path);
    }
  },
);

onMounted(() => {
  id.value = base64.encode(props.folder.urlpath);
  selected.value = isSelectedForDownload(props.folder.code);
});
</script>

<style lang="scss" scoped>
@import '@assets/scss/abstract/variables';

.boxRow {
  font-size: 0.9rem;
  border-bottom: 1px solid #e3e3e3;

  &.isDir {
    background-color: #ededed;

    &:hover {
      background-color: #f5f5f5;
    }
  }

  &.isFile {
    background-color: #ffffff;
  }

  &.isPublic {
    background-color: #dff0d8;

    &:hover {
      background-color: #e7efe3;
    }
  }

  &.notAccessible {
    background-color: #f2dede;

    &:hover {
      background-color: #efe8e8;
    }
  }

  &.highlight {
    background-color: $abus-blue-light;
    color: $white;

    .downloads {
      color: $white !important;
    }
  }

  &.selectedForDownload {
    background-color: $abus-yellow-light;
  }

  .clickable {
    cursor: pointer;
  }

  .folderFileName {
    line-height: 40px;

    i {
      margin-right: 5px;
    }

    div {
      float: left;
    }

    .loadingIndicator {
      margin: 12px 0 0 10px;
    }

    .downloads {
      font-size: 0.6em;
      padding-left: 10px;
      color: #797979;
      vertical-align: middle;
    }
  }

  .date,
  .upload,
  .flags {
    line-height: 40px;
    float: right;
    margin-right: 10px;
  }

  /*.externalLink,*/
  /*.adminButton {*/
  /*width: 15px;*/
  /*min-height: 1px;*/
  /*}*/

  .actions {
    float: right;
    top: 2px;
  }

  .flags {
    line-height: 38px;

    img {
      padding: 0 4px;
      height: 13px;
      width: 26px;
    }
  }

  .date {
    font-size: 0.8rem;
  }

  div.checkboxContainer {
    padding-top: 10px;
    height: 40px;
  }
}

.subFolderCreation {
  position: relative;
  padding: 10px 0;
}
</style>
