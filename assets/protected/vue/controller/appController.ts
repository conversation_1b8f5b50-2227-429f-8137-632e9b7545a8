import { createApp } from 'vue';
import { createPinia } from 'pinia';
import { createI18n } from 'vue-i18n';
import { createBootstrap } from 'bootstrap-vue-next/plugins/createBootstrap';

import { VTooltip, VClosePopper, Dropdown, Tooltip, Menu } from 'floating-vue';

import 'bootstrap/dist/css/bootstrap.css';
import 'bootstrap-vue-next/dist/bootstrap-vue-next.css';
import 'floating-vue/dist/style.css';

import Modal from '../component/helper/Modal.vue';
import ModalStoreDriven from '../component/helper/ModalStoreDriven.vue';
import ModalButton from '../component/helper/ModalButton.vue';
import Toast from '../component/helper/Toast.vue';
import SortTable from '@assets/protected/vue/component/helper/SortTable.vue';
import ListUsers from '../component/reporting/ListUsers.vue';
import UserMode from '../component/frame/UserMode.vue';
import ReportProblem from '../component/frame/ReportProblem.vue';
import Dashboard from '../component/frame/dashboard/Dashboard.vue';
import ActiveUsers from '../component/frame/ActiveUsers.vue';
import ApisFrontend from '../component/apis/ApisFrontend.vue';
import ApisBackend from '../component/apis/ApisBackend.vue';
import ApisBackendSidebar from '../component/apis/ApisBackendSidebar.vue';
import ApisBackendReceiverList from '../component/apis/ApisBackendReceiverList.vue';
import ApisCreate from '../component/apis/ApisCreate.vue';
import ApisSearch from '../component/apis/ApisSearch.vue';
import ApisSettings from '../component/apis/ApisSettings.vue';
import ChangePassword from '../component/frame/ChangePassword.vue';
import Profile from '../component/frame/Profile.vue';
import Settings from '../component/frame/Settings.vue';
import DeleteAccount from '../component/frame/DeleteAccount.vue';
import BoxFrontend from '../component/box/BoxFrontend.vue';
import BoxDownloadMode from '../component/box/BoxDownloadMode.vue';
import BoxAdminSidebar from '../component/box/admin/BoxAdminSidebar.vue';
import NextcloudAdminSidebar from '../component/box/admin/NextcloudAdminSidebar.vue';
import Nextcloud from '../component/box/Nextcloud.vue';
import NextcloudDownloadMode from '../component/box/NextcloudDownloadMode.vue';
import NewsCreate from '../component/news/NewsCreate.vue';
import News from '../component/news/News.vue';
import IsValidUsername from '../component/service/IsValidUsername.vue';
import CompanySuggestion from '../component/service/CompanySuggestion.vue';
import PwdGenerator from '../component/service/PwdGenerator.vue';
import Workflows from '../component/accessmanagment/Workflows.vue';
import Archive from '../component/accessmanagment/Archive.vue';
import AccountList from '../component/accessmanagment/AccountList.vue';
import Information from '../component/accessmanagment/Information.vue';
import CreateAbukonfis from '../component/accessmanagment/CreateAbukonfis.vue';
import DeleteAbukonfis from '../component/accessmanagment/DeleteAbukonfis.vue';
import Bookings from '../component/Appointment/Bookings.vue';
import BookingsFilter from '../component/Appointment/BookingsFilter.vue';

import de from '@translations/across/across.de_DE.json';
import en from '@translations/across/across.en_GB.json';
import fr from '@translations/across/across.fr_FR.json';
import es from '@translations/across/across.es_ES.json';

const i18n = createI18n({
  legacy: false,
  globalInjection: true,
  locale: document.documentElement.lang || 'de',
  fallbackLocale: 'de',
  messages: { de, en, fr, es },
});

const app = createApp({});

app.config.compilerOptions.delimiters = ['<%', '%>'];

app.use(createPinia());
app.use(i18n);
app.use(createBootstrap());

app.directive('tooltip', VTooltip);
app.directive('close-popper', VClosePopper);
app.component('VDropdown', Dropdown);
app.component('VTooltip', Tooltip);
app.component('VMenu', Menu);

app.component('vue-modal', Modal);
app.component('vue-modal-store', ModalStoreDriven);
app.component('ModalButton', ModalButton);
app.component('Toast', Toast);
app.component('UserMode', UserMode);
app.component('ReportProblem', ReportProblem);
app.component('Dashboard', Dashboard);
app.component('ActiveUsers', ActiveUsers);
app.component('ApisFrontend', ApisFrontend);
app.component('ApisBackend', ApisBackend);
app.component('ApisBackendSidebar', ApisBackendSidebar);
app.component('ApisBackendReceiverList', ApisBackendReceiverList);
app.component('ApisCreate', ApisCreate);
app.component('ApisSearch', ApisSearch);
app.component('ApisSettings', ApisSettings);
app.component('ChangePassword', ChangePassword);
app.component('Profile', Profile);
app.component('Settings', Settings);
app.component('DeleteAccount', DeleteAccount);
app.component('BoxFrontend', BoxFrontend);
app.component('BoxDownloadMode', BoxDownloadMode);
app.component('BoxAdminSidebar', BoxAdminSidebar);
app.component('NextcloudAdminSidebar', NextcloudAdminSidebar);
app.component('Nextcloud', Nextcloud);
app.component('NextcloudDownloadMode', NextcloudDownloadMode);
app.component('NewsCreate', NewsCreate);
app.component('News', News);
app.component('ListUsers', ListUsers);
app.component('SortTable', SortTable);
app.component('IsValidUsername', IsValidUsername);
app.component('CompanySuggestion', CompanySuggestion);
app.component('PwdGenerator', PwdGenerator);
app.component('Workflows', Workflows);
app.component('Archive', Archive);
app.component('AccountList', AccountList);
app.component('Information', Information);
app.component('CreateAbukonfis', CreateAbukonfis);
app.component('DeleteAbukonfis', DeleteAbukonfis);
app.component('Bookings', Bookings);
app.component('BookingsFilter', BookingsFilter);

const root = document.getElementById('appController');
if (root) {
  app.mount(root);
}

const iframe = document.getElementById('frame-iframe');
if (iframe && iframe.getAttribute('data-src')) {
  document.addEventListener(
    'DOMContentLoaded',
    function () {
      iframe!.setAttribute('src', iframe!.getAttribute('data-src')!);
      iframe!.removeAttribute('data-src');
    },
    false,
  );
}
