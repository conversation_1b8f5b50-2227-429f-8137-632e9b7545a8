import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import base64 from 'base-64';
import Axios from 'axios';
import { useI18n } from 'vue-i18n';
import type { DownloadFile } from './types';

import { useModalStore } from './useModalStore';

export const useBoxStore = defineStore('box', () => {
  const { t } = useI18n();

  const downloadMode = ref<boolean>(false);
  const selectedForDownload = ref<Record<string, any>>({});
  const downloadCount = ref<number>(0);
  const downloadSize = ref<number>(0);
  const openFolder = ref<string>('');
  const openFolderTrigger = ref<string>(''); // Used to trigger reactive updates

  const getDownloadMode = computed(() => downloadMode.value);
  const getDownloadSize = computed(() => (downloadSize.value / 1048576).toFixed(2));
  const getSelectedForDownload = computed(() => selectedForDownload.value);
  const getSelectedForDownloadCount = computed(() => downloadCount.value);
  const isSelectedForDownload = computed(
    () => (fileid: string) => Object.prototype.hasOwnProperty.call(selectedForDownload.value, fileid),
  );
  const getOpenFolder = computed(() => openFolder.value);
  const getOpenFolderTrigger = computed(() => openFolderTrigger.value);

  const setDownloadMode = (payload: boolean) => {
    downloadMode.value = payload;
    if (payload === false) {
      selectedForDownload.value = {};
      downloadSize.value = 0;
      downloadCount.value = 0;
    }
  };

  const toggleDownloadMode = () => {
    setDownloadMode(!downloadMode.value);
  };

  const addDownload = (payload: DownloadFile) => {
    downloadSize.value += Number(payload.size);
    selectedForDownload.value[payload.fileid] = payload;
    downloadCount.value++;
  };

  const removeDownload = (payload: DownloadFile) => {
    downloadSize.value -= Number(payload.size);
    delete selectedForDownload.value[payload.fileid];
    downloadCount.value--;
  };

  const setOpenFolder = (payload: string) => {
    const pathParts = base64
      .decode(payload)
      .split('/')
      .map((part) =>
        encodeURIComponent(part)
          .replace(/!/g, '%21')
          .replace(/'/g, '%27')
          .replace(/\(/g, '%28')
          .replace(/\)/g, '%29')
          .replace(/\*/g, '%2A'),
      );
    openFolder.value = pathParts.join('/');
  };

  const openFolderPath = (path: string) => {
    openFolderTrigger.value = path;
  };

  const createTagTask = async () => {
    const modalStore = useModalStore();
    const url = '/taskrunner/create';
    const form = new URLSearchParams();
    form.set('bundleName', 'ABUSBox');
    form.set('task', 'nextcloudTags');

    try {
      await Axios.post(url, form, {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      });

      modalStore.showModal({
        cssClass: 'abus-blue-dark',
        title: t('box/admin/tags') as string,
        message: t('box/admin/tags_modal_text') as string,
      });
    } catch (error: any) {
      modalStore.showModal({
        cssClass: 'error',
        message: error?.response?.data ?? String(error),
      });
    }
  };

  const createIndexTask = async (payload: any) => {
    const modalStore = useModalStore();
    const url = '/taskrunner/create';
    const form = new URLSearchParams();
    form.set('bundleName', 'ABUSBox');
    form.set('task', 'nextcloudIndex');
    form.set('parameters', typeof payload === 'string' ? payload : JSON.stringify(payload));

    try {
      await Axios.post(url, form, {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      });

      modalStore.showModal({
        cssClass: 'abus-blue-dark',
        title: t('box/admin/index') as string,
        message: t('box/admin/index_modal_text') as string,
      });
    } catch (error: any) {
      modalStore.showModal({
        cssClass: 'error',
        message: error?.response?.data ?? String(error),
      });
    }
  };

  return {
    downloadMode,
    selectedForDownload,
    downloadCount,
    downloadSize,
    openFolder,
    openFolderTrigger,
    getDownloadMode,
    getDownloadSize,
    getSelectedForDownload,
    getSelectedForDownloadCount,
    isSelectedForDownload,
    getOpenFolder,
    getOpenFolderTrigger,
    setDownloadMode,
    toggleDownloadMode,
    addDownload,
    removeDownload,
    setOpenFolder,
    openFolderPath,
    createTagTask,
    createIndexTask,
  };
});
