import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

// Types for APIS store
export interface SidebarData {
  section: string;
  showIndexButton: boolean;
  showBackendIndexButton: boolean;
  showNewInformationButton: boolean;
  requestTranslationButton?: string;
  requestPublishingButton?: string;
  publishButton?: string;
  rejectButton?: string;
}

export interface SearchData {
  query: string;
  fromDate: string;
  untilDate: string;
  filter: Record<string, any>;
}

export interface FilterData {
  filter: string;
  byBadge?: boolean;
}

export interface ShowFavoritesData {
  showFavorites: boolean;
}

export interface ShowSettingsData {
  section: string;
  categories: any;
}

export interface ShowReceiverListData {
  section: string;
  receivers: Record<string, any>;
}

export interface ContentBlockData {
  [key: string]: string;
}

export interface GlobalAttachmentData {
  [key: string]: any;
}

export const useApisStore = defineStore('apis', () => {
  const sidebarData = ref<SidebarData | null>(null);
  const searchTrigger = ref<SearchData | null>(null);
  const filterTrigger = ref<FilterData | null>(null);
  const favoritesTrigger = ref<ShowFavoritesData | null>(null);
  const settingsTrigger = ref<ShowSettingsData | null>(null);
  const receiverListTrigger = ref<ShowReceiverListData | null>(null);
  const saveTrigger = ref<boolean>(false);
  const transitionTrigger = ref<string>('');
  const contentBlockTrigger = ref<ContentBlockData | null>(null);
  const globalAttachmentAddTrigger = ref<GlobalAttachmentData | null>(null);
  const globalAttachmentRemoveTrigger = ref<GlobalAttachmentData | null>(null);

  const getSidebarData = computed(() => sidebarData.value);
  const getSearchTrigger = computed(() => searchTrigger.value);
  const getFilterTrigger = computed(() => filterTrigger.value);
  const getFavoritesTrigger = computed(() => favoritesTrigger.value);
  const getSettingsTrigger = computed(() => settingsTrigger.value);
  const getReceiverListTrigger = computed(() => receiverListTrigger.value);
  const getSaveTrigger = computed(() => saveTrigger.value);
  const getTransitionTrigger = computed(() => transitionTrigger.value);
  const getContentBlockTrigger = computed(() => contentBlockTrigger.value);
  const getGlobalAttachmentAddTrigger = computed(() => globalAttachmentAddTrigger.value);
  const getGlobalAttachmentRemoveTrigger = computed(() => globalAttachmentRemoveTrigger.value);

  const setSidebar = (data: SidebarData) => {
    sidebarData.value = data;
  };

  const triggerSearch = (data: SearchData) => {
    searchTrigger.value = data;
  };

  const setFilter = (data: FilterData) => {
    filterTrigger.value = data;
  };

  const showFavorites = (data: ShowFavoritesData) => {
    favoritesTrigger.value = data;
  };

  const showSettings = (data: ShowSettingsData) => {
    settingsTrigger.value = data;
  };

  const showReceiverList = (data: ShowReceiverListData) => {
    receiverListTrigger.value = data;
  };

  const triggerSave = () => {
    saveTrigger.value = !saveTrigger.value; // Toggle to trigger reactivity
  };

  const triggerTransition = (transition: string) => {
    transitionTrigger.value = transition;
  };

  const synchronizeContentBlock = (data: ContentBlockData) => {
    contentBlockTrigger.value = data;
  };

  const addGlobalAttachment = (data: GlobalAttachmentData) => {
    globalAttachmentAddTrigger.value = data;
  };

  const removeGlobalAttachment = (data: GlobalAttachmentData) => {
    globalAttachmentRemoveTrigger.value = data;
  };

  // Reset functions to clear triggers after handling
  const resetSearchTrigger = () => {
    searchTrigger.value = null;
  };

  const resetFilterTrigger = () => {
    filterTrigger.value = null;
  };

  const resetFavoritesTrigger = () => {
    favoritesTrigger.value = null;
  };

  const resetSettingsTrigger = () => {
    settingsTrigger.value = null;
  };

  const resetReceiverListTrigger = () => {
    receiverListTrigger.value = null;
  };

  const resetTransitionTrigger = () => {
    transitionTrigger.value = '';
  };

  const resetContentBlockTrigger = () => {
    contentBlockTrigger.value = null;
  };

  const resetGlobalAttachmentAddTrigger = () => {
    globalAttachmentAddTrigger.value = null;
  };

  const resetGlobalAttachmentRemoveTrigger = () => {
    globalAttachmentRemoveTrigger.value = null;
  };

  return {
    sidebarData,
    searchTrigger,
    filterTrigger,
    favoritesTrigger,
    settingsTrigger,
    receiverListTrigger,
    saveTrigger,
    transitionTrigger,
    contentBlockTrigger,
    globalAttachmentAddTrigger,
    globalAttachmentRemoveTrigger,
    getSidebarData,
    getSearchTrigger,
    getFilterTrigger,
    getFavoritesTrigger,
    getSettingsTrigger,
    getReceiverListTrigger,
    getSaveTrigger,
    getTransitionTrigger,
    getContentBlockTrigger,
    getGlobalAttachmentAddTrigger,
    getGlobalAttachmentRemoveTrigger,
    setSidebar,
    triggerSearch,
    setFilter,
    showFavorites,
    showSettings,
    showReceiverList,
    triggerSave,
    triggerTransition,
    synchronizeContentBlock,
    addGlobalAttachment,
    removeGlobalAttachment,
    resetSearchTrigger,
    resetFilterTrigger,
    resetFavoritesTrigger,
    resetSettingsTrigger,
    resetReceiverListTrigger,
    resetTransitionTrigger,
    resetContentBlockTrigger,
    resetGlobalAttachmentAddTrigger,
    resetGlobalAttachmentRemoveTrigger,
  };
});
