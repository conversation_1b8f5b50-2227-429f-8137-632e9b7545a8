# Pinia Store Migration

This directory contains the migrated Pinia stores that replace the previous Vuex store system.

## Migration Overview

The migration from Vuex to Pinia includes the following changes:

### Store Structure

- **Vuex modules** → **Pinia stores**
- **Namespaced modules** → **Individual store files**
- **Mutations/Actions pattern** → **Direct state modification and actions**
- **Getters** → **Computed properties**

### Available Stores

1. **useModalStore** - Manages modal dialogs and notifications
2. **useBoxStore** - Handles file download functionality and box operations
3. **useServicesStore** - Manages company/service related state
4. **useNewsStore** - Complex news management with content, attachments, and publishing
5. **useBookingsStore** - Booking system state and operations

### Usage Examples

#### Basic Store Usage

```typescript
import { useModalStore } from '@assets/protected/vue/stores';

// In a Vue component with <script setup>
const modalStore = useModalStore();

// Show a modal
modalStore.showModal({
  cssClass: 'info',
  title: 'Information',
  message: 'This is an info message',
});

// Access state
const isModalVisible = modalStore.getShow;
```

#### Using i18n in Stores

```typescript
// Inside a Pinia store
import { useI18n } from 'vue-i18n';

export const useExampleStore = defineStore('example', () => {
  const { t } = useI18n();

  const showLocalizedModal = () => {
    modalStore.showModal({
      title: t('modal/title'),
      message: t('modal/message'),
    });
  };
});
```

#### Multiple Stores

```typescript
import { 
  useModalStore, 
  useBoxStore, 
  useNewsStore 
} from '@assets/protected/vue/stores';

const modalStore = useModalStore();
const boxStore = useBoxStore();
const newsStore = useNewsStore();

// Use stores as needed
boxStore.toggleDownloadMode();
newsStore.setDraft(false);
```

### Migration Benefits

1. **Better TypeScript Support** - Full type safety with TypeScript interfaces
2. **Simpler API** - No more mutations, direct state modification
3. **Better DevTools** - Enhanced debugging experience
4. **Composition API Integration** - Native Vue 3 Composition API support
5. **Tree Shaking** - Better bundle optimization
6. **Modern vue-i18n** - Updated to use the latest vue-i18n with `useI18n()` composable

### Backward Compatibility

During the migration period, both Vuex and Pinia stores coexist. The legacy Vuex store is still available but should not be used for new development.

### Testing

A test component `StoreTest.vue` is available to verify all stores work correctly. It can be used by adding `<StoreTest />` to any template.

### Type Safety

All stores use TypeScript interfaces defined in `types.ts` for better type safety and IDE support.

### Cross-Store Dependencies

Some stores (like `useNewsStore`) depend on other stores (like `useModalStore`). These dependencies are properly handled within the store definitions.

## Migration Checklist

- [x] Convert modal store module to Pinia
- [x] Convert box store module to Pinia  
- [x] Convert services store module to Pinia
- [x] Convert bookings store module to Pinia
- [x] Convert news store module to Pinia
- [x] Update ModalStoreDriven component
- [x] Update BoxAdminSidebar component
- [x] Update all box-related components
- [x] Update all news-related components
- [x] Update all booking-related components
- [x] Create TypeScript types and interfaces
- [x] Create store index file for easy imports
- [x] Update documentation
- [x] Create test component

## Next Steps

1. Test all functionality thoroughly
2. Remove legacy Vuex store once migration is complete
3. Update any remaining components that might still use Vuex
4. Consider adding unit tests for individual stores
