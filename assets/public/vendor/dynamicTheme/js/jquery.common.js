import { isMobileSafari } from '@assets/public/helper/featureDetect';

$(document).ready(function () {
  //$('[data-toggle=tooltip]').tooltip({container:'body'});
  $('[data-toggle="tooltip"]').tooltip();
  //$('[data-toggle=popover]').popover ();

  let checkAll = $('input[type="checkbox"].check-all');
  checkAll.click(function () {
    checkAll
      .parentsUntil('form-group')
      .find('.children input[type="checkbox"]')
      .each(function () {
        $(this).prop('checked', !$(this).prop('checked'));
      });
  });

  // Cards Toggle
  $('.card:not(.vue-card) .card-header .card-controls a').closest('.card-header').addClass('clickable');

  $('.card:not(.vue-card) .card-header').click(function (e) {
    let toggle = $(this).find('.card-controls:first a.toggle');

    if (toggle) {
      e.preventDefault();
      let thisIcon = toggle.find('i');
      let thisCard = $(this).closest('.card');

      thisIcon.toggleClass('fa-angle-up fa-angle-down');
      thisCard.toggleClass('card-closed', 150);
    }
  });

  isMobileSafari();
});

// Page refresh immer Scroll Up. Wenn man das so macht, klappt das auch, wenn ein Video im Content ist
$(window).on('beforeunload', function () {
  $(window).scrollTop(0);
});
