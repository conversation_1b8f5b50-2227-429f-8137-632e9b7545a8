version: '2'

services:
  nextcloud_db:
    extends:
      file: common.yml
      service: nextcloud_db
    restart: always
    environment:
      - MYSQL_ROOT_PASSWORD=mariapwd
      - MYSQL_PASSWORD=Vwdylutoguznbsf2
      - MYSQL_DATABASE=nextcloud
      - MYSQL_USER=nextcloud

  nextcloud_fpm:
    extends:
      file: common.yml
      service: nextcloud_fpm
    restart: always
    links:
      - nextcloud_db
    depends_on:
      - nextcloud_db
    build:
      args:
        development: 1
        cert: config/serverRootCA_max.pem
        http_proxy: http://*********:3128
        https_proxy: tcp://*********:3128
    environment:
      - MYSQL_PASSWORD=Vwdylutoguznbsf2
      - MYSQL_DATABASE=nextcloud
      - MYSQL_USER=nextcloud
      - MYSQL_HOST=nextcloud_db
      - NEXTCLOUD_ADMIN_USER=admin
      - NEXTCLOUD_ADMIN_PASSWORD=Sumeogqovsrmiuo8
      - NEXTCLOUD_DATA_DIR=/var/www/data
      - NO_PROXY=elasticsearch
    extra_hosts:
      - "app-2-dmz.abus-vpn.de:************"

  nextcloud_nginx:
    extends:
      file: common.yml
      service: nextcloud_nginx
    restart: always
    build:
      args:
        nginxconf: nginx_dev_max.conf
        cert: config/serverRootCA_max.pem
        http_proxy: http://*********:3128
        https_proxy: http://*********:3128

    links:
      - nextcloud_fpm
    depends_on:
      - nextcloud_fpm
    volumes_from:
      - nextcloud_fpm
    labels:
      - "traefik.http.routers.nextcloud-secure.rule=Host(`nextcloud.development.max`)"

networks:
  nextcloud-net:
  portal-net:
    external: true
  traefik-net:
    external: true
  database-net:
    external: true
  elasticsearch-net:
    external: true
