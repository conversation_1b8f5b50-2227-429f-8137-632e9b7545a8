{"composer/ca-bundle": {"version": "1.2.6"}, "doctrine/annotations": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "a2759dd6123694c8d901d0ec80006e044c2e6457"}, "files": ["config/routes/annotations.yaml"]}, "doctrine/cache": {"version": "1.10.0"}, "doctrine/collections": {"version": "1.6.4"}, "doctrine/common": {"version": "2.12.0"}, "doctrine/dbal": {"version": "v2.10.1"}, "doctrine/deprecations": {"version": "1.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "87424683adc81d7dc305eefec1fced883084aab9"}}, "doctrine/doctrine-bundle": {"version": "1.12", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.12", "ref": "28a0beb09b45a97111e43bb2c0e652aa83d44981"}, "files": ["config/packages/doctrine.yaml", "config/packages/prod/doctrine.yaml", "src/Entity/.gitignore", "src/Repository/.gitignore"]}, "doctrine/doctrine-cache-bundle": {"version": "1.4.0"}, "doctrine/doctrine-migrations-bundle": {"version": "3.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.1", "ref": "ee609429c9ee23e22d6fa5728211768f51ed2818"}, "files": ["config/packages/doctrine_migrations.yaml", "migrations/.gitignore"]}, "doctrine/event-manager": {"version": "1.1.0"}, "doctrine/inflector": {"version": "1.3.1"}, "doctrine/instantiator": {"version": "1.3.0"}, "doctrine/lexer": {"version": "1.2.0"}, "doctrine/migrations": {"version": "2.2.1"}, "doctrine/orm": {"version": "v2.7.0"}, "doctrine/persistence": {"version": "1.3.6"}, "doctrine/reflection": {"version": "v1.1.0"}, "egulias/email-validator": {"version": "2.1.15"}, "firebase/php-jwt": {"version": "v5.2.0"}, "friendsofphp/php-cs-fixer": {"version": "3.75", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.0", "ref": "be2103eb4a20942e28a6dd87736669b757132435"}, "files": [".php-cs-fixer.dist.php"]}, "graylog2/gelf-php": {"version": "1.6.4"}, "guzzlehttp/guzzle": {"version": "6.5.2"}, "guzzlehttp/promises": {"version": "v1.3.1"}, "guzzlehttp/psr7": {"version": "1.6.1"}, "jdorn/sql-formatter": {"version": "v1.2.17"}, "knplabs/knp-snappy": {"version": "v1.2.1"}, "knplabs/knp-snappy-bundle": {"version": "1.10", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.5", "ref": "c81bdcf4a9d4e7b1959071457f9608631865d381"}, "files": ["config/packages/knp_snappy.yaml"]}, "league/html-to-markdown": {"version": "4.9.1"}, "mainick/keycloak-client-bundle": {"version": "2.2.3"}, "mobiledetect/mobiledetectlib": {"version": "2.8.32"}, "monolog/monolog": {"version": "1.25.3"}, "myclabs/deep-copy": {"version": "1.9.5"}, "nikic/php-parser": {"version": "v4.0.3"}, "ocramius/proxy-manager": {"version": "2.7.0"}, "paragonie/constant_time_encoding": {"version": "v2.2.3"}, "php": {"version": "7.4"}, "phpdocumentor/reflection-common": {"version": "2.0.0"}, "phpdocumentor/reflection-docblock": {"version": "4.3.4"}, "phpdocumentor/type-resolver": {"version": "1.0.1"}, "psr/cache": {"version": "1.0.1"}, "psr/container": {"version": "1.0.0"}, "psr/http-message": {"version": "1.0.1"}, "psr/log": {"version": "1.1.2"}, "ralouphie/getallheaders": {"version": "2.0.5"}, "sabre/dav": {"version": "3.2.3"}, "sabre/event": {"version": "3.0.0"}, "sabre/http": {"version": "v4.2.4"}, "sabre/uri": {"version": "1.2.1"}, "sabre/vobject": {"version": "4.2.2"}, "sabre/xml": {"version": "1.5.1"}, "sebastian/code-unit-reverse-lookup": {"version": "1.0.1"}, "sebastian/comparator": {"version": "2.1.3"}, "sebastian/diff": {"version": "2.0.1"}, "sebastian/environment": {"version": "3.1.0"}, "sebastian/exporter": {"version": "3.1.2"}, "sebastian/global-state": {"version": "2.0.0"}, "sebastian/object-enumerator": {"version": "3.0.3"}, "sebastian/object-reflector": {"version": "1.1.1"}, "sebastian/recursion-context": {"version": "3.0.0"}, "sebastian/resource-operations": {"version": "1.0.0"}, "sebastian/version": {"version": "2.0.1"}, "smalot/pdfparser": {"version": "v0.11"}, "symfony/asset": {"version": "v4.4.4"}, "symfony/browser-kit": {"version": "v4.4.4"}, "symfony/cache": {"version": "v4.4.4"}, "symfony/cache-contracts": {"version": "v1.1.7"}, "symfony/config": {"version": "v4.4.4"}, "symfony/console": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.4", "ref": "fd5340d07d4c90504843b53da41525cf42e31f5c"}, "files": ["bin/console", "config/bootstrap.php"]}, "symfony/debug": {"version": "v4.4.4"}, "symfony/dependency-injection": {"version": "v4.4.4"}, "symfony/doctrine-bridge": {"version": "v4.4.4"}, "symfony/dom-crawler": {"version": "v4.4.4"}, "symfony/dotenv": {"version": "v4.4.4"}, "symfony/error-handler": {"version": "v4.4.2"}, "symfony/event-dispatcher": {"version": "v4.4.4"}, "symfony/event-dispatcher-contracts": {"version": "v1.1.7"}, "symfony/expression-language": {"version": "v4.4.4"}, "symfony/filesystem": {"version": "v4.4.4"}, "symfony/finder": {"version": "v4.4.4"}, "symfony/flex": {"version": "1.19", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "146251ae39e06a95be0fe3d13c807bcf3938b172"}, "files": [".env"]}, "symfony/form": {"version": "v4.4.4"}, "symfony/framework-bundle": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.4", "ref": "24eb45d1355810154890460e6a05c0ca27318fe7"}, "files": ["config/bootstrap.php", "config/packages/cache.yaml", "config/packages/framework.yaml", "config/packages/test/framework.yaml", "config/preload.php", "config/routes/dev/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php"]}, "symfony/http-client": {"version": "v4.4.31"}, "symfony/http-client-contracts": {"version": "v2.4.0"}, "symfony/http-foundation": {"version": "v4.4.4"}, "symfony/http-kernel": {"version": "v4.4.4"}, "symfony/inflector": {"version": "v4.4.4"}, "symfony/intl": {"version": "v4.4.4"}, "symfony/ldap": {"version": "v4.4.4"}, "symfony/mailer": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.3", "ref": "09051cfde49476e3c12cd3a0e44289ace1c75a4f"}, "files": ["config/packages/mailer.yaml"]}, "symfony/maker-bundle": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "fadbfe33303a76e25cb63401050439aa9b1a9c7f"}}, "symfony/mime": {"version": "v4.3.8"}, "symfony/monolog-bridge": {"version": "v4.4.4"}, "symfony/monolog-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "2120e71a370db3a494b8afcc42d7cfb2cff6f910"}, "files": ["config/packages/dev/monolog.yaml", "config/packages/prod/deprecations.yaml", "config/packages/prod/monolog.yaml", "config/packages/test/monolog.yaml"]}, "symfony/options-resolver": {"version": "v4.4.4"}, "symfony/phpunit-bridge": {"version": "4.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.1", "ref": "2ba17ea3190d76e6e37a52378f6bca3d84891bc5"}, "files": [".env.test", "bin/phpunit", "phpunit.xml.dist", "tests/bootstrap.php"]}, "symfony/polyfill-ctype": {"version": "v1.13.1"}, "symfony/polyfill-intl-icu": {"version": "v1.13.1"}, "symfony/polyfill-intl-idn": {"version": "v1.12.0"}, "symfony/polyfill-mbstring": {"version": "v1.13.1"}, "symfony/polyfill-php72": {"version": "v1.13.1"}, "symfony/polyfill-php80": {"version": "v1.23.1"}, "symfony/process": {"version": "v4.4.4"}, "symfony/property-access": {"version": "v4.4.4"}, "symfony/property-info": {"version": "v4.4.4"}, "symfony/routing": {"version": "4.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.2", "ref": "683dcb08707ba8d41b7e34adb0344bfd68d248a7"}, "files": ["config/packages/prod/routing.yaml", "config/packages/routing.yaml", "config/routes.yaml"]}, "symfony/security-bundle": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.4", "ref": "7b4408dc203049666fe23fabed23cbadc6d8440f"}, "files": ["config/packages/security.yaml"]}, "symfony/security-core": {"version": "v4.2.1"}, "symfony/security-csrf": {"version": "v4.2.1"}, "symfony/security-http": {"version": "v4.2.1"}, "symfony/serializer": {"version": "v4.4.4"}, "symfony/service-contracts": {"version": "v2.0.1"}, "symfony/stopwatch": {"version": "v4.4.4"}, "symfony/translation": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "2ad9d2545bce8ca1a863e50e92141f0b9d87ffcd"}, "files": ["config/packages/translation.yaml", "translations/.gitignore"]}, "symfony/translation-contracts": {"version": "v1.1.7"}, "symfony/twig-bridge": {"version": "v4.4.4"}, "symfony/twig-bundle": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.4", "ref": "73baff3f7b3cea12a73812a7cfd2c0924a9e250f"}, "files": ["config/packages/test/twig.yaml", "config/packages/twig.yaml", "templates/base.html.twig"]}, "symfony/validator": {"version": "4.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.3", "ref": "3eb8df139ec05414489d55b97603c5f6ca0c44cb"}, "files": ["config/packages/test/validator.yaml", "config/packages/validator.yaml"]}, "symfony/var-dumper": {"version": "v4.4.4"}, "symfony/var-exporter": {"version": "v4.2.1"}, "symfony/web-profiler-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "6bdfa1a95f6b2e677ab985cd1af2eae35d62e0f6"}, "files": ["config/packages/dev/web_profiler.yaml", "config/packages/test/web_profiler.yaml", "config/routes/dev/web_profiler.yaml"]}, "symfony/webpack-encore-bundle": {"version": "1.15", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.9", "ref": "f466b6384e592cfda11ca2361bbf7c7287b58f55"}, "files": ["assets/app.js", "assets/bootstrap.js", "assets/controllers.json", "assets/controllers/hello_controller.js", "assets/styles/app.css", "config/packages/webpack_encore.yaml", "package.json", "webpack.config.js"]}, "symfony/workflow": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "3b2f8ca32a07fcb00f899649053943fa3d8bbfb6"}, "files": ["config/packages/workflow.yaml"]}, "symfony/yaml": {"version": "v4.4.4"}, "twig/extra-bundle": {"version": "v3.20.0"}, "twig/twig": {"version": "v2.12.3"}, "webmozart/assert": {"version": "1.6.0"}}