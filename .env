# In all environments, the following files are loaded if they exist,
# the latter taking precedence over the former:
#
#  * .env                contains default values for the environment variables needed by the app
#  * .env.local          uncommitted file with local overrides
#  * .env.$APP_ENV       committed environment-specific defaults
#  * .env.$APP_ENV.local uncommitted environment-specific overrides
#
# Real environment variables win over .env files.
#
# DO NOT DEFINE PRODUCTION SECRETS IN THIS FILE NOR IN ANY OTHER COMMITTED FILES.
# https://symfony.com/doc/current/configuration/secrets.html
#
# Run "composer dump-env prod" to compile .env files for production use (requires symfony/flex >=1.2).
# https://symfony.com/doc/current/best_practices.html#use-environment-variables-for-infrastructure-configuration

###> symfony/framework-bundle ###
APP_ENV=dev
APP_DEBUG=true
APP_SECRET=
#TRUSTED_PROXIES=*********/8,10.0.0.0/8,**********/12,***********/16
#TRUSTED_HOSTS='^(localhost|example\.com)$'
###< symfony/framework-bundle ###

###> doctrine/doctrine-bundle ###
# Format described at https://www.doctrine-project.org/projects/doctrine-dbal/en/latest/reference/configuration.html#connecting-using-a-url
# IMPORTANT: You MUST configure your server version, either here or in config/packages/doctrine.yaml
#
# DATABASE_URL="sqlite:///%kernel.project_dir%/var/data.db"
# DATABASE_URL="mysql://app:!ChangeMe!@127.0.0.1:3306/db_name?serverVersion=8"
DATABASE_URL="postgresql://app:!ChangeMe!@127.0.0.1:5432/db_name?serverVersion=14&charset=utf8"
###< doctrine/doctrine-bundle ###

DOMAIN=abus-kransysteme.de
DEVELOPER_MAIL=<EMAIL>

COOKIE_DOMAIN=abus-kransysteme.de
COOKIE_SECURE=false

NEXTCLOUD_CAD_EXCHANGE_FOLDER=WebDavDMZ/3DProduktmodell
NEXTCLOUD_CAD_EXCHANGE_EMAIL=
NEXTCLOUD_CAD_EXCHANGE_USER=
NEXTCLOUD_CAD_EXCHANGE_PASSWORD=
NEXTCLOUD_CAD_EXCHANGE_DELETE_AFTER_SECONDS=2419200

SYMFONY_DECRYPTION_SECRET=

LDAP_READ_PASSWORD=
LDAP_WRITE_PASSWORD=
DN_USERS=
DN_COMPANIES=
DN_GROUPS=
DN_QUERY_PAGESIZE=5000

PORTAL_DATABASE_PASSWORD=
TEAMROOM_DATABASE_PASSWORD=
SALES_DATABASE_PASSWORD=
INFORMATION_DATABASE_PASSWORD=
SERVICE_DATABASE_PASSWORD=
MONTAGE_DATABASE_PASSWORD=
APPLICATION_DATABASE_PASSWORD=
APPLICATION_REMOTE_DATABASE_PASSWORD=
TDATA_DATABASE_PASSWORD=
TDATA_PREVIEW_DATABASE_PASSWORD=
WEBSITE_DATABASE_PASSWORD=
MEDIA_DATABASE_PASSWORD=
BOX_DATABASE_PASSWORD=
CATALOG_DATABASE_PASSWORD=
CATALOG2020_DATABASE_PASSWORD=

PIMCORE_DATABASE_HOST=
PIMCORE_DATABASE_USER=
PIMCORE_DATABASE_NAME=
PIMCORE_DATABASE_PASSWORD=

ABUKONFIS_DATABASE_PASSWORD=
TASKRUNNER_DATABASE_PASSWORD=
USERMANAGEMENT_DATABASE_PASSWORD=
NEWS_DATABASE_PASSWORD=

### Nextcloud
NEXTCLOUD_URL=
NEXTCLOUD_HOST=
NEXTCLOUD_VERIFY_SSL=false
NEXTCLOUD_USER=admin
NEXTCLOUD_PASSWORD=
NEXTCLOUD_DATABASE_USER=
NEXTCLOUD_DATABASE_PASSWORD=

NEXTCLOUD_ELASTICSEARCH_URL=
NEXTCLOUD_ELASTICSEARCH_VERIFY_SSL=false
NEXTCLOUD_ELASTICSEARCH_USER=
NEXTCLOUD_ELASTICSEARCH_PASSWORD=

REDIS_DSN=
REDIS_COMPANYCATEGORY=
REDIS_COMPANY=
REDIS_GROUP=
REDIS_USER=user

USER_INITIAL_PASSWORD=

### JITSI
JITSI_BASE_URL=
JITSI_API_KEY=

### CHAT
CHAT_AUTH_TOKEN=
CHAT_AUTH_USERID=
CHAT_DELETE_USER_API_ENDPOINT=
CHAT_FIND_USER_API_ENDPOINT=
GRAYLOG_SERVER=
GRAYLOG_PORT=

ASANA_TOKEN=********************************************************************

DEFAULT_MAIL_FROM_ADDRESS=<EMAIL>
DEFAULT_MAIL_FROM_NAME='ABUS Kransysteme GmbH'

CRM_SUITECRM_BASE_URI=https://crmmw.abus-vpn.de
CRM_SUITECRM_USERNAME=<EMAIL>
CRM_SUITECRM_PASSWORD=

CRM_SUITECRM_STAGE_BASE_URI=https://crmmw.abus-vpn.de
CRM_SUITECRM_STAGE_USERNAME=<EMAIL>
CRM_SUITECRM_STAGE_PASSWORD=

CRM_SEND_DATA_STAGE=false
SSL_MIDDLEWARE_VERIFICATION=false

FORM_BACKEND_PUBLIC_KEY=forms.public.php

###> symfony/mailer ###
MAILER_DSN=null://null
MAILER_FROM_OVERWRITE=<EMAIL>
###< symfony/mailer ###

###> mainick/keycloak-client-bundle ###
IAM_ENABLED=false
IAM_VERIFY_SSL=true # Verify SSL certificate
IAM_BASE_URL='<your-base-server-url>'  # Keycloak server URL
IAM_REALM='<your-realm>' # Keycloak realm name
IAM_CLIENT_ID='<your-client-id>' # Keycloak client id
IAM_CLIENT_SECRET='<your-client-secret>' # Keycloak client secret
IAM_ENCRYPTION_ALGORITHM='<your-algorithm>' # RS256, HS256, etc.
IAM_ENCRYPTION_KEY='<your-public-key>' # public key
IAM_ENCRYPTION_KEY_PATH='<your-public-key-path>' # public key path
IAM_VERSION='<your-version-keycloak>' # Keycloak version
IAM_REDIRECT_URI='<your-redirect-uri>' # Keycloak redirect uri
###< mainick/keycloak-client-bundle ###

###> knplabs/knp-snappy-bundle ###
WKHTMLTOPDF_PATH=/usr/local/bin/wkhtmltopdf
WKHTMLTOIMAGE_PATH=/usr/local/bin/wkhtmltoimage
###< knplabs/knp-snappy-bundle ###
