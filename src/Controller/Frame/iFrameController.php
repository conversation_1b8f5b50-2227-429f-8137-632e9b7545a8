<?php
/**
 * <PERSON><PERSON><PERSON>: <PERSON>
 * E-Mail: <EMAIL>
 * Datum: 27.11.17 14:43.
 */

namespace App\Controller\Frame;

use App\Exception\LanguageNotSupportedException;
use App\Model\Api\CatalogCreator\CatalogCreator;
use App\Model\Api\Language\LanguageSanitizer;
use App\Model\Api\PortalUser\PortalUserInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

class iFrameController extends AbstractController
{
    /**
     * Zeigt den ABUS Chat in einem iFrame an.
     */
    #[Route('/chat', name: 'abus_frame_chat', methods: ['GET'])]
    public function chat(PortalUserInterface $portalUser): Response
    {
        $portalUser->fetch();

        return $this->render(
            'Frame/iFrame/classic.html.twig',
            [
                'user' => $portalUser,
                'openModule' => 'chat',
                'module' => 'chat',
                'link' => 'https://chat.abus-kransysteme.de',
            ]
        );
    }

    /**
     * Zeigt das ABUS Gitlab in einem iFrame an.
     */
    #[Route('/gitlab', name: 'abus_frame_gitlab', methods: ['GET'])]
    public function gitlab(PortalUserInterface $portalUser): Response
    {
        $portalUser->fetch();

        return $this->render(
            'Frame/iFrame/classic.html.twig',
            [
                'user' => $portalUser,
                'openModule' => 'gitlab',
                'module' => 'gitlab',
                'link' => 'https://gitlab.abus-kransysteme.de',
            ]
        );
    }

    /**
     * Zeigt die Medien Bibliothek in einem iFrame an.
     *
     * @throws \App\Exception\LanguageNotSupportedException
     */
    #[Route('/media', name: 'abus_frame_media', methods: ['GET'])]
    public function media(Request $request, PortalUserInterface $portalUser, LanguageSanitizer $languageSanitizer): Response
    {
        $portalUser->fetch();

        setcookie('mediaiframe', true, 0, '/', $this->getParameter('domain'));

        return $this->render(
            'Frame/iFrame/classic.html.twig',
            [
                'user' => $portalUser,
                'openModule' => 'media',
                'module' => 'media',
                'link' => 'https://media.abus-kransysteme.de/pages/home.php?language='.$languageSanitizer->sanitize($request->getLocale(), true),
            ]
        );
    }

    /**
     * Zeigt den ABUS Ersatzteilkatalog in einem iFrame an.
     *
     * @param string $area
     * @param string $catalog
     */
    #[Route('/spareparts/{area}/{catalog}', name: 'abus_frame_spareparts', methods: ['GET'])]
    public function spareparts(Request $request, PortalUserInterface $portalUser, CatalogCreator $catalogCreator, string $area = null, string $catalog = null): Response
    {
        $portalUser->fetch();

        setcookie('sparepartsiframe', true, 0, '/', $this->getParameter('domain'));

        $link = $catalogCreator->getRedirectURL($portalUser, $request->getLocale(), $area, $catalog);

        // Damit werden Warenkorb und Suche gehighlighted
        if (empty($catalog)) {
            $catalog = $area;
        }

        return $this->render(
            'Frame/iFrame/classic.html.twig',
            [
                'user' => $portalUser,
                'openModule' => 'spareparts',
                'openSubmodule' => $catalog,
                'module' => 'spareparts',
                'link' => $link,
            ]
        );
    }

    /**
     * Zeigt die Grafana Software in einem iFrame an.
     */
    #[Route('/grafana', name: 'abus_frame_grafana', methods: ['GET'])]
    public function grafana(PortalUserInterface $portalUser): Response
    {
        $portalUser->fetch();

        return $this->render(
            'Frame/iFrame/classic.html.twig',
            [
                'user' => $portalUser,
                'openModule' => 'devops',
                'openSubmodule' => 'grafana',
                'module' => 'grafana',
                'link' => 'https://monitoring.abus-kransysteme.de',
            ]
        );
    }

    /**
     * Zeigt die Portainer Software in einem iFrame an.
     */
    #[Route('/portainer', name: 'abus_frame_portainer', methods: ['GET'])]
    public function portainer(PortalUserInterface $portalUser): Response
    {
        $portalUser->fetch();

        return $this->render(
            'Frame/iFrame/classic.html.twig',
            [
                'user' => $portalUser,
                'openModule' => 'devops',
                'openSubmodule' => 'portainer',
                'module' => 'portainer',
                'link' => 'https://portainer.abus-kransysteme.de:3000',
            ]
        );
    }

    /**
     * Zeigt Graylog in einem iFrame an.
     */
    #[Route('/logs', name: 'abus_frame_logs', methods: ['GET'])]
    public function logs(PortalUserInterface $portalUser): Response
    {
        $portalUser->fetch();

        return $this->render(
            'Frame/iFrame/classic.html.twig',
            [
                'user' => $portalUser,
                'openModule' => 'devops',
                'openSubmodule' => 'logs',
                'module' => 'logs',
                'link' => 'https://logs.abus-kransysteme.de',
            ]
        );
    }

    /**
     * Zeigt phpMyAdmin in einem iFrame an.
     */
    #[Route('/sql', name: 'abus_frame_sql', methods: ['GET'])]
    public function sql(PortalUserInterface $portalUser): Response
    {
        $portalUser->fetch();

        return $this->render(
            'Frame/iFrame/classic.html.twig',
            [
                'user' => $portalUser,
                'openModule' => 'devops',
                'openSubmodule' => 'sql',
                'module' => 'sql',
                'link' => 'https://sql2.abus-kransysteme.de:3000',
            ]
        );
    }

    /**
     * Zeigt traefik in einem iFrame an.
     */
    #[Route('/traefik', name: 'abus_frame_traefik', methods: ['GET'])]
    public function traefik(PortalUserInterface $portalUser): Response
    {
        $portalUser->fetch();

        return $this->render(
            'Frame/iFrame/classic.html.twig',
            [
                'user' => $portalUser,
                'openModule' => 'devops',
                'openSubmodule' => 'traefik',
                'module' => 'traefik',
                'link' => 'https://traefik.abus-kransysteme.de:3001/dashboard/',
            ]
        );
    }

    /**
     * Zeigt kibana in einem iFrame an.
     */
    #[Route('/kibana', name: 'abus_frame_kibana', methods: ['GET'])]
    public function kibana(PortalUserInterface $portalUser): Response
    {
        $portalUser->fetch();

        return $this->render(
            'Frame/iFrame/classic.html.twig',
            [
                'user' => $portalUser,
                'openModule' => 'devops',
                'openSubmodule' => 'kibana',
                'module' => 'kibana',
                'link' => 'https://kibana.abus-kransysteme.de:3000',
            ]
        );
    }

    /**
     * Zeigt prometheus in einem iFrame an.
     */
    #[Route('/prometheus', name: 'abus_frame_prometheus', methods: ['GET'])]
    public function prometheus(PortalUserInterface $portalUser): Response
    {
        $portalUser->fetch();

        return $this->render(
            'Frame/iFrame/classic.html.twig',
            [
                'user' => $portalUser,
                'openModule' => 'devops',
                'openSubmodule' => 'prometheus',
                'module' => 'prometheus',
                'link' => 'https://prometheus.abus-kransysteme.de:3000',
            ]
        );
    }

    /**
     * Zeigt Matomo in einem iFrame an.
     */
    #[Route('/matomo', name: 'abus_frame_matomo', methods: ['GET'])]
    public function matomo(PortalUserInterface $portalUser): Response
    {
        $portalUser->fetch();

        return $this->render(
            'Frame/iFrame/classic.html.twig',
            [
                'user' => $portalUser,
                'openModule' => 'reporting',
                'openSubmodule' => 'matomo',
                'module' => 'matomo',
                'link' => 'https://matomo.abus-kransysteme.de',
            ]
        );
    }

    /**
     * Zeigt das Intranet in einem iFrame an.
     */
    #[Route('/intranet', name: 'abus_frame_intranet', methods: ['GET'])]
    public function intranet(PortalUserInterface $portalUser): Response
    {
        $portalUser->fetch();

        return $this->render(
            'Frame/iFrame/classic.html.twig',
            [
                'user' => $portalUser,
                'openModule' => 'intranet',
                'openSubmodule' => 'intranet',
                'module' => 'intranet',
                'link' => 'https://abusintranet.abus.intern',
            ]
        );
    }

    /**
     * Zeigt das die Nextcloud in einem iFrame an.
     */
    #[Route('/nextcloud', name: 'abus_frame_nextcloud', methods: ['GET'])]
    public function nextcloud(PortalUserInterface $portalUser): Response
    {
        $portalUser->fetch();

        return $this->render(
            'Frame/iFrame/classic.html.twig',
            [
                'user' => $portalUser,
                'openModule' => 'nextcloud',
                'openSubmodule' => 'nextcloud',
                'module' => 'nextcloud',
                'link' => 'https://nextcloud.abus-kransysteme.de',
            ]
        );
    }

//    /**
//     * Zeigt OVISS in einem iFrame an
//     *
//     * @Route("/oviss", name="abus_frame_oviss", methods={"GET"})
//     *
//     * @param PortalUserInterface $portalUser
//     * @return Response
//     */
//    public function oviss(PortalUserInterface $portalUser): Response
//    {
//        $portalUser->fetch();
//
//        return $this->render(
//            'Frame/iFrame/classic.html.twig',
//            array(
//                'user' => $portalUser,
//                'openModule' => 'oviss',
//                'openSubmodule' => 'oviss',
//                'module' => 'oviss',
//                'link' => 'https://ssl.abus-kransysteme.de/wv2'
//            )
//        );
//    }

    /**
     * Zeigt die Zugangsverwaltung aus dem alten Portal in einem iFrame an.
     *
     * @throws \App\Exception\LanguageNotSupportedException
     */
    #[Route('/account/{url}', name: 'abus_frame_account_legacy', methods: ['GET'])]
    public function accountLegacy(Request $request, string $url, PortalUserInterface $portalUser, LanguageSanitizer $languageSanitizer): Response
    {
        $portalUser->fetch();

        $locale = $languageSanitizer->sanitize($request->getLocale(), true);

        $link = 'https://legacy.'.$_ENV['DOMAIN'].'/account';
        if ('' !== $url) {
            $link .= '/'.$url;
        }

        $link .= '?_lang='.$locale;

        return $this->render(
            'Frame/iFrame/classic.html.twig',
            [
                'user' => $portalUser,
                'openModule' => 'account',
                'openSubmodule' => 'account_'.$url,
                'module' => 'account',
                'link' => $link,
            ]
        );
    }

    /**
     * Zeigt das Reporting aus dem alten Portal in einem iFrame an.
     *
     * @throws \App\Exception\LanguageNotSupportedException
     */
    #[Route('/reportinglegacy', name: 'abus_frame_reporting_legacy', methods: ['GET'])]
    public function reportingLegacy(Request $request, PortalUserInterface $portalUser, LanguageSanitizer $languageSanitizer): Response
    {
        $portalUser->fetch();

        $locale = $languageSanitizer->sanitize($request->getLocale(), true);

        return $this->render(
            'Frame/iFrame/classic.html.twig',
            [
                'user' => $portalUser,
                'openModule' => 'reporting',
                'openSubmodule' => 'legacy',
                'module' => 'reporting',
                'link' => 'https://legacy.'.$_ENV['DOMAIN'].'/reporting?_lang='.$locale,
            ]
        );
    }

//    /**
//     * Zeigt "Weitere Dienste anfragen" aus dem alten Portal in einem iFrame an.
//     *
//     * @Route("/furtherservices", name="abus_frame_furtherServices_legacy", methods={"GET"})
//     *
//     * @throws \App\Exception\LanguageNotSupportedException
//     */
//    public function furtherServicesLegacy(Request $request, PortalUserInterface $portalUser, LanguageSanitizer $languageSanitizer): Response
//    {
//        $portalUser->fetch();
//
//        $locale = $languageSanitizer->sanitize($request->getLocale(), true);
//
//        return $this->render(
//            'Frame/iFrame/classic.html.twig',
//            [
//                'user' => $portalUser,
//                'openModule' => 'furtherservices',
//                'module' => 'furtherservices',
//                'link' => 'https://legacy.'.$_ENV['DOMAIN'].'/request?_lang='.$locale,
//            ]
//        );
//    }

    /**
     * Zeigt den Service Bereich aus dem alten Portal in einem iFrame an.
     *
     * @throws \App\Exception\LanguageNotSupportedException
     */
    #[Route('/service/{url}', name: 'abus_frame_service_legacy', methods: ['GET'])]
    public function serviceLegacy(Request $request, string $url, PortalUserInterface $portalUser, LanguageSanitizer $languageSanitizer): Response
    {
        $portalUser->fetch();

        $locale = $languageSanitizer->sanitize($request->getLocale(), true);

        $link = 'https://legacy.'.$_ENV['DOMAIN'].'/service';
        if ('' !== $url) {
            $link .= '/'.$url;
        }

        $link .= '?_lang='.$locale;

        return $this->render(
            'Frame/iFrame/classic.html.twig',
            [
                'user' => $portalUser,
                'openModule' => 'service',
                'openSubmodule' => 'service_'.$url,
                'module' => 'service',
                'link' => $link,
            ]
        );
    }

    /**
     * Zeigt die Website in einem iFrame an.
     */
    #[Route('/website/{lang}', name: 'abus_frame_website', requirements: ['lang' => 'de|at|uk|es|pl|se|fr|nl|com'], methods: ['GET'])]
    public function website(PortalUserInterface $portalUser, string $lang): Response
    {
        $portalUser->fetch();

        $link = null;
        $openSubmodule = null;

        switch ($lang) {
            case 'de':
                $link = 'https://www.abus-kransysteme.de';
                $openSubmodule = 'germany';
                break;
            case 'at':
                $link = 'https://www.abus-kran.at';
                $openSubmodule = 'austria';
                break;
            case 'uk':
                $link = 'https://www.abuscranes.co.uk';
                $openSubmodule = 'england';
                break;
            case 'es':
                $link = 'https://www.abusgruas.es';
                $openSubmodule = 'spain';
                break;
            case 'pl':
                $link = 'https://www.abuscranes.pl';
                $openSubmodule = 'poland';
                break;
            case 'se':
                $link = 'https://www.abus-kransystem.se';
                $openSubmodule = 'sweden';
                break;
            case 'fr':
                $link = 'https://www.abus-levage.fr';
                $openSubmodule = 'france';
                break;
            case 'nl':
                $link = 'https://www.abus-kraansystemen.nl';
                $openSubmodule = 'netherlands';
                break;
            case 'com':
                $link = 'https://www.abuscranes.com';
                $openSubmodule = 'international';
                break;
        }

        return $this->render(
            'Frame/iFrame/classic.html.twig',
            [
                'user' => $portalUser,
                'openModule' => 'website',
                'openSubmodule' => $openSubmodule,
                'module' => 'website',
                'link' => $link,
            ]
        );
    }

    /**
     * @throws \App\Exception\ADUserNotFoundException
     */
    #[Route('/printshop', name: 'abus_frame_printshop', methods: ['GET'])]
    public function printshop(PortalUserInterface $portalUser): Response
    {
        $portalUser->fetch();

        return $this->render(
            'Frame/iFrame/classic.html.twig',
            [
                'user' => $portalUser,
                'openModule' => 'print_shop',
                'module' => 'print_shop',
                'link' => 'https://me-portal.me-druckhaus.de/PrinectPortal/login2?locale=de',
            ]
        );
    }

    /**
     * @throws \App\Exception\ADUserNotFoundException
     */
    #[Route('/legalnotice', name: 'abus_frame_legalnotice', methods: ['GET'])]
    public function legalNotice(PortalUserInterface $portalUser): Response
    {
        $portalUser->fetch();

        return $this->render(
            'Frame/iFrame/classic.html.twig',
            [
                'user' => $portalUser,
                'highlightModule' => 'legalnotice',
                'link' => 'https://portal.development.mario/legal',
            ]
        );
    }
}
