version: '2'

services:
  nextcloud_db:
    extends:
      file: common.yml
      service: nextcloud_db
    restart: always

  nextcloud_fpm:
    extends:
      file: common.yml
      service: nextcloud_fpm
    restart: always
    links:
      - nextcloud_db
    depends_on:
      - nextcloud_db
    build:
      args:
        development: 1
        environment: ${ENVIRONMENT}
    extra_hosts:
      - "app-2-dmz.abus-vpn.de:************"

  nextcloud_nginx:
    extends:
      file: common.yml
      service: nextcloud_nginx
    restart: always
    build:
      args:
        environment: ${ENVIRONMENT}
    links:
      - nextcloud_fpm
    depends_on:
      - nextcloud_fpm
    volumes_from:
      - nextcloud_fpm
    labels:
      - "traefik.http.routers.${NAMEPREFIX}-secure.rule=${HOSTS}"
      - "traefik.http.routers.${NAMEPREFIX}-secure.tls.certresolver=leresolver"

  nextcloud_redis:
    extends:
      file: common.yml
      service: nextcloud_redis
    restart: always


networks:
  nextcloud-net:
  portal-net:
    external: true
  traefik-net:
    external: true
  database-net:
    external: true
  elasticsearch-net:
    external: true