{% extends 'Frame/Layout/base.html.twig' %}

{% block bodyClass %}layoutNotAuthenticated login{% endblock %}

{% block stylesheets %}
    {{ vite_css('assets/public/register/register.ts') }}
{% endblock %}

{% block body %}
<div data-role="page" id="grantpermission" class="default-background text-dark">
    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100vh; background-color: #0058A1; padding: -20px;">

        <div style="width: 980px; margin: 20px auto 0; padding: 20px; background-color: #e8e8e8;">

            <h2>ABUS Portal</h2>

            <div class="my-2"> <PERSON><PERSON> soll für <b>{{ username }}</b> in <b>{{ service_name|raw|trans({}, 'across') }}</b> freigegeben werden?</div>

            <form action="" method="post" data-ajax="false">

                <div class="form-group border p-4 w-50">

                    {% for key, item in options %}
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" name="level[]" id="{{ key }}" value="{{ key }}">
                            <label class="form-check-label" for="{{ key }}">{{ item }}</label>
                        </div>
                    {% endfor %}

                </div>

                <input type="submit" class="btn btn-success small" name="allow" id="next" value="{{ "Allow access"|trans({}, 'across') }}" data-theme="d" data-icon="arrow-r" data-iconpos="right" />

            </form>
        </div>

    </div>

    {% endblock %}
