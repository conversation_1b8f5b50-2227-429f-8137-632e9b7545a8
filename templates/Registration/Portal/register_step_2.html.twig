{% extends 'Frame/Layout/base.html.twig' %}

{% block bodyClass %}layoutNotAuthenticated login{% endblock %}

{% block stylesheets %}
    {{ vite_css('assets/public/register/register.ts') }}
{% endblock %}

{% block body %}
    <div class="container-fluid">
        <div class="outer">
            <img src="{{ asset('build/images/abus/logos/logo_without_subline.png') }}" class="logo" alt="ABUS Kransysteme GmbH">
            {% include 'Frame/Layout/Parts/LangFlagsBar.html.twig' %}
            <h3 class="portalregisterTitle">{{ "ABUS Portal"|trans({}, 'across') }} - {{ "newregister/registration"|trans({}, 'across') }}</h3>
        </div>
        <div class="container d-flex justify-content-center">
            <div class="jumbotron text-center d-flex justify-content-between">
                <div class="d-flex justify-content-center flex-column">
                    <div class="card">
                        {% if errorArray |length > 0 %}
                            <div class="card-header">
                                <div class="pleaseProvideBox alert alert-danger">
                                    <b>{{ "newregister/pleaseProvide"|trans({}, 'across') }}</b>
                                    <br /><br />
                                    {% for error in errorArray %}
                                        <div>{{ error|trans({}, 'across') }}</div>
                                    {% endfor %}
                                </div>
                            </div>
                        {% endif %}
                        <div class="card-body">
                            <form id="register" method="post" data-ajax="false">
                                {% for service in services %}
                                    <div class="form-group row">
                                        <div class="col-md-1">
                                            <input class="form-check-input form-control m-0" name="services[]" type="checkbox" value="{{ service.uniqueIdentifier }}" id="service{{ service.uniqueIdentifier }}"/>
                                        </div>
                                        <div class="col-md-11">
                                            <label type="text" class="form-check-label form-control font-weight-bold description" for="service{{ service.uniqueIdentifier }}">
                                                <span>{{ service.getName()|raw|trans({}, 'across') }}</span>
                                                {% if service.description is not null %}
                                                    <small class="text-muted">{{ service.description | trans({}, 'across') }}</small>
                                                {% endif %}
                                            </label>
                                        </div>
                                    </div>
{#                                    {% if service.subModules %}#}
{#                                        {% for sub in service.subModules %}#}
{#                                            <div class="form-group row">#}
{#                                                <div class="col-md-1">#}
{#                                                    <input class="form-check-input form-control m-0" name="servicesChilds[]" type="checkbox" value="{{ sub.uniqueIdentifier }}" id="subservice"/>#}
{#                                                </div>#}
{#                                                <div class="col-md-11">#}
{#                                                    <label type="text" class="form-check-label form-control font-weight-bold description" for="services">#}
{#                                                        <span>{{ sub.getName()|raw|trans({}, 'across') }}</span>#}
{#                                                        {% if sub.description is not null %}#}
{#                                                            <small class="text-muted">{{ sub.description | trans({}, 'across') }}</small>#}
{#                                                        {% endif %}#}
{#                                                    </label>#}
{#                                                </div>#}
{#                                            </div>#}
{#                                        {% endfor %}#}
{#                                    {% endif %}#}
                                {% endfor %}
                                <div class="offset-md-1">
                                    <div class="form-group row mb-2">
                                        <div class="col-md-12">
                                            <button type="submit" name="step_2_next" id="next" class="btn btn-warning w-100" value="">{{ "newregister/next"|trans({}, 'across') }}</button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="text-dark ms-2">
                <div class="card" style="width: 18rem;">
                    <div class="card-header bg-secondary text-white">
                        <div class="headline col-12"><b>{{ "newregister/notice"|trans({}, 'across') }}</b>:</div>
                    </div>
                    <div class="card-body">
                        <div class="p-2 text-left">{{ "newregister/pleaseRegister"|trans({}, 'across') }}
                            <br /><br />
                            {{ "newregister/registrationChecked"|trans({}, 'across') }}
                            <br /><br /><b>ABUS Kransysteme GmbH</b>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
