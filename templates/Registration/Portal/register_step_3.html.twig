{% extends 'Frame/Layout/base.html.twig' %}

{% block bodyClass %}layoutNotAuthenticated login{% endblock %}

{% block stylesheets %}
    {{ vite_css('assets/public/register/register.ts') }}
{% endblock %}

    {% block body %}
        <div class="container-fluid d-flex justify-content-center text-dark">
            <div class="jumbotron text-center d-flex justify-content-between">
                <div class="d-flex justify-content-center flex-column">
                    <div class="outer">
                        <img src="{{ asset('build/images/abus/logos/logo_without_subline.png') }}" class="logo" alt="ABUS Kransysteme GmbH">
                        {% include 'Frame/Layout/Parts/LangFlagsBar.html.twig' %}
                        <h3 class="portalregisterTitle text-white">{{ "ABUS Portal"|trans({}, 'across') }} - {{ "newregister/registration"|trans({}, 'across') }}</h3>
                    </div>
                    <div class="card">
                        <div class="card-header">
                            <h2 class="col-12">{{ "newregister/registerComplete"|trans({}, 'across') }}</h2>
                        </div>
                        <div class="card-body text-left">
                            <div class="row">
                                <div class="col-12">{{ "newregister/requestingAccess"|trans({}, 'across') }}</div>
                            </div>
                            <br /><br />
                            <div class="row">
                                <b class="col-12">{{ "newregister/submittedData"|trans({}, 'across') }}</b>
                            </div>
                            <br /><br />

                            <table class="table w-100">
                                <tr>
                                    <td class="font-weight-bold">{{ "newregister/company"|trans({}, 'across') }}</td>
                                    <td>{{ values.company }}</td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">{% block position %}{{ "newregister/position"|trans({}, 'across') }}{% endblock %}</td>
                                    <td>{{ values.position }}</td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">{{ "newregister/name"|trans({}, 'across') }}</td>
                                    <td>{{ values.gender }} {% if values.title is not null %}{{ values.title }} {% endif %} {{ values.firstname }} {{ values.lastname }}</td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">{{ "newregister/street"|trans({}, 'across') }}</td>
                                    <td>{{ values.street }}</td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">{{ "newregister/postcodeTown"|trans({}, 'across') }}</td>
                                    <td>{{ values.postcode }} {{ values.town }}</td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">{{ "newregister/country"|trans({}, 'across') }}</td>
                                    <td>{{ values.country }}</td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">{{ "newregister/telephone"|trans({}, 'across') }}</td>
                                    <td>{{ values.telephone }}</td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">{{ "newregister/email"|trans({}, 'across') }}</td>
                                    <td>{{ values.email }}</td>
                                </tr>
                                <tr><td colspan="2">&nbsp;</td></tr>
                                <tr>
                                    <td class="font-weight-bold" valign="top">{{ "newregister/requestedServices"|trans({}, 'across') }}&nbsp;&nbsp;&nbsp;</td>
                                    <td>
                                        {% if choosenModules is defined %}
                                            {% for key, module in choosenModules %}
                                                {% set first = true %}
                                                {% if choosenModules is iterable %}
                                                    <table class="table">
                                                        <tr>
                                                            {% if module.isSubmodule() %}
                                                                <span class="badge badge-info">{{ module.isSubmodule().name|raw|trans({}, 'across') }} - {{ module.name|raw|trans({}, 'across') }}</span>
                                                            {% else %}
                                                                <span class="badge badge-info">{{ module.name|raw|trans({}, 'across') }}</span>
                                                            {% endif %}
                                                    </table>
                                                {% else %}
                                                    <span class="badge badge-secondary">{{ key|raw }}</span>
                                                {% endif %}
                                            {% endfor %}
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% endblock %}

    {% block footer %}{% endblock %}

