{% extends 'Frame/Layout/base.html.twig' %}

{% block bodyClass %}layoutNotAuthenticated login{% endblock %}

{% block stylesheets %}
    {{ vite_css('assets/public/register/register.ts') }}
{% endblock %}

{% block body %}
    <div class="container-fluid">
        <div class="outer">
            <img src="{{ asset('build/images/abus/logos/logo_without_subline.png') }}" class="logo" alt="ABUS Kransysteme GmbH">
            {% include 'Frame/Layout/Parts/LangFlagsBar.html.twig' %}
        </div>
        <div class="container register-portal-box justify-content-center mb-2">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h2 class="text-left m-0">{{ "ABUS Technical Data"|trans({}, 'across') }} - {{ "Your access to the ABUS Portal"|trans({}, 'across') }}</h2>
                </div>
                <div class="card-body bg-white">
                    {{ "Dear <PERSON><PERSON> or Sir,"|trans({}, 'across') }}
                    <br /><br />
                    {{ "Thank you for your interest in the ABUS product range!"|trans({}, 'across') }}
                    <br /><br />
                    {{ "We would be pleased to provide you with well-founded technical data of our products for the planning or conception of your project."|trans({}, 'across')|raw }}
                    <br /><br />
                    {{ "The ABUS technical data are only accessible to registered users who log on to the ABUS portal with their personal e-mail address. As access requirements, we therefore need some information from you and ask you to provide us with this below:"|trans({}, 'across')|raw }}
                    <br /><br />
                </div>
            </div>
        </div>
        <div class="container register-portal-box d-flex justify-content-center">
            <div class="text-center d-flex justify-content-between mb-5">
                <div class="d-flex justify-content-center flex-column mb-5">
                    <div class="card">
                        {% if errorArray |length > 0 %}
                            <div class="card-header">
                                <div class="pleaseProvideBox alert alert-danger">
                                    <b>{{ "newregister/pleaseProvide"|trans({}, 'across') }}</b>
                                    <br /><br />
                                    {% for error in errorArray %}
                                        <div>{{ error.message|trans({}, 'across') }}</div>
                                    {% endfor %}
                                </div>
                            </div>
                        {% endif %}
                        <div class="card-body">
                            <form class="col-12 register-portal-form" id="register" action="" method="post" data-ajax="false">
                                <div class="row">
                                    <div class="col-md-1 pe-0 ps-0">
                                        <div class="form-group">
                                            <label for="gender" >{{ "newregister/gender"|trans({}, 'across') }}</label>
                                            <select name="gender" id="gender" class="form-control px-0">
                                                <option value="{{ "newregister/mr"|trans({}, 'across') }}" {% if values is not defined or (values and values.gender  is defined and values.gender  ==  "newregister/mr"|trans({}, 'across')) %}selected="selected"{% endif %} >{{ "newregister/mr"|trans({}, 'across') }}</option>
                                                <option value="{{ "newregister/mrs"|trans({}, 'across') }}" {% if values is not defined or (values and values.gender  is defined and values.gender  == "newregister/mrs"|trans({}, 'across')) %}selected="selected"{% endif %} >{{ "newregister/mrs"|trans({}, 'across') }}</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-1 pe-0">
                                        <div class="form-group">
                                            <label for="title">{{ "newregister/title"|trans({}, 'across') }}</label>
                                            <input type="text" name="title" id="title" class="form-control" value="{% if values and values.title  is defined %}{{ values.title  }}{% endif %}" />
                                        </div>
                                    </div>

                                    <div class="col-md-4 {% if (values and values.firstname  is defined and values.firstname  == '') %}isRequired{% endif %}">
                                        <div class="form-group">
                                            <label for="firstname">{{ "newregister/firstname"|trans({}, 'across') }}</label>
                                            <input type="text" name="firstname" id="firstname" class="form-control" value="{% if values and values.firstname  is defined %}{{ values.firstname  }}{% endif %}" />
                                        </div>
                                    </div>

                                    <div class="col-md-6 pe-0 {% if (values and values.lastname  is defined and values.lastname  == '') %}isRequired{% endif %}">
                                        <div class="form-group">
                                            <label for="lastname">{{ "newregister/lastname"|trans({}, 'across') }}</label>
                                            <input type="text" name="lastname" id="lastname" class="form-control" value="{% if values and values.lastname  is defined %}{{ values.lastname  }}{% endif %}" />
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 ps-0">
                                        <div class="form-group">
                                            <label for="company">{{ "newregister/company"|trans({}, 'across') }}</label>
                                            <input type="text" class="form-control" name="company" id="company" value="{% if values is defined and values.company is defined %}{{ values.company }}{% endif %}" />
                                        </div>
                                    </div>

                                    <div class="col-md-6 pe-0">
                                        <div class="form-group">
                                            <label for="position" class="select">{{ "newregister/branch"|trans({}, 'across') }}</label>
                                            <select name="position" id="position" class="form-control">
                                                <option value="" {% if values is not defined or (values and values.position == '') %}selected="selected"{% endif %}></option>
                                                <option value="{{ "Consultancy Engineer"|trans({}, 'across') }}" {% if values and values.position == "Consultancy Engineer"|trans({}, 'across') %}selected="selected"{% endif %} >{{ "Consultancy Engineer"|trans({}, 'across') }}</option>
                                                <option value="{{ "Architect"|trans({}, 'across') }}" {% if values and values.position == "Architect"|trans({}, 'across') %}selected="selected"{% endif %} >{{ "Architect"|trans({}, 'across') }}</option>
                                                <option value="{{ "Planning of Structural Framework"|trans({}, 'across') }}" {% if values and values.position == "Planning of Structural Framework"|trans({}, 'across') %}selected="selected"{% endif %} >{{ "Planning of Structural Framework"|trans({}, 'across') }}</option>
                                                <option value="{{ "Planning Department"|trans({}, 'across') }}" {% if values and values.position == "Planning Department"|trans({}, 'across') %}selected="selected"{% endif %} >{{ "Planning Department"|trans({}, 'across') }}</option>
                                                <option value="{{ "Technical Office"|trans({}, 'across') }}" {% if values and values.position == "Technical Office"|trans({}, 'across') %}selected="selected"{% endif %} >{{ "Technical Office"|trans({}, 'across') }}</option>
                                                <option value="{{ "Reseller"|trans({}, 'across') }}" {% if values and values.position == "Reseller"|trans({}, 'across') %}selected="selected"{% endif %} >{{ "Reseller"|trans({}, 'across') }}</option>
                                                <option value="{{ "Distributor selling though catalogues"|trans({}, 'across') }}" {% if values and values.position == "Distributor selling though catalogues"|trans({}, 'across') %}selected="selected"{% endif %} >{{ "Distributor selling though catalogues"|trans({}, 'across') }}</option>
                                                <option value="{{ "Crane Construction Company"|trans({}, 'across') }}" {% if values and values.position == "Crane Construction Company"|trans({}, 'across') %}selected="selected"{% endif %} >{{ "Crane Construction Company"|trans({}, 'across') }}</option>

                                                <option value="{{ "newregister/other"|trans({}, 'across') }}" {% if values and values.position == "newregister/other"|trans({}, 'across') %}selected="selected"{% endif %} >{{ "newregister/other"|trans({}, 'across') }}</option>

                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 ps-0 {% if (values and values.street is defined and values.street == '') %}isRequired{% endif %}">
                                        <div class="form-group">
                                            <label for="street">{{ "newregister/street"|trans({}, 'across') }}</label>
                                            <input type="text" name="street" id="street" class="form-control" value="{% if values and values.street is defined %}{{ values.street }}{% endif %}" />
                                        </div>
                                    </div>

                                    <div class="col-md-2 pe-0">
                                        <div class="form-group">
                                            <label for="postcode">{{ "newregister/postcode"|trans({}, 'across') }}</label>
                                            <input type="text" name="postcode" id="postcode" class="form-control {% if (values is defined and values.postcode  is defined and values.postcode  == '') %}isRequired{% endif %}" value="{% if values is defined and values.postcode  is defined %}{{ values.postcode  }}{% endif %}" />
                                        </div>
                                    </div>

                                    <div class="col-md-4 pe-0">
                                        <div class="form-group">
                                            <label for="town">{{ "newregister/town"|trans({}, 'across') }}</label>
                                            <input type="text" name="town" id="town" class="form-control {% if (values and values.town  is defined and values.town  == '') %}isRequired{% endif %}" value="{% if values and values.town  is defined %}{{ values.town  }}{% endif %}" />
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 ps-0">
                                        <div class="form-group">
                                            <label for="country">{{ "newregister/country"|trans({}, 'across') }}</label>
                                            <select name="country" id="country" class="form-control" tabindex="10">
                                                {% for country in countries %}
                                                    <option value="{{ country.printable_name }}" {% if current_country == country.printable_name %}selected="selected"{% endif %}>{{ country.printable_name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 ps-0 {% if (values and values.email  is defined and values.email  == '') %}isRequired{% endif %}">
                                        <div class="form-group">
                                            <label for="email">{{ "newregister/email"|trans({}, 'across') }}</label>
                                            <input type="text" name="email" id="email" class="form-control" value="{% if values and values.email  is defined %}{{ values.email  }}{% endif %}" />
                                        </div>
                                    </div>
                                    <div class="col-md-6 pe-0 {% if (values and values.telephone  is defined and values.telephone  == '') %}isRequired{% endif %}">
                                        <div class="form-group">
                                            <label for="telephone">{{ "newregister/telephone"|trans({}, 'across') }}</label>
                                            <input type="text" name="telephone" id="telephone" class="form-control" value="{% if values and values.telephone  is defined %}{{ values.telephone  }}{% endif %}" />
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-12 px-0">
                                        <div class="alert alert-success custom-control custom-checkbox">
                                            <div class="form-group row">
                                                <div class="form-check">
                                                    <input class="custom-control-input form-check-input mt-0" id="privacy" onChange="activateButton()" type="checkbox" required/>
                                                    <label for="privacy" class="description custom-control-label form-check-label">
                                                        <b>{{ "newregister/dataprotection"|trans({}, 'across') }}:</b>
                                                        <span>{{ "newregister/dataprotectionText"|trans({}, 'across') }}</span>
                                                        {% if getLanguage()|lower == 'de_de' %}
                                                            {% set dps_link = 'https://nextcloud.abus-kransysteme.de/s/DKgew9AZcyYqByL/download' %}
                                                        {% elseif  getLanguage()|lower == 'fr_fr'%}
                                                            {% set dps_link = 'https://nextcloud.abus-kransysteme.de/s/jEq23xo3s5ZXGTY/download' %}
                                                        {% elseif  getLanguage()|lower == 'es_es'%}
                                                            {% set dps_link = 'https://nextcloud.abus-kransysteme.de/s/RaPSBF7erXe9FX5/download' %}
                                                        {% else %}
                                                            {% set dps_link = 'https://nextcloud.abus-kransysteme.de/s/Tk5MKg43fiQwibM/download' %}
                                                        {% endif %}
                                                        <span> {{ "newregister/moreInformation"|trans({}, 'across') }} <a href="{{ dps_link }}" target="_self">{{ "newregister/dataprotection"|trans({}, 'across') }}</a>.</span>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-12 px-0">
                                        <div class="form-group m-0">
                                            <button type="submit" name="step_1_next" id="next" class="btn btn-warning w-100" value="{{ "newregister/next"|trans({}, 'across') }}" disabled="disabled">
                                                {{ "newregister/next"|trans({}, 'across') }}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="text-dark ms-2">
                    <div class="card" style="width: 18rem;">
                        <div class="card-header bg-secondary text-white">
                            <div class="headline col-12"><b>{{ "newregister/notice"|trans({}, 'across') }}</b>:</div>
                        </div>
                        <div class="card-body">
                            <div class="p-2 text-left">{{ "newregister/pleaseRegister"|trans({}, 'across') }}
                                <br /><br />
                                {{ "newregister/registrationChecked"|trans({}, 'across') }}
                                <br /><br /><b>ABUS Kransysteme GmbH</b>
                                <br />
                                (<a href="https://portal.abus-kransysteme.de/legal?_lang={{ app.request.get("_lang") }}" target="_blank">{{ "newregister/legalNotice"|trans({}, 'across') }}</a>)
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="footer">
        <div id="help" class="container">
            <span>{{ "frame/any_problems"|trans({}, 'across') }}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="mailto: <EMAIL>?subject=ABUS Portal"><EMAIL></a></span>
        </div>
    </div>

{% endblock %}


{% block javascripts %}

    {{ parent() }}

    <script>
        const privacy = document.querySelector('#privacy');
        const btn = document.querySelector('#next');

        function activateButton() {
            if(privacy.checked == true) {
                btn.removeAttribute('disabled');
                return;
            }
            btn.setAttribute('disabled', 'disabled')
        }
    </script>
{% endblock %}
