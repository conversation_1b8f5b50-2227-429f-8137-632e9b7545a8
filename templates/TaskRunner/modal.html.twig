<div class="modal fade" id="taskrunner" tabindex="-1" role="dialog" aria-hidden="true" ng-controller="{{ ngController }}">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">
                    <span aria-hidden="true">×</span><span class="visually-hidden">{{ "taskrunner/close"|trans({}, 'across') }}</span>
                </button>
                <h4 class="modal-title" id="myModalLabel">{{ "taskrunner/task_is_running"|trans({}, 'across') }}</h4>
            </div>

            <div class="modal-body">
                {{ "taskrunner/you_will_be_notified"|trans({}, 'across') }}
            </div>

            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-success">{{ "taskrunner/close"|trans({}, 'across') }}</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="taskrunnerError" tabindex="-1" role="dialog" aria-hidden="true" ng-controller="{{ ngController }}">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header" style="background-color: #db5565; border-color: #db5565; color: #ffffff;">
                <button type="button" class="close" data-dismiss="modal">
                    <span aria-hidden="true" style="color: #ffffff;">×</span><span class="visually-hidden">{{ "taskrunner/close"|trans({}, 'across') }}</span>
                </button>
                <h4 class="modal-title" id="myModalLabel">{{ "taskrunner/error_occured"|trans({}, 'across') }}</h4>
            </div>

            <div class="modal-body">
                {{ "taskrunner/error_while_executing"|trans({}, 'across') }}
            </div>

            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-danger">{{ "taskrunner/close"|trans({}, 'across') }}</button>
            </div>
        </div>
    </div>
</div>
