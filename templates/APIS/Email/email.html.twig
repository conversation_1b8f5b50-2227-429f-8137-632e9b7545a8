{% extends 'Api/email/email.html.twig' %}
{% block content %}

    {{ "apis/salutation"|trans({}, 'across', locale) }}<br /><br />

    {{ text|trans({}, 'across', locale) }}<br /><br />

    <a href="{{ link }}">{{ link }}</a><br /><br />

    <table cellpadding=0 cellspacing=0 style="font-family: sans-serif; font-size: 10pt; padding-top: 5px; padding-right: 10px;">
        <tr>
            <td>{{ "apis/title"|trans({}, 'across', locale) }}:</td>
            <td><strong>{{ title }}</strong></td>
        </tr>
        {% if date is not empty %}
        <tr>
            <td>{{ "apis/date"|trans({}, 'across', locale) }}:</td>
            <td>{{ date|localizeddate('medium', 'none', locale) }}</td>
        </tr>
        {% endif %}
        <tr>
            <td>{{ "apis/author"|trans({}, 'across', locale) }}:</td>
            <td><a href="mailto: {{ authorEmail }}">{{ author }}</a></td>
        </tr>
    </table>

{% endblock %}
