{% extends 'Frame/Layout/layout.html.twig' %}

{% block administration %}
    {{ parent() }}
    <div class="sidebar-panel">
        <h5 class="sidebar-panel-title">
            {{ ["apis/", module, "/headline_short"]|join|trans({}, 'across') }}
        </h5>
    </div>

    <div class="content" >
        <apis-backend-sidebar></apis-backend-sidebar>
    </div>
{% endblock %}

{% block headline %}{{ ["apis/", module, "/headline"]|join|trans({}, 'across') }}{% endblock %}
{% block headline_description %}{% endblock %}
{% block headline_backbutton %}{% endblock %}


{% block content %}

    {% set categoriesTranslated = [] %}

    {% for category in categories %}
        {% set categoriesTranslated = categoriesTranslated|merge({ (category): ["apis/", module, "/", category|lower|replace({' ': '_'})]|join|trans({}, 'across')}) %}
    {% endfor %}

    <apis-create
            section="{{ module }}"
            :number="{{ number }}"
            sectionprefix="{{ sectionprefix }}"
            :categories="{{ categoriesTranslated|json_encode() }}"
            :receivers="{{ receivers|json_encode() }}"
    ></apis-create>

{% endblock %}

{% block custom_javascripts %}

{% endblock %}
