{% extends 'Frame/Layout/base.html.twig' %}

{% block title %}{{ "frame/portal"|trans({}, 'across') }} | ABUS Kransysteme GmbH{% endblock %}

{% block bodyClass %}layoutNotAuthenticated login{% endblock %}

{% block stylesheets %}
    <style>
        [v-cloak] {
            display: none !important;
        }
    </style>

    {{ vite_css('assets/public/login/login.ts') }}
{% endblock %}

{% block javascripts %}
    {{ vite_script('assets/public/app/app.ts') }}
    {{ vite_script('assets/public/login/login.ts') }}
{% endblock %}

{% block body %}

    <div id="login" class="container-fluid" v-cloak>

        <div class="outer">

            <img src="{{ asset('build/images/abus/logos/logo_without_subline.png') }}" class="logo" alt="ABUS Kransysteme GmbH">

            {% include 'Frame/Layout/Parts/LangFlagsBar.html.twig' %}

            {% block content %}

                <div v-show="!showResetPasswordDialog">
                    <div class="container-fluid message-container">
                        {% if error %}
                            <div class="row">
                                <div class="alert alert-danger" role="alert">
                                    {{ ['frame/', error.messageKey|lower|replace({' ': '_'})]|join|trans({}, 'across')|raw }}
                                </div>
                            </div>
                        {% endif %}
                    </div>

                    <form action="{{ path('login') }}" method="post">
                        <div class="form-group" >
                            <input type="text" id="username" name="_username" class="form-control" placeholder="{{ "frame/username_(email)"|trans({}, 'across') }}"/>
                        </div>
                        <div class="form-group">
                            <input type="password" id="password" class="form-control" name="_password" placeholder="{{ "frame/password"|trans({}, 'across') }}" />
                        </div>

                        <br/>

                        <input type="hidden" name="_csrf_token"
                               value="{{ csrf_token('authenticate') }}"
                        >

                        <button type="submit" class="btn btn-success">{{ "frame/login"|trans({}, 'across') }}</button>

                        <div class="forgotPassword" @click="showResetPasswordDialog = true">{{ "frame/forgot_your_password"|trans({}, 'across') }}</div>
                    </form>

                </div>

                <div v-show="showResetPasswordDialog">
                    <div class="container-fluid message-container">
                        <div class="row" v-show="emailSent">
                            <div class="alert alert-info" role="alert" style="width: 100%">
                                {* this.backendMessage *}
                            </div>
                        </div>
                    </div>

                    <form action="" method="post" :init="recoveryRoute = '{{ path('abus_api_forgot_password') }}'">
                        <div class="form-group">
                            <input type="email" name="email" class="form-control" placeholder="{{ "frame/email"|trans({}, 'across') }}" v-model="sendPasswordRecoveryToEmail">
                        </div>

                        <button type="button" class="btn btn-success" @click="requestNewPassword" :disabled="!sendPasswordRecoveryToEmail.length">{{ "frame/request_new_password"|trans({}, 'across') }}</button>

                        <br /><br />

                        <button type="button" class="btn btn-primary" @click="showResetPasswordDialog = false">{{ "frame/back_to_login"|trans({}, 'across') }}</button>
                    </form>
                </div>
            {% endblock %}
        </div>
    </div>

    <div class="footer">

        <div class="container-fluid">
        <div class="row">
            <div class="col-xl-4 col-lg-6" id="register">
                {% if hasCookie('ABUSREGISTER') == false %}
                    <a href="{{ url('abus_register_legacy') }}" class="btn btn-sm btn-secondary">{{ "frame/register_account"|trans({}, 'across') }}</a>
                {% else %}
                    &nbsp;
                {% endif %}
            </div>
            <div class="col-xl-4 col-lg-6" id="help">
                <span>{{ "frame/any_problems"|trans({}, 'across') }}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <a class="ui-link noSpinner" href="mailto: <EMAIL>?subject=ABUS Portal"><EMAIL></a>
                </span>
            </div>
            {#<div class="col-lg-4" id="imprint">#}
                {#<span><a href="{{ url('abus_frame_legal_notice') }}">{{ "Imprint"|trans({}, 'frame') }}</a></span>#}
            {#</div>#}
        </div>
        </div>
    </div>

{% endblock %}
