{% extends 'Frame/Layout/layout.html.twig' %}

{% block administration %}
    {{ parent() }}
{% endblock %}

{% set showPageHeader = false %}
{% set iframe = true %}
{#{% set hideRightSidebar = true %}#}

{% block title %}
    {% if title is defined %}{{ title }}
    {% else %} {{ parent() }}
    {% endif %}
{% endblock %}


{% block content %}

    {% if user.isUserMode() %}
        <div class="container">
            <div class="bs-callout bs-callout-danger">
                <h4>{{ "frame/iframe/not_available"|trans({}, 'across') }}</h4>
                <p>{{ "frame/iframe/not_available_description"|trans({}, 'across') }}</p>
            </div>
        </div>
    {% else %}

        <div style="overflow: auto; -webkit-overflow-scrolling:touch;">
            <iframe id="frame-iframe" data-src="{{ link }}" frameborder="0" scrolling="yes" seamless="seamless" class="{% if mobile is defined and mobile == true %}mobile-iframe{% endif %} {% if scrollbar is defined and scrollbar == true %}scrollbar{% endif %}">Alternativtext</iframe>
        </div>

        {#<div class="container-fluid">#}
            {#<div class="row">#}
                {#<div class="col-xs-12 w-100 scroll-wrapper">#}
                    {#<iframe src="{{ link }}" frameborder="0" scrolling="yes" seamless="seamless" sandbox="allow-forms allow-pointer-lock allow-popups allow-same-origin allow-scripts">Alternativtext</iframe>#}
                    {#<iframe src="{{ link }}" frameborder="0" scrolling="yes" seamless="seamless" class="{% if mobile is defined and mobile == true %}mobile-iframe{% endif %} {% if scrollbar is defined and scrollbar == true %}scrollbar{% endif %}">Alternativtext</iframe>#}
                {#</div>#}
            {#</div>#}
        {#</div>#}
    {% endif %}

{% endblock %}
