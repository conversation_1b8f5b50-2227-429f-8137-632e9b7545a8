{% extends 'Frame/Layout/layout.html.twig' %}

{% block administration %}
    {{ parent() }}
{% endblock %}

{% set showPageHeader = true %}
{% set iframe = false %}
{% set iframeWithBorder = true %}

{% block title %}
    {% if title is defined %}{{ title }}
    {% else %} {{ parent() }}
    {% endif %}
{% endblock %}

{% block headline_backbutton %}{% endblock %}

{% block headline %}{{ "tdata/technical_data"|trans({}, 'across') }}{% endblock %}
{% block headline_description %}
    {% if module == 'eot_cranes_abukonfis' %}
        {{ "tdata/eot_cranes"|trans({}, 'across') }}
    {% endif %}

    {% if module == 'jib_cranes_abukonfis' %}
        {{ "tdata/jib_cranes"|trans({}, 'across') }}
    {% endif %}

    {% if module == 'mobile_gantry_cranes_abukonfis' %}
        {{ "tdata/mobile_gantry_cranes"|trans({}, 'across') }}
    {% endif %}

    {% if module == 'wire_rope_hoists_abukonfis' %}
        {{ "tdata/wire_rope_hoists"|trans({}, 'across') }}
    {% endif %}

    {% if module == 'chain_hoists_with_trolley_abukonfis' %}
        {{ "tdata/chain_hoists_with_trolley"|trans({}, 'across') }}
    {% endif %}

    {% if module == 'stationary_chain_hoists_abukonfis' %}
        {{ "tdata/stationary_chain_hoists"|trans({}, 'across') }}
    {% endif %}

    {% if module == 'crane_drives_abukonfis' %}
        {{ "tdata/crane_drives"|trans({}, 'across') }}
    {% endif %}

    {% if module == 'hb-systems_abukonfis' %}
        {{ "tdata/hb-systems"|trans({}, 'across') }}
    {% endif %}

{% endblock %}

{% block content %}

    <div id="tdata">
    {% if user.isUserMode() %}
        <div class="container">
            <div class="bs-callout bs-callout-danger">
                <h4>{{ "frame/iframe/not_available"|trans({}, 'across') }}</h4>
                <p>{{ "frame/iframe/not_available_description"|trans({}, 'across') }}</p>
            </div>
        </div>
    {% else %}
        {% if module in ['eot_cranes_abukonfis', 'jib_cranes_abukonfis', 'mobile_gantry_cranes_abukonfis', 'wire_rope_hoists_abukonfis', 'chain_hoists_with_trolley_abukonfis', 'stationary_chain_hoists_abukonfis'] %}
            <div class="row mt-3 mb-3">
                <div class="col">

                    <div class="card card-closed mt-3">
                        <div class="card-header text-white bg-abus-blue-light">
                            <i class="fa fa-th-large"></i>
                            {{ 'tdata/crane_selection_help'|trans({}, 'across') }}
                            <div class="card-controls card-controls-right">
                                <a href="#" class="toggle card-minimize">
                                    <i class="fa-angle-up fa"></i>
                                </a>
                            </div>
                        </div>
                        <div class="card-body bg-white">
                            {% if module == 'eot_cranes_abukonfis' %}
                                {% include 'TechnicalData/ProduktInformation/Laufkran/ElvElkEls.html.twig' %}
                                {% include 'TechnicalData/ProduktInformation/Laufkran/Zlk.html.twig' %}
                                {% include 'TechnicalData/ProduktInformation/Laufkran/DlvmEdlEdk.html.twig' %}
                            {% endif %}

                            {% if module == 'jib_cranes_abukonfis' %}
                                {% include 'TechnicalData/ProduktInformation/Schwenkkran/Ls.html.twig' %}
                                {% include 'TechnicalData/ProduktInformation/Schwenkkran/Lsx.html.twig' %}
                                {% include 'TechnicalData/ProduktInformation/Schwenkkran/Vs.html.twig' %}
                                {% include 'TechnicalData/ProduktInformation/Schwenkkran/Lw.html.twig' %}
                                {% include 'TechnicalData/ProduktInformation/Schwenkkran/Lwx.html.twig' %}
                                {% include 'TechnicalData/ProduktInformation/Schwenkkran/Vw.html.twig' %}
                            {% endif %}

                            {% if module == 'mobile_gantry_cranes_abukonfis' %}
                                {% include 'TechnicalData/ProduktInformation/Leichtportalkran/Leichtportalkran.html.twig' %}
                                <nextcloud start-folder="{{ "tdata/technische_daten/mobile_gantry"|trans({}, 'across') }}" :is-admin="false" :inline-search="false"></nextcloud>
                            {% endif %}

                            {% if module == 'wire_rope_hoists_abukonfis' %}
                                {% include 'TechnicalData/ProduktInformation/Seilzug/TypeE.html.twig' %}
                                {% include 'TechnicalData/ProduktInformation/Seilzug/TypeS.html.twig' %}
                                {% include 'TechnicalData/ProduktInformation/Seilzug/TypeU.html.twig' %}
                                {% include 'TechnicalData/ProduktInformation/Seilzug/TypeD.html.twig' %}
                                {% include 'TechnicalData/ProduktInformation/Seilzug/TypeZ.html.twig' %}
                                <nextcloud start-folder="{{ "tdata/technische_daten/wire_rope_hoist"|trans({}, 'across') }}" :is-admin="false" :inline-search="false"></nextcloud>
                            {% endif %}

                            {% if module == 'chain_hoists_with_trolley_abukonfis' %}
                                {% include 'TechnicalData/ProduktInformation/Kettenzug/gmc.html.twig' %}
                                {% include 'TechnicalData/ProduktInformation/Kettenzug/gm2.html.twig' %}
                                {% include 'TechnicalData/ProduktInformation/Kettenzug/gm4.html.twig' %}
                                {% include 'TechnicalData/ProduktInformation/Kettenzug/gm6.html.twig' %}
                                {% include 'TechnicalData/ProduktInformation/Kettenzug/gm8.html.twig' %}
                                <nextcloud start-folder="{{ "tdata/technische_daten/chain_hoist"|trans({}, 'across') }}" :is-admin="false" :inline-search="true"></nextcloud>
                            {% endif %}

                            {% if module == 'stationary_chain_hoists_abukonfis' %}
                                {% include 'TechnicalData/ProduktInformation/Kettenzug/gmc.html.twig' %}
                                {% include 'TechnicalData/ProduktInformation/Kettenzug/gm2.html.twig' %}
                                {% include 'TechnicalData/ProduktInformation/Kettenzug/gm4.html.twig' %}
                                {% include 'TechnicalData/ProduktInformation/Kettenzug/gm6.html.twig' %}
                                {% include 'TechnicalData/ProduktInformation/Kettenzug/gm8.html.twig' %}
                                <nextcloud start-folder="{{ "tdata/technische_daten/chain_hoist"|trans({}, 'across') }}" :is-admin="false" :inline-search="false"></nextcloud>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}

        {% if user.werksvertretung != false %}
            <div class="row mb-3">
                <div class="col-md-12 col-lg-6 mb-3">
                    {% include 'TechnicalData/KontaktInformation/ABUS.html.twig' %}
                </div>
                <div class="col-md-12 col-lg-6 mb-3">
                    {% if module == 'eot_cranes_abukonfis' %}
                        {% include 'TechnicalData/KontaktInformation/website_eot_cranes.html.twig' %}
                    {% endif %}

                    {% if module == 'jib_cranes_abukonfis' %}
                        {% include 'TechnicalData/KontaktInformation/website_jib_cranes.html.twig' %}
                    {% endif %}

                    {% if module == 'mobile_gantry_cranes_abukonfis' %}
                        {% include 'TechnicalData/KontaktInformation/website_mobile_gantry_cranes.html.twig' %}
                    {% endif %}

                    {% if module == 'wire_rope_hoists_abukonfis' %}
                        {% include 'TechnicalData/KontaktInformation/website_wire_rope_hoists.html.twig' %}
                    {% endif %}

                    {% if module == 'chain_hoists_with_trolley_abukonfis' %}
                        {% include 'TechnicalData/KontaktInformation/website_chain_hoists_with_trolley.html.twig' %}
                    {% endif %}

                    {% if module == 'stationary_chain_hoists_abukonfis' %}
                        {% include 'TechnicalData/KontaktInformation/website_stationary_chain_hoists.html.twig' %}
                    {% endif %}

                    {% if module == 'crane_drives_abukonfis' %}
                        {% include 'TechnicalData/KontaktInformation/website_crane_drives.html.twig' %}
                    {% endif %}

                    {% if module == 'hb-systems_abukonfis' %}
                        {% include 'TechnicalData/KontaktInformation/website_hb-systems.html.twig' %}
                    {% endif %}
                </div>
            </div>
        {% endif %}

        <div class="card mb-3">
            <div class="card-body bg-white selectionPortlet">
                <iframe id="frame-iframe" data-src="{{ link }}" frameborder="0" scrolling="yes" seamless="seamless" style="height: 1600px" class="{% if mobile is defined and mobile == true %}mobile-iframe{% endif %} {% if scrollbar is defined and scrollbar == true %}scrollbar{% endif %}">Alternativtext</iframe>
            </div>
        </div>
    {% endif %}
    </div>

{% endblock %}
