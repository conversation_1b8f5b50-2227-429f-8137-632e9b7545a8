<li class="dropdown">
    <a href="#" data-toggle="dropdown" class="tipB" title="{{ "Notifications"|trans({}, 'frame') }}">
        <i class="fa fa-bell-o fa-fw"></i>
        <span class="visually-hidden">{{ "Notifications"|trans({}, 'frame') }}</span>
        <span class="badge badge-danger">{{ user.notifications|length }}</span>
    </a>
    <ul class="dropdown-menu right dropdown-notification" role="menu">
        <li><a href="#">{{ "Notifications"|trans({}, 'frame') }}</a></li>

        {% for notification in user.notifications %}
            <li><a href="{{ path('abus_frame_notification', {'subdomain': getSubdomain('portal'), 'domain': getDomain(), 'id': notification.id}) }}"><i class="{{ notification.icon }}"></i> {{ notification.name }}</a></li>
        {% endfor %}

        <li><a href="{{ path('abus_frame_notifications', {'subdomain': getSubdomain('portal'), 'domain': getDomain()}) }}" class="view-all">{{ "View all"|trans({}, 'frame') }} <i class="l-arrows-right"></i></a></li>
    </ul>
</li>
