<div class="sidebar-panel">
    <h5 class="sidebar-panel-title">{{ "frame/abus_services"|trans({}, 'across') }}</h5>
</div>

<div class="side-nav">
    <ul class="nav">

        {% for module in user.modules %}

            {% if (module.link is defined and module.link is not empty) or (module.submodules is defined and module.submodules is not empty) %}

                {% set link = modulelinkDecoder(module.link) %}
                {% if(module.convertLinkToDevelopment) %}
                    {% set link = switchDomain(module.link) %}
                {% endif %}

                <li {% if module.submodules is not empty  %}class="hasSub {% if module.identifier is defined and openModule is defined and module.identifier == openModule %}highlight-menu{% endif %}"{% endif %}>
                    {% if module.link is defined %}<a href="{% if link_active is not defined or link_active == true %}{{ link }}{% endif %}" {% if module.link_target is defined %}target="{{ module.link_target }}"{% endif %} {% if module.identifier is defined and openModule is defined and module.identifier == openModule %} class="{% if module.submodules is empty %}active{% else %}active-state expand{% endif %}"{% endif %}  {% if module.openInNewTab is defined and module.openInNewTab %}target="_blank"{% endif %}>{% endif %}

                        {% if module.identifier is defined and module.identifier == 'spareparts' and user.getConnectionStatus('catalog') == false%}
                            <i class="fa fa-bolt fa-fw text-danger"></i>
                        {% endif %}

                        {% if module.identifier is defined and module.identifier == 'abukonfis' and user.getConnectionStatus('abukonfis') == false%}
                            <i class="fa fa-bolt fa-fw text-danger"></i>
                        {% endif %}

                        {% if module.icon is defined %}
                                <i class="{{ module.icon }} fa-fw"></i>
                        {% endif %}
                        <span class="txt">{{ module.name|trans({}, 'across') }}</span>

                    {% if module.link is defined %}</a>{% endif %}

                    {% if module.submodules is defined and module.submodules is not empty %}

                        <ul class="sub {% if module.identifier is defined and openModule is defined and module.identifier == openModule %}show{% endif %}">

                            {% if module.identifier is defined and module.identifier == 'spareparts' %}
                                {% for catalog in user.getAllowedCatalogs %}
                                    <li><a href="{{ switchDomain( module.link) }}/catalog/{{ catalog|url_encode }}" {% if openSubmodule is defined and catalog is defined and catalog == openSubmodule %}class="active"{% endif %}><span class="txt">{{ translateCatalog(catalog) }}</span></a></li>
                                {% endfor %}
                            {% endif %}


                            {% for key, submodule in module.subModules %}

                                {% set submodulelink = modulelinkDecoder(submodule.link) %}
                                {% if(module.convertLinkToDevelopment) %}
                                    {% set submodulelink = switchDomain(submodule.link) %}
                                {% endif %}

                                {% if submodule.name is defined %}
                                    <li {% if submodule.submodules is defined and submodule.submodules|length > 0 %}class="hasSub"{% endif %}>
                                        <a href="{% if submodule.link is defined %}{{ submodulelink }}{% endif %}" {% if openSubmodule is defined and submodule.identifier == openSubmodule %}class="active {% if submodule.submodules is defined and submodule.submodules is not empty %}expand{% endif %}"{% endif %} {% if submodule.openInNewTab is defined and submodule.openInNewTab %}target="_blank"{% endif %}><span class="txt">{{ submodule.name|trans({}, 'across') }}</span></a>
                                    </li>
                                {% endif %}
                            {% endfor %}
                        </ul>
                    {% endif %}
                </li>
            {% endif %}
        {% endfor %}
    </ul>
</div>
