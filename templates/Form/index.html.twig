{% extends 'Frame/Layout/layout.html.twig' %}

{% block stylesheets %}
    <link rel="stylesheet" href="{{ asset('vueform/abusForms.css', 'vueform') }}">
    {{ vite_css('assets/public/form/form.ts') }}
    {{ parent() }}
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script type="module" src="{{ asset('vueform/abusForms.js', 'vueform') }}"></script>
{% endblock %}

{% block headline %}{% endblock %}
{% block headline_description %}{% endblock %}
{% block headline_backbutton %}{% endblock %}

{% block content %}

   <div class="container border my-5">
       <div id="app" class="abusFormController">
           <abus-form formname="{{ formName }}" locale-long="{{ getLanguage(getLanguage(true,true))|replace({'_': '-'}) }}" default-form-data='{{ { "mail": identity }|json_encode|raw }}' encrypted-data='{{ { "identity": identity, "name": name }|json_encode|encryptFormData }}'></abus-form>
       </div>
   </div>

{% endblock %}

{% block footer %}{% endblock %}
