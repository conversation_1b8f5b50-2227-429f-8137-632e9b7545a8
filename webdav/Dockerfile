FROM debian:latest

ARG environment
ENV environment="$environment"

RUN apt-get clean && apt-get update && apt-get install -y \
        davfs2 \
        ca-certificates \
        rsync \
        cron \
    && rm -rf /var/lib/apt/lists/*

# WEBDAV Zertifikat kopieren
COPY webdav/certs/webdav.cer /etc/davfs2/certs/webdav.cer
COPY webdav/certs/abus_chain.cer /etc/davfs2/certs/abus_chain.cer

# WEBDAV config kopieren
COPY webdav/config/davfs2.conf /etc/davfs2/davfs2.conf

RUN touch /etc/mtab

COPY webdav/entrypoint.sh /
RUN chmod 777 /entrypoint.sh
ENTRYPOINT /entrypoint.sh