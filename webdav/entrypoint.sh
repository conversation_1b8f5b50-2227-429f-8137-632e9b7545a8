#!/bin/bash

BASE_DIR="/mnt/webdav"
URL_BASE="https://app-2-dmz.abus-vpn.de"
USERNAME=<EMAIL>
PASSWORD=roboN3XT!

# Secrets File erstellen
SECRETS_FILE="/etc/davfs2/secrets"

# Liste der Unterverzeichnisse
DIRECTORIES=(
  "Service"
  "3DProduktmodell"
  "Prospektmaterial"
  "/TechDoku/pdf"
  "Sicherheitshinweise Fremdfirmen"
  "Abnahme-Sachverstaendige"
  "Service-Notdienst"
  "Servicetechniker ABUS GM"
  "Auftragszentrum/ABUS Portal/ABUS Box/"
  "ABUS Liefertermine"
  "proALPHA9"
  "Produkthandbuecher"
)

# Verzeichnisse erstellen
for dir in "${DIRECTORIES[@]}"; do
  mkdir -p "$BASE_DIR/$dir"

  MOUNT_POINT="$BASE_DIR/$dir"
  WEB_DAV_URL="$URL_BASE/$dir"

  # Eintrag in die secrets-Datei hinzufügen
  echo "\"$WEB_DAV_URL\" $USERNAME $PASSWORD" >> "$SECRETS_FILE"

  # Mount ausführen
  echo "Mounting $WEB_DAV_URL to $MOUNT_POINT"
  mount.davfs -o ro "$WEB_DAV_URL" "$MOUNT_POINT"
done

# Erster Synchronisationslauf (direkt beim Start des Containers), danach per cron Job
rsync -av --delete /mnt/webdav/ /webdav
echo "*/10 * * * * rsync -av --delete /mnt/webdav/ /webdav" > /etc/cron.d/rsync-cron
cron

tail -f /dev/null