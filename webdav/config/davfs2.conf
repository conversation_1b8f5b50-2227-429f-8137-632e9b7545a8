# davfs2 configuration file 2020-08-03
# version 13
# ------------------------------------

# Copyright (C) 2006, 2007, 2008, 2009, 2012, 2013, 2014 <PERSON>

# Copying and distribution of this file, with or without modification, are
# permitted in any medium without royalty provided the copyright notice
# and this notice are preserved.


# Please read the davfs2.conf (5) man page for a description of the
# configuration options and syntax rules.


# Available options and default values
# ====================================

# General Options
# ---------------

# dav_user        davfs2            # system wide config file only
# dav_group       davfs2            # system wide config file only
# buf_size        16                 # KiByte

# WebDAV Related Options
# ----------------------

# use_proxy       1                 # system wide config file only
# proxy                             # system wide config file only
trust_ca_cert /etc/davfs2/certs/abus_chain.cer
# servercert                        # deprecated: use trust_ca_cert
trust_server_cert /etc/davfs2/certs/webdav.cer
#clientcert /etc/davfs2/certs/webdav.cer
# secrets         ~/.davfs2/secrets # user config file only
ask_auth        0
# use_locks       1
# lock_owner      <user-name>
# lock_timeout    1800              # seconds
# lock_refresh    60                # seconds
# use_expect100   0
# if_match_bug    0
# drop_weak_etags 0
# n_cookies       0
# precheck        1
# ignore_dav_header 0
# use_compression 0
# min_propset     0
# follow_redirect 0
# sharepoint_href_bug 0
# server_charset
# connect_timeout 10                # seconds
# read_timeout    30                # seconds
# retry           30                # seconds
# max_retry       300               # seconds
# add_header

# Cache Related Options
# ---------------------

# backup_dir      lost+found
# cache_dir       /var/cache/davfs2 # system wide cache
#                 ~/.davfs2/cache   # per user cache
# cache_size      50                # MiByte
# table_size      1024
# dir_refresh     60                # seconds
# file_refresh    1                 # second
# delay_upload    10
# gui_optimize    0
# minimize_mem    0

# Debugging Options
# -----------------

# debug           # possible values: config, kernel, cache, http, xml,
                  #      httpauth, locks, ssl, httpbody, secrets, most

debug ssl