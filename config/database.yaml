parameters:
  ### Portal Datenbank
  env(PORTAL_DATABASE_HOST): portal_db
  env(PORTAL_DATABASE_USER): root

  database_driver: pdo_mysql
  database_host: '%env(PORTAL_DATABASE_HOST)%'
  database_port: null
  database_name: portal
  database_user: '%env(PORTAL_DATABASE_USER)%'
  database_password: '%env(PORTAL_DATABASE_PASSWORD)%'
  database_charset: UTF8

  ### News Datenbank
  env(NEWS_DATABASE_HOST): portal_db
  env(NEWS_DATABASE_USER): news

  news_database_driver: pdo_mysql
  news_database_host: '%env(NEWS_DATABASE_HOST)%'
  news_database_port: null
  news_database_name: portal_news
  news_database_user: '%env(NEWS_DATABASE_USER)%'
  news_database_password: '%env(NEWS_DATABASE_PASSWORD)%'
  news_database_charset: UTF8

  ### Information Datenbank
  env(INFORMATION_DATABASE_HOST): portal_db
  env(INFORMATION_DATABASE_USER): information

  information_database_driver: pdo_mysql
  information_database_host: '%env(INFORMATION_DATABASE_HOST)%'
  information_database_port: null
  information_database_name: portal_information
  information_database_user: '%env(INFORMATION_DATABASE_USER)%'
  information_database_password: '%env(INFORMATION_DATABASE_PASSWORD)%'
  information_database_charset: UTF8
  information_upload_dir: /var/www/data/information

  ### Bewerber Datenbank
  env(APPLICATION_DATABASE_HOST): abus_website_2016_db
  env(APPLICATION_DATABASE_USER): www_career

  application_database_driver: pdo_mysql
  application_database_host: '%env(APPLICATION_DATABASE_HOST)%'
  application_database_port: null
  application_database_name: www_career
  application_database_user: '%env(APPLICATION_DATABASE_USER)%'
  application_database_password: '%env(APPLICATION_DATABASE_PASSWORD)%'
  application_database_charset: UTF8

  ### Bewerber Datenbank - REMOTE => Liegt auf den eigenständigen Hanslog Server
  env(APPLICATION_REMOTE_DATABASE_HOST): **************
  env(APPLICATION_REMOTE_DATABASE_USER): hansalog

  applicationRemote_database_driver: pdo_mysql
  applicationRemote_database_host: '%env(APPLICATION_REMOTE_DATABASE_HOST)%'
  applicationRemote_database_port: 3306
  applicationRemote_database_name: hansalog
  applicationRemote_database_user: '%env(APPLICATION_REMOTE_DATABASE_USER)%'
  applicationRemote_database_password: '%env(APPLICATION_REMOTE_DATABASE_PASSWORD)%'

  ### Technische Daten Datenbank
  env(TDATA_DATABASE_HOST): portal_db
  env(TDATA_DATABASE_USER): tdata_b

  tdata_database_driver: pdo_mysql
  tdata_database_host: '%env(TDATA_DATABASE_HOST)%'
  tdata_database_port: null
  tdata_database_name: tdata_b
  tdata_database_user: '%env(TDATA_DATABASE_USER)%'
  tdata_database_password: '%env(TDATA_DATABASE_PASSWORD)%'
  tdata_database_charset: UTF8

  ### Technische Daten Datenbank - PREVIEW
  env(TDATA_PREVIEW_DATABASE_HOST): portal_db
  env(TDATA_PREVIEW_DATABASE_USER): tdata_a

  tdata_preview_database_driver: pdo_mysql
  tdata_preview_database_host: '%env(TDATA_PREVIEW_DATABASE_HOST)%'
  tdata_preview_database_port: null
  tdata_preview_database_name: tdata_a
  tdata_preview_database_user: '%env(TDATA_PREVIEW_DATABASE_USER)%'
  tdata_preview_database_password: '%env(TDATA_PREVIEW_DATABASE_PASSWORD)%'
  tdata_preview_database_charset: UTF8

  ### Website Datenbank
  env(WEBSITE_DATABASE_HOST): portal_db
  env(WEBSITE_DATABASE_USER): www_2014_11_1

  website_database_driver: pdo_mysql
  website_database_host: '%env(WEBSITE_DATABASE_HOST)%'
  website_database_port: null
  website_database_name: www_2014_11_1
  website_database_user: '%env(WEBSITE_DATABASE_USER)%'
  website_database_password: '%env(WEBSITE_DATABASE_PASSWORD)%'
  website_database_charset: UTF8

  ### Media Datenbank
  env(MEDIA_DATABASE_HOST): abus_media_db
  env(MEDIA_DATABASE_USER): media

  media_database_driver: pdo_mysql
  media_database_host: '%env(MEDIA_DATABASE_HOST)%'
  media_database_port: null
  media_database_name: media
  media_database_user: '%env(MEDIA_DATABASE_USER)%'
  media_database_password: '%env(MEDIA_DATABASE_PASSWORD)%'
  media_database_charset: UTF8

  ### ABUS Box Datenbank
  env(BOX_DATABASE_HOST): portal_db
  env(BOX_DATABASE_USER): portal_box

  storage_database_driver: pdo_mysql
  storage_database_host: '%env(BOX_DATABASE_HOST)%'
  storage_database_port: null
  storage_database_name: portal_box
  storage_database_user: '%env(BOX_DATABASE_USER)%'
  storage_database_password: '%env(BOX_DATABASE_PASSWORD)%'
  storage_database_charset: UTF8

  ### Catalog Creator 2020 Datenbank
  env(CATALOG2020_DATABASE_HOST): abus_abuetk2024_db
  env(CATALOG2020_DATABASE_USER): cc_read

  catalog2020_database_driver: pdo_mysql
  catalog2020_database_host: '%env(CATALOG2020_DATABASE_HOST)%'
  catalog2020_database_port: null
  catalog2020_database_name: abuetk
  catalog2020_database_user: '%env(CATALOG2020_DATABASE_USER)%'
  catalog2020_database_password: '%env(CATALOG2020_DATABASE_PASSWORD)%'
  catalog2020_database_charset: UTF8

  ### ABUKonfis Datenbank
  env(ABUKONFIS_DATABASE_HOST): *************
  env(ABUKONFIS_DATABASE_USER): portal_admin

  abukonfis_database_driver: pdo_mysql
  abukonfis_database_host: '%env(ABUKONFIS_DATABASE_HOST)%'
  abukonfis_database_port: 3306
  abukonfis_database_name: userdb
  abukonfis_database_user: '%env(ABUKONFIS_DATABASE_USER)%'
  abukonfis_database_password: '%env(ABUKONFIS_DATABASE_PASSWORD)%'
  abukonfis_database_charset: UTF8

  ### TaskRunner Datenbank
  env(TASKRUNNER_DATABASE_HOST): portal_db
  env(TASKRUNNER_DATABASE_USER): portal_taskrunner

  taskrunner_database_driver: pdo_mysql
  taskrunner_database_host: '%env(TASKRUNNER_DATABASE_HOST)%'
  taskrunner_database_port: null
  taskrunner_database_name: portal_taskrunner
  taskrunner_database_user: '%env(TASKRUNNER_DATABASE_USER)%'
  taskrunner_database_password: '%env(TASKRUNNER_DATABASE_PASSWORD)%'
  taskrunner_database_charset: UTF8

  ### UserManagement Datenbank
  env(USERMANAGEMENT_DATABASE_HOST): portal_db
  env(USERMANAGEMENT_DATABASE_USER): portal_usermanagement

  usermanagement_database_driver: pdo_mysql
  usermanagement_database_host: '%env(USERMANAGEMENT_DATABASE_HOST)%'
  usermanagement_database_port: null
  usermanagement_database_name: portal_usermanagement
  usermanagement_database_user: '%env(USERMANAGEMENT_DATABASE_USER)%'
  usermanagement_database_password: '%env(USERMANAGEMENT_DATABASE_PASSWORD)%'
  usermanagement_database_charset: UTF8

  ### Nextcloud Datenbank
  env(NEXTCLOUD_DATABASE_HOST): nextcloud_db
  env(NEXTCLOUD_DATABASE_USER): nextcloud

  nextcloud_database_driver: pdo_mysql
  nextcloud_database_host: '%env(NEXTCLOUD_DATABASE_HOST)%'
  nextcloud_database_port: null
  nextcloud_database_name: nextcloud
  nextcloud_database_user: '%env(NEXTCLOUD_DATABASE_USER)%'
  nextcloud_database_password: '%env(NEXTCLOUD_DATABASE_PASSWORD)%'
  nextcloud_database_charset: UTF8

  pimcore_database_driver: pdo_mysql
  pimcore_database_host: '%env(PIMCORE_DATABASE_HOST)%'
  pimcore_database_port: 3306
  pimcore_database_name: '%env(PIMCORE_DATABASE_NAME)%'
  pimcore_database_user: '%env(PIMCORE_DATABASE_USER)%'
  pimcore_database_password: '%env(PIMCORE_DATABASE_PASSWORD)%'
  pimcore_database_charset: UTF8
