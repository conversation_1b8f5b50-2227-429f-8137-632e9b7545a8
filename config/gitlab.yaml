parameters:
    gitlab_url: 'https://gitlab.abus-kransysteme.de/api/v4/'
    gitlab_private_token: '%env(GITLAB_TOKEN)%'
    gitlab_upload_directory: '/reportProblem/'
    gitlab_projectid: 47
    gitlab_projects_subfolder: 'projects/'
    gitlab_proxyhost: '*************:8080'
    gitlab_title: 'Portal: '
    gitlab_labels:
        bug: 'bug'
        suggestion: 'suggestion'
        improvement: 'enhancement'

    gitlab:
        url: '%gitlab_url%'
        upload_directory: '%gitlab_upload_directory%'
        private_token: '%gitlab_private_token%'
        projectid: '%gitlab_projectid%'
        projects_subfolder: '%gitlab_projects_subfolder%'
        proxyhost: '%gitlab_proxyhost%'
        title: '%gitlab_title%'
        labels: '%gitlab_labels%'
