monolog:
  channels: ['abus']
  use_microseconds: false
  handlers:
    symfonyError:
      type: fingers_crossed
      action_level: Error
      handler: graylog
      #            channels:     [cache, console, doctrine, event, php, request, translation]
      channels: [cache, console, doctrine, php, request, translation]

    symfonyWarning:
      type: fingers_crossed
      action_level: WARNING
      handler: graylog
      channels: [security]

    abus:
      type: fingers_crossed
      action_level: debug
      handler: graylog
      channels: [abus]

    #        graylog:
    #            type:         gelf
    #            publisher:
    #                hostname: '%env(GRAYLOG_SERVER)%'
    #                port:     '%env(GRAYLOG_PORT)%'
    #            level:        debug
    #            bubble:       true

    # File Log Ersatz
    graylog:
      type: stream
      path: '%kernel.logs_dir%/%kernel.environment%.log'
      level: debug
