parameters:
    technicalData:

        ###
        # Welche LDAP Gruppe darf auf welche Produkte zugreifen?
        # Wenn weitere Gruppen hier angelegt werden sollen, muss diesen auch der Zugriff auf tdata. über die Security.yml erlaubt werden
        ###
        ROLE_PORTAL_TECHNICAL_DATA_ALL.availableProducts:                        ['EOT cranes', 'Jib cranes', 'Mobile gantry cranes', 'Wire rope hoists', 'Chain hoists with trolley', 'Stationary chain hoists', 'Crane drives', 'HB-systems']
        ROLE_PORTAL_TECHNICAL_DATA_LAUFKRAN.availableProducts:                   ['EOT cranes', 'Jib cranes', 'Mobile gantry cranes', 'Wire rope hoists', 'Chain hoists with trolley', 'Stationary chain hoists', 'Crane drives', 'HB-systems']
        ROLE_PORTAL_TECHNICAL_DATA_KIT.availableProducts:                        ['EOT cranes', 'Jib cranes', 'Mobile gantry cranes', 'Wire rope hoists', 'Chain hoists with trolley', 'Stationary chain hoists', 'Crane drives', 'HB-systems']
        ROLE_PORTAL_TECHNICAL_DATA_RESELLER.availableProducts:                   ['Jib cranes', 'Mobile gantry cranes', 'Chain hoists with trolley', 'Stationary chain hoists', 'HB-systems']
        ROLE_PORTAL_TECHNICAL_DATA_EXTERNAL_PLANNING.availableProducts:          ['EOT cranes', 'Jib cranes', 'Mobile gantry cranes', 'HB-systems']
        ROLE_PORTAL_TECHNICAL_DATA_EXTERNAL_MOUNTING_PARTNER.availableProducts:  ['EOT cranes', 'Jib cranes', 'Mobile gantry cranes', 'Wire rope hoists', 'Chain hoists with trolley', 'Stationary chain hoists', 'Crane drives', 'HB-systems']

        ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_ALL.availableProducts:                 ['EOT cranes', 'Jib cranes', 'Mobile gantry cranes', 'Wire rope hoists', 'Chain hoists with trolley', 'Stationary chain hoists', 'Crane drives', 'HB-systems']
        ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_EXT_DB_ALL.availableProducts:          ['EOT cranes', 'Jib cranes', 'Mobile gantry cranes', 'Wire rope hoists', 'Chain hoists with trolley', 'Stationary chain hoists', 'Crane drives', 'HB-systems']
        ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_EXT_SK_LPK_KZUG_HB.availableProducts:  ['Jib cranes', 'Mobile gantry cranes', 'Chain hoists with trolley', 'Stationary chain hoists', 'HB-systems']
        ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_EXT_DB_LK_SK_LPK_HB.availableProducts: ['EOT cranes', 'Jib cranes', 'Mobile gantry cranes', 'HB-systems']

        tdata_base_dir: /var/www/data/tdata/tdata_a
        #tdata_preview_base_dir: /var/www/data/tdata/tdata_a
        tdata_download_limit_per_last_30_days: 200

        ### LAUFKRANE
        #EOT_CRANES.sql: SELECT DISTINCT SQL_CALC_FOUND_ROWS t1.krbez AS "Crane type", t1.var AS "Ver", if(t2.szsystem=3,"Energy chain","Festoon cable") AS "Mains power system", t1.kabez AS "Trolley type", t1.hw AS "Hook path [mm]", concat(t1.vhubkl,"/",t1.vhubgr) AS "Lifting [m/min]", concat(t2.btrspng,"/",t2.freq) AS "Operating voltage [V/Hz]", concat("/LK/",t2.ktyp,"/",t1.krbez,"_",t2.szsystem,"_",round(t1.vkrfgr),"_",t1.kabez,".gb.pdf") AS "PDF_GB", concat("/LK/",t2.ktyp,"/",t1.krbez,"_",t2.szsystem,"_",round(t1.vkrfgr),"_",t1.kabez,".d.pdf") AS "PDF_D" FROM y_reihenrech_erg_zus1 AS t1 INNER JOIN y_reihenrech_gen as t2 ON (t1.LoginID = t2.LoginID) AND (t1.RNr = t2.RNr) AND (t1.PosNr = t2.PosNr) AND (t1.PosNrAnl = t2.PosNrAnl) AND (t1.lfdNr = t2.lfdNr) AND (t1.lfdNrKa = t2.lfdNrKa) WHERE (t1.erg="OK") AND (t1.rnr in (81214,81216,81218,81224,82217,82220)) ---ADDITIONAL_CONDITIONS---  ORDER BY FIND_IN_SET(t2.ktyp,"ELV,ELK,ELS,ZLK,ZLK-B,DLVM,EDL"),t1.gl,t1.sw
        EOT_CRANES.sql: SELECT DISTINCT SQL_CALC_FOUND_ROWS t1.krbez AS "Crane type", t1.var AS "Ver", if(t2.szsystem=3,"Energy chain","Festoon cable") AS "Mains power system", t1.kabez AS "Trolley type", t1.hw AS "Hook path [mm]", concat(t1.vhubkl,"/",t1.vhubgr) AS "Lifting [m/min]", CONCAT(CASE t2.krfuehrsystem WHEN 1 THEN "wheel flange guided" WHEN 2 THEN "guide rollers" ELSE "XXXXXXXXXX" END) AS "Crane guiding system", concat(t2.btrspng,"/",t2.freq) AS "Operating voltage [V/Hz]", concat("/LK/",t2.ktyp,"/",t1.krbez,"_",t2.szsystem,"_",round(t1.vkrfgr),"_",t2.krfuehrsystem,"_",t1.kabez,".gb.pdf") AS "PDF_GB", concat("/LK/",t2.ktyp,"/",t1.krbez,"_",t2.szsystem,"_",round(t1.vkrfgr),"_",t2.krfuehrsystem,"_",t1.kabez,".d.pdf") AS "PDF_D" FROM y_reihenrech_erg_zus1 AS t1 INNER JOIN y_reihenrech_gen as t2 ON (t1.LoginID = t2.LoginID) AND (t1.RNr = t2.RNr) AND (t1.PosNr = t2.PosNr) AND (t1.PosNrAnl = t2.PosNrAnl) AND (t1.lfdNr = t2.lfdNr) AND (t1.lfdNrKa = t2.lfdNrKa) WHERE (t1.erg="OK") AND (t1.rnr in (81214,81216,81218,81224,82217,82220,83214,83216,83218)) ---ADDITIONAL_CONDITIONS---  ORDER BY FIND_IN_SET(t2.ktyp,"ELV,ELK,ELS,ZLK,ZLK-B,DLVM,EDL"),t1.gl,t1.sw, CASE WHEN t1.kabez LIKE "GM 800%" THEN 1 WHEN t1.kabez LIKE "GM 1000%" THEN 2 WHEN t1.kabez LIKE "GM 2000%" THEN 3 WHEN t1.kabez LIKE "GM 3000%" THEN 4 WHEN t1.kabez LIKE "GM 5000%" THEN 5 WHEN t1.kabez LIKE "GM 7000%" THEN 6 ELSE 7 END

        EOT_CRANES.sqlFieldName.crane_type: ktyp
        EOT_CRANES.sqlFieldType.crane_type: string
        EOT_CRANES.sqlOperator.crane_type: =

        EOT_CRANES.sqlFieldName.load_capacity: gl
        EOT_CRANES.sqlFieldType.load_capacity: integer
        EOT_CRANES.sqlOperator.load_capacity: =

        EOT_CRANES.sqlFieldName.operation_voltage: concat(btrspng,"/",freq)
        EOT_CRANES.sqlFieldType.operation_voltage: string
        EOT_CRANES.sqlOperator.operation_voltage: =

        EOT_CRANES.sqlFieldName.span: sw
        EOT_CRANES.sqlFieldType.span: integer
        EOT_CRANES.sqlOperator.span: =

        EOT_CRANES.sqlFieldName.crane_guiding_system: krfuehrsystem
        EOT_CRANES.sqlFieldType.crane_guiding_system: integer
        EOT_CRANES.sqlOperator.crane_guiding_system: =

        ### SCHWENKKRANE
        JIB_CRANES.sql: SELECT DISTINCT SQL_CALC_FOUND_ROWS t1.krbez as "Crane type",t1.ukmass as "Height of lower edge of jib [mm]",t1.bauhoe as "Overall height [mm]",t1.kabez as "Trolley type", if(t1.vkfkl=0,"manual",concat(t1.vkfkl,"/",t1.vkfgr)) as "Drive [m/min]", if(t1.vSchwGr=0,"manual","electrical") as "Slewing", if(t1.vhubkl=0,"manual",concat(t1.vhubkl,"/",t1.vhubgr)) as "Lifting [m/min]" ,t1.hoeha as "Highest hook position [mm]", CONCAT(CASE befest WHEN 1 THEN "wall bracket / wall bearing" WHEN 12 THEN "foundation with anchor bolts" WHEN 13 THEN "foundation with anchor bolts" WHEN 19 THEN "dowel plate" WHEN 20 THEN "dowel plate" WHEN 21 THEN "weld-on plates" WHEN 25 THEN "embracing bracket" ELSE "XXXXXXXXXX" END) AS "Mounting" ,concat(t2.btrspng,"/",t2.freq) as "Operating voltage [V/Hz]" ,concat("/SK/",t2.ktyp,"/",replace(t1.krbez,":","")," - ",replace(t1.kabez,"/"," "),".",t1.ukmass,".",IF(t1.befest=13,12,t1.befest),".gb.pdf") as "PDF_GB", concat("/SK/",t2.ktyp,"/",replace(t1.krbez,":","")," - ",replace(t1.kabez,"/"," "),".",t1.ukmass,".",IF(t1.befest=13,12,t1.befest),".d.pdf") as "PDF_D" FROM y_reihenrech_sk_erg AS t1 INNER JOIN y_reihenrech_gen as t2 ON (t1.LoginID = t2.LoginID) AND (t1.RNr = t2.RNr) AND (t1.PosNr = t2.PosNr) AND (t1.PosNrAnl = t2.PosNrAnl) AND (t1.lfdNr = t2.lfdNr) WHERE (t1.erg="OK") AND (t1.rnr in (33,34,35,36,37)) ---ADDITIONAL_CONDITIONS--- ORDER BY FIND_IN_SET(t2.ktyp,"VS,LS,LSX,VW,LW,LWX"),t1.gl,t1.al,t1.ukmass,t1.vkfgr,t2.freq,t2.btrspng

        JIB_CRANES.sqlFieldName.crane_type: t2.ktyp
        JIB_CRANES.sqlFieldType.crane_type: string
        JIB_CRANES.sqlOperator.crane_type: =

        JIB_CRANES.sqlFieldName.load_capacity: t1.gl
        JIB_CRANES.sqlFieldType.load_capacity: integer
        JIB_CRANES.sqlOperator.load_capacity: =

        JIB_CRANES.sqlFieldName.operation_voltage: concat(t2.btrspng,"/",t2.freq)
        JIB_CRANES.sqlFieldType.operation_voltage: string
        JIB_CRANES.sqlOperator.operation_voltage: =

        JIB_CRANES.sqlFieldName.jib_length: t1.al
        JIB_CRANES.sqlFieldType.jib_length: integer
        JIB_CRANES.sqlOperator.jib_length: =

        JIB_CRANES.sqlFieldName.slewing: t1.vSchwGr
        JIB_CRANES.sqlFieldType.slewing: float
        JIB_CRANES.sqlOperator.slewing: IN
        JIB_CRANES.sqlRewrite.slewing:

        JIB_CRANES.sqlFieldName.drive: concat(t1.vkfkl,"/",t1.vkfgr)
        JIB_CRANES.sqlFieldType.drive: string
        JIB_CRANES.sqlOperator.drive: =
        JIB_CRANES.sqlRewrite.drive:
            manual: 0/0

        JIB_CRANES.sqlFieldName.lifting: concat(t1.vhubkl,"/",t1.vhubgr)
        JIB_CRANES.sqlFieldType.lifting: string
        JIB_CRANES.sqlOperator.lifting: =

        JIB_CRANES.sqlFieldName.height_of_lower_edge_of_jib: t1.ukmass
        JIB_CRANES.sqlFieldType.height_of_lower_edge_of_jib: integer
        JIB_CRANES.sqlOperator.height_of_lower_edge_of_jib: =

        JIB_CRANES.sqlFieldName.mounting: t1.befest
        JIB_CRANES.sqlFieldType.mounting: string
        JIB_CRANES.sqlOperator.mounting: IN

        ### LEICHTPORTALKRANE

        MOBILE_GANTRY_CRANES.sql: SELECT DISTINCT SQL_CALC_FOUND_ROWS t1.krbez AS "Crane type", t1.gesb AS "Total width [mm]" ,t1.gesh AS "Total height [mm]", t1.gesbi AS "Clear width [mm]", t1.hoeha AS "Highest hook position [mm]", t1.kabez AS "Trolley type", if(t1.vkfkl=0,"manual","electrical") as "Trolley travel" ,if(t1.vhubkl=0,"manual",concat(t1.vhubkl,"/",t1.vhubgr)) as "Lifting [m/min]", CONCAT(t2.btrspng, "/", t2.freq) as "Operating voltage [V/Hz]", CONCAT("/LPK/", replace( replace( replace(t1.krbez," kg","")," mm","")," x ","x"),"x",t1.gesh,".",t1.szsys," - ",replace(t1.kabez,"/"," "),".gb.pdf") as "PDF_GB", CONCAT("/LPK/", replace( replace( replace(t1.krbez," kg","")," mm","")," x ","x"),"x",t1.gesh,".",t1.szsys," - ",replace(t1.kabez,"/"," "),".d.pdf") as "PDF_D" FROM y_reihenrech_lpk_erg AS t1 INNER JOIN y_reihenrech_gen as t2 ON (t1.LoginID = t2.LoginID) AND (t1.RNr = t2.RNr) AND (t1.PosNr = t2.PosNr) AND (t1.PosNrAnl = t2.PosNrAnl) AND (t1.lfdNr = t2.lfdNr) WHERE (t1.erg = "OK") AND (t1.RNr IN (60)) AND (t1.SZSys = 2) AND (t1.GL IN (250,320,400,500,630,800,1000,1250,1600,2000)) ---ADDITIONAL_CONDITIONS--- ORDER BY t1.gl,t1.gesb,t1.gesh,t1.vkfkl,t2.freq,t2.btrspng

        MOBILE_GANTRY_CRANES.sqlFieldName.load_capacity: t1.gl
        MOBILE_GANTRY_CRANES.sqlFieldType.load_capacity: integer
        MOBILE_GANTRY_CRANES.sqlOperator.load_capacity: =

        MOBILE_GANTRY_CRANES.sqlFieldName.trolley_travel: t1.vKfKl
        MOBILE_GANTRY_CRANES.sqlFieldType.trolley_travel: string
        MOBILE_GANTRY_CRANES.sqlOperator.trolley_travel: =

        MOBILE_GANTRY_CRANES.sqlFieldName.total_width: t1.GesB
        MOBILE_GANTRY_CRANES.sqlFieldType.total_width: integer
        MOBILE_GANTRY_CRANES.sqlOperator.total_width: =

        MOBILE_GANTRY_CRANES.sqlFieldName.total_height: t1.GesH
        MOBILE_GANTRY_CRANES.sqlFieldType.total_height: integer
        MOBILE_GANTRY_CRANES.sqlOperator.total_height: =

        MOBILE_GANTRY_CRANES.sqlFieldName.operation_voltage: concat(t2.btrspng,"/",t2.freq)
        MOBILE_GANTRY_CRANES.sqlFieldType.operation_voltage: string
        MOBILE_GANTRY_CRANES.sqlOperator.operation_voltage: =

        ### SEILZUEGE

        WIRE_ROPE_HOISTS.sql: SELECT DISTINCT SQL_CALC_FOUND_ROWS t1.kabez as "Trolley type", t1.bart as "Type", IF(t1.mgr=0,"C",t1.mgr) as "Hoist size", t1.hublst as "Load capacity [kg]", CONCAT(t1.vhubkl,"/",t1.vhubgr) AS "Lifting [m/min]", if(t1.bart="EKZ",if(t1.vkfkl=0,"manual",CONCAT(t1.vkfkl,"/",t1.vkfgr)),CONCAT(t1.vkfkl,"/",t1.vkfgr)) as "Drive [m/min]", CONCAT(t1.trbwgr,"/",t1.iso) AS "FEM/ISO", CONCAT(t1.strng,"/",t1.ablf) AS "Reeving", t1.hw as "Hook path [mm]", IF(t1.bart="EKZ",CONCAT(t1.bprmin," - ",t1.bprmax),t1.spur) as "Track [mm]", t1.lhnr as "Hook", CONCAT(t1.u1nenn, "/", t1.freq) as "Operating voltage [V/Hz]", CONCAT("/LKZ/",t1.bart,"/",t1.u1nenn,"_",t1.freq,"/",t4.pdfname) AS "PDF_GB", CONCAT("/LKZ/",t1.bart,"/",t1.u1nenn,"_",t1.freq,"/",t3.pdfname) AS "PDF_D", concat(t1.mgr,t1.hublst,t1.motausf,t1.vhubgr,t1.trbwgr,t1.strng,t1.ablf,t1.gen,t1.bart,t1.dl,t1.vkfgr) AS SILENT_bez_o_hw, t1.spur AS SILENT_spur FROM y_reihenrech_lkz_gen_2 AS t1, ( ---SUBSELECT--- ) AS t2, y_reihenrech_lkz_docs AS t3, y_reihenrech_lkz_docs AS t4 WHERE t1.erg = "OK" AND t1.loginid = t3.loginid AND t1.loginid = t4.loginid AND t1.rnr = t3.rnr AND t1.rnr = t4.rnr AND t1.kabez = t3.bez AND t1.kabez = t4.bez AND t1.hw = t3.hw AND t1.hw = t4.hw AND t1.spur = t3.spur AND t1.spur = t4.spur AND t1.u1nenn = t3.u1nenn AND t1.u1nenn = t4.u1nenn AND t1.freq = t3.freq AND t1.freq = t4.freq AND t3.sprache = "d" AND t4.sprache = "gb" ---ADDITIONAL_CONDITIONS--- GROUP BY SILENT_bez_o_hw, t1.spur, t1.hw ORDER BY FIND_IN_SET(t1.bart,"EKZ,E,U,S,D,DA,DA4,DQA,DB,Z,ZA,ZB"), t1.mgr, t1.hublst, t1.iso, t1.hw, t1.spur, t1.vhubgr, t1.vkfgr, t1.strng
        WIRE_ROPE_HOISTS.sqlSubSelect: SELECT DISTINCT MIN(t1.hw) AS hw_ij, IF(t1.bart="EKZ",t1.spur,MIN(t1.spur)) AS spur_ij, concat(t1.mgr,t1.hublst,t1.motausf,t1.vhubgr,t1.trbwgr,t1.strng,t1.ablf,t1.gen,t1.bart,t1.dl,t1.vkfgr,IF(t1.bart="EKZ",t1.spur,"")) AS bez_o_hw_ij FROM y_reihenrech_lkz_gen_2 AS t1 WHERE (t1.erg = "OK") ---ADDITIONAL_CONDITIONS---	GROUP BY bez_o_hw_ij

        WIRE_ROPE_HOISTS.sqlFieldName.type: t1.bart
        WIRE_ROPE_HOISTS.sqlFieldType.type: string
        WIRE_ROPE_HOISTS.sqlOperator.type: =

        WIRE_ROPE_HOISTS.sqlFieldName.load_capacity: t1.hublst
        WIRE_ROPE_HOISTS.sqlFieldType.load_capacity: integer
        WIRE_ROPE_HOISTS.sqlOperator.load_capacity: =

        WIRE_ROPE_HOISTS.sqlFieldName.operation_voltage: concat(t1.u1nenn,"/",t1.freq)
        WIRE_ROPE_HOISTS.sqlFieldType.operation_voltage: string
        WIRE_ROPE_HOISTS.sqlOperator.operation_voltage: LIKE

        WIRE_ROPE_HOISTS.sqlFieldName.hoist_size: t1.mgr
        WIRE_ROPE_HOISTS.sqlFieldType.hoist_size: integer
        WIRE_ROPE_HOISTS.sqlOperator.hoist_size: =

        WIRE_ROPE_HOISTS.sqlFieldName.reeving: concat(t1.strng,"/",t1.ablf)
        WIRE_ROPE_HOISTS.sqlFieldType.reeving: string
        WIRE_ROPE_HOISTS.sqlOperator.reeving: =

        WIRE_ROPE_HOISTS.sqlFieldName.drive: t1.vkfgrnenn
        WIRE_ROPE_HOISTS.sqlFieldType.drive: integer
        WIRE_ROPE_HOISTS.sqlOperator.drive: =
        #WIRE_ROPE_HOISTS.sqlRewrite.drive:
        #  manual: '0.0'

        WIRE_ROPE_HOISTS.sqlFieldName.lifting: t1.vhubgrnenn
        WIRE_ROPE_HOISTS.sqlFieldType.lifting: string
        WIRE_ROPE_HOISTS.sqlOperator.lifting: =

        WIRE_ROPE_HOISTS.sqlFieldName.hook_path: t1.hw
        WIRE_ROPE_HOISTS.sqlFieldType.hook_path: integer
        WIRE_ROPE_HOISTS.sqlOperator.hook_path: '>='
        WIRE_ROPE_HOISTS.sqlCondition.hook_path: AND
        WIRE_ROPE_HOISTS.resultSQL_addition.hook_path: AND (t1.hw = t2.hw_ij)

        WIRE_ROPE_HOISTS.sqlFieldName.fem_iso: t1.iso
        WIRE_ROPE_HOISTS.sqlFieldType.fem_iso: string
        WIRE_ROPE_HOISTS.sqlOperator.fem_iso: '>='

        WIRE_ROPE_HOISTS.sqlFieldName.track: IF(t1.bart IN ("E","U","S"),1,(t1.spur = "---VALUE---"))
        WIRE_ROPE_HOISTS.sqlFieldType.track: string
        WIRE_ROPE_HOISTS.sqlOperator.track: IF
        WIRE_ROPE_HOISTS.resultSQL_addition.track: AND IF(t1.bart IN ("E","U","S"),1,(t1.spur = t2.spur_ij))


        ### KRANFAHRWERKE

        CRANE_DRIVES.sql: SELECT DISTINCT SQL_CALC_FOUND_ROWS t1.fwtbez as "Crane drive", t1.fwttypkurz as "Type", t1.dl as "Wheel dia. [mm]", if(t1.fwtausf LIKE "spurkranzg%","wheel flange guided","guide rollers") as "Type of drive", t1.fmabst as "Spacing [mm]", t1.hpr as "Height of end carriage [mm]", t1.vfwt as "Drive [m/min]", t1.rkr as "Wheel base [mm]", t1.hsch as "Screw clearance [mm]", t1.trbwgr as "FEM group", t1.anzant as "SILENT_Ant", t1.lst as "Rating [kW]", t1.rmax as "SILENT_Rmax [kN]", t1.swmax as "SILENT_SWmax [mm]", t1.dp as "SILENT_DP [mm]", CONCAT(t1.u1nenn, "/", t1.freq) as "Operating voltage [V/Hz]", CONCAT("/FWT/",t1.fwttypkurz,"/",t1.u1nenn,"_",t1.freq,"/",t1.fwtbez,if(t1.fwtausf LIKE "spurkranzg%",".sp.",".fr."),if(t1.fmabst IS NULL,CONCAT(t1.bprmin, ".", t1.bprmax),t1.fmabst),".",round(if(t1.freq=50,t1.vfwt,t1.vfwt*1.2)),".gb.pdf") as "PDF_GB", CONCAT("/FWT/",t1.fwttypkurz,"/",t1.u1nenn,"_",t1.freq,"/",t1.fwtbez,if(t1.fwtausf LIKE "spurkranzg%",".sp.",".fr."),if(t1.fmabst IS NULL,CONCAT(t1.bprmin, ".", t1.bprmax),t1.fmabst),".",round(if(t1.freq=50,t1.vfwt,t1.vfwt*1.2)),".d.pdf") as "PDF_D" FROM y_reihenrech_fwt_erg AS t1 WHERE t1.erg = "OK" ---ADDITIONAL_CONDITIONS--- ORDER BY FIND_IN_SET(t1.fwttypkurz, "EL,ZL,ZL8,ED"),t1.dl,t1.fwtausf desc,t1.fmabst,t1.hpr,t1.vfwt,t1.rkr,t1.hsch,t1.ksp,t1.vplatte,t1.trbwgr,t1.bprmin

        CRANE_DRIVES.sqlFieldName.operation_voltage: concat(t1.u1nenn,"/",t1.freq)
        CRANE_DRIVES.sqlFieldType.operation_voltage: string
        CRANE_DRIVES.sqlOperator.operation_voltage: =

        CRANE_DRIVES.sqlFieldName.type: t1.fwttypkurz
        CRANE_DRIVES.sqlFieldType.type: string
        CRANE_DRIVES.sqlOperator.type: =

        CRANE_DRIVES.sqlFieldName.type_of_drive: t1.fwtausf
        CRANE_DRIVES.sqlFieldType.type_of_drive: string
        CRANE_DRIVES.sqlOperator.type_of_drive: =

        CRANE_DRIVES.sqlFieldName.wheel: t1.dl
        CRANE_DRIVES.sqlFieldType.wheel: integer
        CRANE_DRIVES.sqlOperator.wheel: =

        CRANE_DRIVES.sqlFieldName.wheel_base: t1.rkr
        CRANE_DRIVES.sqlFieldType.wheel_base: integer
        CRANE_DRIVES.sqlOperator.wheel_base: =

        CRANE_DRIVES.sqlFieldName.fem_group: t1.trbwgr
        CRANE_DRIVES.sqlFieldType.fem_group: string
        CRANE_DRIVES.sqlOperator.fem_group: =

        CRANE_DRIVES.sqlFieldName.drive: t1.vfwt
        CRANE_DRIVES.sqlFieldType.drive: integer
        CRANE_DRIVES.sqlOperator.drive: =

        CRANE_DRIVES.sqlFieldName.spacing: t1.fmabst
        CRANE_DRIVES.sqlFieldType.spacing: integer
        CRANE_DRIVES.sqlOperator.spacing: =

        ### KETTENZÜGE STATIONÄR

        STATIONARY_CHAIN_HOISTS.sql: SELECT DISTINCT SQL_CALC_FOUND_ROWS t1.bezhebez as "Trolley type", t1.bart as "Type", IF(t1.mgr=0,"C",t1.mgr) as "Hoist size", t1.hublst as "Load capacity [kg]", CONCAT(t1.vhubkl,"/",t1.vhubgr) AS "Lifting [m/min]", CONCAT(t1.trbwgr,"/",t1.iso) AS "FEM/ISO", CONCAT(t1.strng,"/",t1.ablf) AS "Reeving", t1.hw as "Hook path [mm]", t1.lhnr as "Hook", CONCAT(t1.u1nenn, "/", t1.freq) as "Operating voltage [V/Hz]", CONCAT("/LKZ/",t1.bart,"/",t1.u1nenn,"_",t1.freq,"/",t4.pdfname) AS "PDF_GB", CONCAT("/LKZ/",t1.bart,"/",t1.u1nenn,"_",t1.freq,"/",t3.pdfname) AS "PDF_D", concat(t1.mgr,t1.hublst,t1.motausf,t1.vhubgr,t1.trbwgr,t1.strng,t1.ablf,t1.gen,t1.bart) AS SILENT_bez_o_hw FROM y_reihenrech_lkz_gen_2 AS t1, ( ---SUBSELECT--- ) AS t2, y_reihenrech_lkz_docs AS t3, y_reihenrech_lkz_docs AS t4 WHERE t1.erg = "OK" AND t1.loginid = t3.loginid AND t1.loginid = t4.loginid AND t1.rnr = t3.rnr AND t1.rnr = t4.rnr AND t1.bezhebez = t3.bez AND t1.bezhebez = t4.bez AND t1.hw = t3.hw AND t1.hw = t4.hw AND t1.spur = t3.spur AND t1.spur = t4.spur AND t1.u1nenn = t3.u1nenn AND t1.u1nenn = t4.u1nenn AND t1.freq = t3.freq AND t1.freq = t4.freq AND t3.sprache = "d" AND t4.sprache = "gb" ---ADDITIONAL_CONDITIONS--- GROUP BY SILENT_bez_o_hw, t1.spur, t1.hw ORDER BY FIND_IN_SET(t1.bart,"EKZ,E,U,S,D,DA,DA4,DQA,DB,Z,ZA,ZB"), t1.mgr, t1.hublst, t1.iso, t1.hw, t1.spur, t1.vhubgr, t1.vkfgr, t1.strng
        STATIONARY_CHAIN_HOISTS.sqlSubSelect: SELECT DISTINCT MIN(t1.hw) AS hw_ij, IF(t1.bart="EKZ",t1.spur,MIN(t1.spur)) AS spur_ij, concat(t1.mgr,t1.hublst,t1.motausf,t1.vhubgr,t1.trbwgr,t1.strng,t1.ablf,t1.gen,t1.bart) AS bez_o_hw_ij FROM y_reihenrech_lkz_gen_2 AS t1 WHERE (t1.erg = "OK") ---ADDITIONAL_CONDITIONS--- GROUP BY bez_o_hw_ij

        STATIONARY_CHAIN_HOISTS.sqlFieldName.operation_voltage: concat(t1.u1nenn,"/",t1.freq)
        STATIONARY_CHAIN_HOISTS.sqlFieldType.operation_voltage: string
        STATIONARY_CHAIN_HOISTS.sqlOperator.operation_voltage: =

        STATIONARY_CHAIN_HOISTS.sqlFieldName.load_capacity: t1.hublst
        STATIONARY_CHAIN_HOISTS.sqlFieldType.load_capacity: integer
        STATIONARY_CHAIN_HOISTS.sqlOperator.load_capacity: =

        STATIONARY_CHAIN_HOISTS.sqlFieldName.hoist_size: IF(t1.mgr=0,"c",t1.mgr)
        STATIONARY_CHAIN_HOISTS.sqlFieldType.hoist_size: string
        STATIONARY_CHAIN_HOISTS.sqlOperator.hoist_size: =

        STATIONARY_CHAIN_HOISTS.sqlFieldName.reeving: concat(t1.strng,"/",t1.ablf)
        STATIONARY_CHAIN_HOISTS.sqlFieldType.reeving: string
        STATIONARY_CHAIN_HOISTS.sqlOperator.reeving: =

        STATIONARY_CHAIN_HOISTS.sqlFieldName.lifting: t1.vhubgrnenn
        STATIONARY_CHAIN_HOISTS.sqlFieldType.lifting: string
        STATIONARY_CHAIN_HOISTS.sqlOperator.lifting: =

        STATIONARY_CHAIN_HOISTS.sqlFieldName.hook_path: t1.hw
        STATIONARY_CHAIN_HOISTS.sqlFieldType.hook_path: integer
        STATIONARY_CHAIN_HOISTS.sqlOperator.hook_path: '>='
        STATIONARY_CHAIN_HOISTS.sqlCondition.hook_path: AND
        STATIONARY_CHAIN_HOISTS.resultSQL_addition.hook_path: AND (t1.hw = t2.hw_ij)

        STATIONARY_CHAIN_HOISTS.sqlFieldName.fem_iso: t1.iso
        STATIONARY_CHAIN_HOISTS.sqlFieldType.fem_iso: string
        STATIONARY_CHAIN_HOISTS.sqlOperator.fem_iso: '>='

        ### KETTENZÜGE MIT FAHRWERK

        CHAIN_HOISTS_WITH_TROLLEY.sql: SELECT DISTINCT SQL_CALC_FOUND_ROWS t1.kabez as "Trolley type", t1.bart as "Type", IF(t1.mgr=0,"C",t1.mgr) as "Hoist size", t1.hublst as "Load capacity [kg]", CONCAT(t1.vhubkl,"/",t1.vhubgr) AS "Lifting [m/min]", if(t1.bart="EKZ",if(t1.vkfkl=0,"manual",CONCAT(t1.vkfkl,"/",t1.vkfgr)),CONCAT(t1.vkfkl,"/",t1.vkfgr)) as "Drive [m/min]", CONCAT(t1.trbwgr,"/",t1.iso) AS "FEM/ISO", CONCAT(t1.strng,"/",t1.ablf) AS "Reeving", t1.hw as "Hook path [mm]", IF(t1.bart="EKZ",CONCAT(t1.bprmin," - ",t1.bprmax),t1.spur) as "Track [mm]", t1.lhnr as "Hook", CONCAT(t1.u1nenn, "/", t1.freq) as "Operating voltage [V/Hz]", CONCAT("/LKZ/",t1.bart,"/",t1.u1nenn,"_",t1.freq,"/",t4.pdfname) AS "PDF_GB", CONCAT("/LKZ/",t1.bart,"/",t1.u1nenn,"_",t1.freq,"/",t3.pdfname) AS "PDF_D", concat(t1.mgr,t1.hublst,t1.motausf,t1.vhubgr,t1.trbwgr,t1.strng,t1.ablf,t1.gen,t1.bart,t1.dl,t1.vkfgr) AS SILENT_bez_o_hw FROM y_reihenrech_lkz_gen_2 AS t1, ( ---SUBSELECT--- ) AS t2, y_reihenrech_lkz_docs AS t3, y_reihenrech_lkz_docs AS t4 WHERE t1.erg = "OK" AND t1.loginid = t3.loginid AND t1.loginid = t4.loginid AND t1.rnr = t3.rnr AND t1.rnr = t4.rnr AND t1.kabez = t3.bez AND t1.kabez = t4.bez AND t1.hw = t3.hw AND t1.hw = t4.hw AND t1.spur = t3.spur AND t1.spur = t4.spur AND t1.u1nenn = t3.u1nenn AND t1.u1nenn = t4.u1nenn AND t1.freq = t3.freq AND t1.freq = t4.freq AND t3.sprache = "d" AND t4.sprache = "gb" ---ADDITIONAL_CONDITIONS--- GROUP BY SILENT_bez_o_hw, t1.spur, t1.hw ORDER BY FIND_IN_SET(t1.bart,"EKZ,E,U,S,D,DA,DA4,DQA,DB,Z,ZA,ZB"), t1.mgr, t1.hublst, t1.iso, t1.hw, t1.spur, t1.vhubgr, t1.vkfgr, t1.strng
        CHAIN_HOISTS_WITH_TROLLEY.sqlSubSelect: SELECT DISTINCT MIN(t1.hw) AS hw_ij, IF(t1.bart="EKZ",t1.spur,MIN(t1.spur)) AS spur_ij, concat(t1.mgr,t1.hublst,t1.motausf,t1.vhubgr,t1.trbwgr,t1.strng,t1.ablf,t1.gen,t1.bart,t1.dl,t1.vkfgr,IF(t1.bart="EKZ",t1.spur,"")) AS bez_o_hw_ij FROM y_reihenrech_lkz_gen_2 AS t1 WHERE (t1.erg = "OK") ---ADDITIONAL_CONDITIONS---	GROUP BY bez_o_hw_ij

        CHAIN_HOISTS_WITH_TROLLEY.sqlFieldName.operation_voltage: concat(t1.u1nenn,"/",t1.freq)
        CHAIN_HOISTS_WITH_TROLLEY.sqlFieldType.operation_voltage: string
        CHAIN_HOISTS_WITH_TROLLEY.sqlOperator.operation_voltage: LIKE

        CHAIN_HOISTS_WITH_TROLLEY.sqlFieldName.load_capacity: t1.hublst
        CHAIN_HOISTS_WITH_TROLLEY.sqlFieldType.load_capacity: integer
        CHAIN_HOISTS_WITH_TROLLEY.sqlOperator.load_capacity: =

        CHAIN_HOISTS_WITH_TROLLEY.sqlFieldName.hoist_size: IF(t1.mgr=0,"c",t1.mgr)
        CHAIN_HOISTS_WITH_TROLLEY.sqlFieldType.hoist_size: string
        CHAIN_HOISTS_WITH_TROLLEY.sqlOperator.hoist_size: =

        CHAIN_HOISTS_WITH_TROLLEY.sqlFieldName.reeving: concat(t1.strng,"/",t1.ablf)
        CHAIN_HOISTS_WITH_TROLLEY.sqlFieldType.reeving: string
        CHAIN_HOISTS_WITH_TROLLEY.sqlOperator.reeving: =

        CHAIN_HOISTS_WITH_TROLLEY.sqlFieldName.drive: t1.vkfgrnenn
        CHAIN_HOISTS_WITH_TROLLEY.sqlFieldType.drive: string
        CHAIN_HOISTS_WITH_TROLLEY.sqlOperator.drive: =
        CHAIN_HOISTS_WITH_TROLLEY.sqlRewrite.drive:
            manual: '0.0'

        CHAIN_HOISTS_WITH_TROLLEY.sqlFieldName.lifting: t1.vhubgrnenn
        CHAIN_HOISTS_WITH_TROLLEY.sqlFieldType.lifting: string
        CHAIN_HOISTS_WITH_TROLLEY.sqlOperator.lifting: =

        CHAIN_HOISTS_WITH_TROLLEY.sqlFieldName.hook_path: t1.hw
        CHAIN_HOISTS_WITH_TROLLEY.sqlFieldType.hook_path: integer
        CHAIN_HOISTS_WITH_TROLLEY.sqlOperator.hook_path: '>='
        CHAIN_HOISTS_WITH_TROLLEY.resultSQL_addition.hook_path: AND (t1.hw = t2.hw_ij)

        CHAIN_HOISTS_WITH_TROLLEY.sqlFieldName.fem_iso: t1.iso
        CHAIN_HOISTS_WITH_TROLLEY.sqlFieldType.fem_iso: string
        CHAIN_HOISTS_WITH_TROLLEY.sqlOperator.fem_iso: '>='
