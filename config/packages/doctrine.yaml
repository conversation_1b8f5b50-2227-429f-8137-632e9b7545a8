doctrine:
    dbal:
        default_connection:   default
        connections:
            default:
                driver:   '%database_driver%'
                host:     '%database_host%'
                port:     '%database_port%'
                dbname:   '%database_name%'
                user:     '%database_user%'
                password: '%database_password%'
                charset:  UTF8
            storage:
                driver:   '%storage_database_driver%'
                host:     '%storage_database_host%'
                port:     '%storage_database_port%'
                dbname:   '%storage_database_name%'
                user:     '%storage_database_user%'
                password: '%storage_database_password%'
                charset:  '%storage_database_charset%'
            taskrunner:
                driver:   '%taskrunner_database_driver%'
                host:     '%taskrunner_database_host%'
                port:     '%taskrunner_database_port%'
                dbname:   '%taskrunner_database_name%'
                user:     '%taskrunner_database_user%'
                password: '%taskrunner_database_password%'
                charset:  '%taskrunner_database_charset%'
            apis:
                driver:   '%information_database_driver%'
                host:     '%information_database_host%'
                port:     '%information_database_port%'
                dbname:   '%information_database_name%'
                user:     '%information_database_user%'
                password: '%information_database_password%'
                charset:  '%information_database_charset%'
            news:
                driver:   '%news_database_driver%'
                host:     '%news_database_host%'
                port:     '%news_database_port%'
                dbname:   '%news_database_name%'
                user:     '%news_database_user%'
                password: '%news_database_password%'
                charset:  '%news_database_charset%'
            usermanagement:
                driver:   '%usermanagement_database_driver%'
                host:     '%usermanagement_database_host%'
                port:     '%usermanagement_database_port%'
                dbname:   '%usermanagement_database_name%'
                user:     '%usermanagement_database_user%'
                password: '%usermanagement_database_password%'
                charset:  '%usermanagement_database_charset%'
            pimcore:
                driver: '%pimcore_database_driver%'
                host: '%pimcore_database_host%'
                port: '%pimcore_database_port%'
                dbname: '%pimcore_database_name%'
                user: '%pimcore_database_user%'
                password: '%pimcore_database_password%'
                charset: '%pimcore_database_charset%'
                schema_filter: ~^(abus_appointment_)~

        # IMPORTANT: You MUST configure your server version,
        # either here or in the DATABASE_URL env var (see .env file)
        #server_version: '13'

        # only needed for MySQL
        #charset: utf8mb4
        #default_table_options:
        #    collate: utf8mb4_unicode_ci

        # backtrace queries in profiler (increases memory usage per request)
        #profiling_collect_backtrace: '%kernel.debug%'

    orm:
        default_entity_manager: default
        entity_managers:
            default:
                connection: default
                mappings:
                    Api:
                        prefix: App\Entity\Api
                        type: attribute
                        dir: '%kernel.project_dir%/src/Entity/Api'
                        is_bundle: false

            storage:
                connection: storage
                mappings:
                    Box:
                        prefix: App\Entity\Box
                        type: attribute
                        dir: '%kernel.project_dir%/src/Entity/Box'
                        is_bundle: false

            taskrunner:
                connection: taskrunner
                mappings:
                    TaskRunner:
                        prefix: App\Entity\TaskRunner
                        type: attribute
                        dir: '%kernel.project_dir%/src/Entity/TaskRunner'
                        is_bundle: false

            apis:
                connection: apis
                naming_strategy: doctrine.orm.naming_strategy.underscore_number_aware
                mappings:
                    APIS:
                        prefix: App\Entity\APIS
                        type: attribute
                        dir: '%kernel.project_dir%/src/Entity/APIS'
                        is_bundle: false

            news:
                connection: news
                mappings:
                    NEWS:
                        prefix: App\Entity\News
                        type: attribute
                        dir: '%kernel.project_dir%/src/Entity/News'
                        is_bundle: false

            pimcore:
                connection: pimcore
                naming_strategy: doctrine.orm.naming_strategy.underscore_number_aware
                auto_mapping: true
                mappings:
                    Pimcore:
                        prefix: App\Entity\Pimcore
                        type: attribute
                        dir: '%kernel.project_dir%/src/Entity/Pimcore'
                        is_bundle: false

            usermanagement:
                connection: usermanagement
                mappings:
                    Api:
                        prefix: App\Entity\Api
                        type: attribute
                        dir: '%kernel.project_dir%/src/Entity/Api'
                        is_bundle: false
#                    UserManagement:
#                        prefix: App\Entity\Api
#                        type: annotation
#                        dir: '%kernel.project_dir%/src/Entity/Api'
#                        is_bundle: false
#           auto_generate_proxy_classes: true
#               naming_strategy: doctrine.orm.naming_strategy.underscore_number_aware
#               auto_mapping: true
#               mappings:
#                   App:
#                       is_bundle: false
#                       type: annotation
#                       dir: '%kernel.project_dir%/src/Entity'
#                       prefix: 'App\Entity'
#                       alias: App
