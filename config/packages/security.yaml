# To get started with security, check out the documentation:
# http://symfony.com/doc/current/book/security.html
security:
    password_hashers:
        App\Security\User\ABUSUser: plaintext

    # https://symfony.com/doc/current/book/security.html#where-do-users-come-from-user-providers
    providers:
#        app_users:
#            id: App\Security\User\ABUSUserProvider
        mainick_keycloak_user_provider:
            id: Mainick\KeycloakClientBundle\Security\User\KeycloakUserProvider

    firewalls:
        # disables authentication for assets and the profiler, adapt it according to your needs
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false

        apisimages:
            pattern:  ^/apis/(sales|mounting|service|technical)/(\d+)/imageobjects/anonymous
            lazy: true
#            logout_on_user_change: true

        auth_connect:
          pattern: /auth/keycloak/connect
          security: false

        secured_area:
          pattern: ^/
          provider: mainick_keycloak_user_provider
          entry_point: Mainick\KeycloakClientBundle\Security\EntryPoint\KeycloakAuthenticationEntryPoint
          custom_authenticator:
            - <PERSON>ick\KeycloakClientBundle\Security\Authenticator\KeycloakAuthenticator
          logout:
            path: mainick_keycloak_security_auth_logout

          # activate different ways to authenticate
          # https://symfony.com/doc/current/security.html#the-firewall

          # https://symfony.com/doc/current/security/impersonating_user.html
          # switch_user: true

    role_hierarchy:
      PORTAL_2018: [ 'ROLE_PORTAL_2018' ]

#        main:
#            form_login:
#                login_path: login
#                check_path: login
#                use_referer: true
#                enable_csrf: true
##                csrf_token_generator: security.csrf.token_manager
#            logout:
##                handler: [App\EventListener\LogoutListener]
#                path:   /logout
#                target: /login
#                invalidate_session: true
##            logout_on_user_change: false
#            switch_user: { role: ROLE_PORTAL_ADMIN_USERMODE, parameter: _username }
#            custom_authenticators:
#              - App\Security\FormLoginAuthenticator
#            entry_point: form_login

    access_control:

# ABUS Vital
        - { path: ^/$, methods: [GET], host: ^vital\., roles: ['PUBLIC_ACCESS'] }
        - { path: ^/event/\d+$, methods: [GET], host: ^vital\., roles: ['PUBLIC_ACCESS'] }
        - { path: ^/save$, methods: [POST], host: ^vital\., roles: ['PUBLIC_ACCESS'] }
        - { path: ^/update/\d+$, methods: [GET], host: vital\., roles: ['PUBLIC_ACCESS'] }

# ABUS BOX

        #  Dinge die nur ADMINS dürfen
        #
        #  abus_box_api_administration                       GET        ANY      ANY    /box/api/administration/{path}
        #  abus_box_api_getDataSize                          GET        ANY      ANY    /box/api/administration/getDataSize
        #  abus_box_crawl                                    GET        ANY      ANY    /box/crawl
        #  abus_box_api_administration_store                 POST       ANY      ANY    /box/api/administration/store
        - { path: ^/box(/api/administration|/crawl), methods: [GET, POST], roles: ['ROLE_PORTAL_BOX_ADMIN', 'ROLE_PORTAL_ADMIN'] }

        #  Dinge die ALLE dürfen
        #
        #  abus_box_home                                     GET        ANY      ANY    /box
        #  abus_box_download                                 GET        ANY      ANY    /box/download/{path}
        #  abus_box_api_root                                 GET        ANY      ANY    /box/api/get/{path}
        #  abus_box_api_search                               GET        ANY      ANY    /box/api/search
        #  abus_box_api_download_zip                         POST       ANY      ANY    /box/api/downloadZip
        #  abus_box_nextcloud_root                           GET        ANY      ANY    /box/nextcloud/get/{path}
        - { path: ^/box(/api)?, methods: [GET, POST], roles: ['IS_AUTHENTICATED_FULLY'] }

# ABUS Teams
        #  abus_frame_teams_project                          GET        ANY      ANY    /teams/projects/{project}
        #  abus_frame_teams                                  GET        ANY      ANY    /teams/projects
        #  abus_frame_teams_homepage                         GET        ANY      ANY    /teams
        -
          path: ^/teams
          methods: [GET]
          roles:
              - 'ROLE_WEB_TEAMS_ADMINISTRATOR'
              - 'ROLE_WEB_TEAMS_MANAGER'
              - 'ROLE_WEB_TEAMS_MODERATOR_ABUS'
              - 'ROLE_WEB_TEAMS_MODERATOR_PARTNER'
              - 'ROLE_WEB_TEAMS_BENUTZER_ABUS'
              - 'ROLE_WEB_TEAMS_BENUTZER_PARTNER'
              - 'ROLE_WEB_TEAMS_BENUTZER_EXTERN'

        # Darf nur vom DockerContainer selbst aufgerufen werden
        - { path: ^/apis/(sales|mounting|service|technical)/(\d+)/imageobjects/anonymous, allow_if: "request.headers.get('x-forwarded-for') matches '/^172\\./'" }


# ABUS Informationen SALES

        # Dinge die nur ADMINS und EDITOREN dürfen
        #
        # REGEX: ^(\/apisadmin(\/create\/sales|\/receiverlist/sales|\/reference\/search|\/nextnumber|\/create\/sales|\/reference\/save\/sales|\/flip\/attachment\/sales|\/upload\/attachment\/([^\/]*)\/sales|\/global\/attachment\/sales|\/reference\/delete\/sales|\/delete\/attachment\/sales))|(\/apis\/report\/sales)
        # /apisadmin/create/sales
        # /apisadmin/receiverlist/sales
        # /apisadmin/reference/search/sales
        # /apisadmin/nextnumber/sales
        # /apisadmin/create/sales/{number}
        # /apisadmin/reference/save/sales/{number}
        # /apisadmin/flip/attachment/sales/{number}/{file}
        # /apisadmin/upload/attachment/{language}/sales/{number}
        # /apisadmin/global/attachment/sales/{number}/{file}
        # /apisadmin/reference/delete/sales/{informationNumber}/{referenceNumber}
        # /apisadmin/delete/attachment/sales/{number}/{file}
        # /apis/report/sales/{number}
        #
        #
        #  apis_backend_create                               GET        ANY      ANY    /apisadmin/create/{section}
        #  apis_ajax_backend_receiver_list                   GET        ANY      ANY    /apisadmin/receiverlist/{section}
        #  apis_ajax_backend_search_reference                GET        ANY      ANY    /apisadmin/reference/search/{section}
        #  apis_ajax_backend_nextnumber                      GET        ANY      ANY    /apisadmin/nextnumber/{section}
        #  apis_ajax_report                                  GET        ANY      ANY    /apis/report/{section}/{number}
        #  apis_ajax_backend_create                          POST       ANY      ANY    /apisadmin/create/{section}/{number}
        #  apis_ajax_backend_save_reference                  POST       ANY      ANY    /apisadmin/reference/save/{section}/{number}
        #  apis_ajax_backend_attachment_flip                 POST       ANY      ANY    /apisadmin/flip/attachment/{section}/{number}/{file}
        #  apis_ajax_backend_attachment_upload               POST       ANY      ANY    /apisadmin/upload/attachment/{language}/{section}/{number}
        #  apis_ajax_backend_delete_reference                DELETE     ANY      ANY    /apisadmin/reference/delete/{section}/{informationNumber}/{referenceNumber}
        #  apis_ajax_backend_attachment_delete               DELETE     ANY      ANY    /apisadmin/delete/attachment/{section}/{number}/{file}
        #  apis_ajax_backend_attachment_global               PATCH      ANY      ANY    /apisadmin/global/attachment/{section}/{number}/{file}
        -
          path: ^(/apisadmin(/create/sales|/receiverlist/sales|/reference/search/sales|/nextnumber/sales|/create/sales|/reference/save/sales|/flip/attachment/sales|/upload/attachment/([^/]*)/sales|/global/attachment/sales|/reference/delete/sales|/delete/attachment/sales))|(/apis/report/sales)
          methods: [GET, POST, DELETE, PATCH]
          roles:
              - 'ROLE_PORTAL_INFORMATION_SALES_ADMIN'
              - 'ROLE_PORTAL_INFORMATION_SALES_EDITOR'

        # Dinge die nur ADMINS, EDITOREN und TRANSLATOR dürfen
        #
        # REGEX ^\/apisadmin(\/save|\/state\/change|\/load|\/edit|\/load\/edit)?\/sales
        # /apisadmin/sales
        # /apisadmin/load/sales/{state}
        # /apisadmin/edit/sales/{number}
        # /apisadmin/load/edit/sales/{number}
        # /apisadmin/save/sales/{number}
        # /apisadmin/state/change/sales/{number}/{transition}
        #
        #
        #  apis_backend_index                                GET        ANY      ANY    /apisadmin/{section}
        #  apis_ajax_backend_load                            GET        ANY      ANY    /apisadmin/load/{section}/{state}
        #  apis_backend_edit                                 GET        ANY      ANY    /apisadmin/edit/{section}/{number}
        #  apis_ajax_backend_edit                            GET        ANY      ANY    /apisadmin/load/edit/{section}/{number}
        #  apis_ajax_backend_save                            POST       ANY      ANY    /apisadmin/save/{section}/{number}
        #  apis_ajax_backend_state_change                    POST       ANY      ANY    /apisadmin/state/change/{section}/{number}/{transition}
        -
          path: ^/apisadmin(/save|/state/change|/load|/edit|/load/edit)?/sales
          methods: [GET, POST]
          roles:
              - 'ROLE_PORTAL_INFORMATION_SALES_ADMIN'
              - 'ROLE_PORTAL_INFORMATION_SALES_EDITOR'
              - 'ROLE_PORTAL_INFORMATION_SALES_TRANSLATOR'

        # Dinge die alle dürfen
        #
        # REGEX ^\/apis(\/load|\/read|\/viewed|\/favorites|\/settings)?\/sales
        # /apis/settings/sales
        # /apis/sales
        # /apis/sales/{number}
        # /apis/sales/print/{number}
        # /apis/sales/pdf/{number}
        # /apis/sales/{number}/imageobjects/{filename}
        # /apis/sales/{number}/imageobjects/anonymous/{filename}
        # /apis/sales/{number}/attachment/{filename}
        # /apis/load/sales}/{limit}/{page}
        # /apis/read/sales/{number}
        # /apis/viewed/sales/{number}
        # /apis/favorites/sales/{number}
        # /apis/settings/sales
        # /apis/favorites/sales/{number}
        #
        #
        #  apis_ajax_settings_load                           GET        ANY      ANY    /apis/settings/{section}
        #  apis_frontend_index                               GET        ANY      ANY    /apis/{section}
        #  apis_frontend_index_single                        GET        ANY      ANY    /apis/{section}/{number}
        #  apis_frontend_print                               GET        ANY      ANY    /apis/{section}/print/{number}
        #  apis_frontend_pdf                                 GET        ANY      ANY    /apis/{section}/pdf/{number}
        #  apis_frontend_images                              GET        ANY      ANY    /apis/{section}/{number}/imageobjects/{filename}
        #  apis_frontend_images_anonymous                    GET        ANY      ANY    /apis/{section}/{number}/imageobjects/anonymous/{filename}
        #  apis_frontend_attachment                          GET        ANY      ANY    /apis/{section}/{number}/attachment/{filename}
        #  apis_ajax_paged                                   POST       ANY      ANY    /apis/load/{section}/{limit}/{page}
        #  apis_ajax_marked_as_read                          POST       ANY      ANY    /apis/read/{section}/{number}
        #  apis_ajax_viewed                                  POST       ANY      ANY    /apis/viewed/{section}/{number}
        #  apis_ajax_add_favorites                           POST       ANY      ANY    /apis/favorites/{section}/{number}
        #  apis_ajax_settings_store                          POST       ANY      ANY    /apis/settings/{section}
        #  apis_ajax_remove_favorites                        DELETE     ANY      ANY    /apis/favorites/{section}/{number}
        -
          path: ^/apis(/load|/read|/viewed|/favorites|/settings)?/sales
          methods: [GET, POST, DELETE]
          roles:
              - 'ROLE_PORTAL_INFORMATION_SALES_ADMIN'
              - 'ROLE_PORTAL_INFORMATION_SALES_EDITOR'
              - 'ROLE_PORTAL_INFORMATION_SALES_TRANSLATOR'
              - 'ROLE_PORTAL_INFORMATION_SALES_INTERN'
              - 'ROLE_PORTAL_INFORMATION_SALES_INLAND'
              - 'ROLE_PORTAL_INFORMATION_SALES_EXPORT'

# ABUS Informationen SERVICE

        # Dinge die nur ADMINS und EDITOREN dürfen
        #
        # REGEX: ^(\/apisadmin(\/create\/service|\/receiverlist|\/reference\/search|\/nextnumber|\/create\/service|\/reference\/save\/service|\/flip\/attachment\/service|\/upload\/attachment\/([^\/]*)\/service|\/global\/attachment\/service|\/reference\/delete\/service|\/delete\/attachment\/service))|(\/apis\/report\/service)
        # /apisadmin/create/service
        # /apisadmin/receiverlist/service
        # /apisadmin/reference/search/service
        # /apisadmin/nextnumber/service
        # /apisadmin/create/service/{number}
        # /apisadmin/reference/save/service/{number}
        # /apisadmin/flip/attachment/service/{number}/{file}
        # /apisadmin/upload/attachment/{language}/service/{number}
        # /apisadmin/global/attachment/service/{number}/{file}
        # /apisadmin/reference/delete/service/{informationNumber}/{referenceNumber}
        # /apisadmin/delete/attachment/service/{number}/{file}
        # /apis/report/service/{number}
        #
        #
        #  apis_backend_create                               GET        ANY      ANY    /apisadmin/create/{section}
        #  apis_ajax_backend_receiver_list                   GET        ANY      ANY    /apisadmin/receiverlist/{section}
        #  apis_ajax_backend_search_reference                GET        ANY      ANY    /apisadmin/reference/search/{section}
        #  apis_ajax_backend_nextnumber                      GET        ANY      ANY    /apisadmin/nextnumber/{section}
        #  apis_ajax_report                                  GET        ANY      ANY    /apis/report/{section}/{number}
        #  apis_ajax_backend_create                          POST       ANY      ANY    /apisadmin/create/{section}/{number}
        #  apis_ajax_backend_save_reference                  POST       ANY      ANY    /apisadmin/reference/save/{section}/{number}
        #  apis_ajax_backend_attachment_flip                 POST       ANY      ANY    /apisadmin/flip/attachment/{section}/{number}/{file}
        #  apis_ajax_backend_attachment_upload               POST       ANY      ANY    /apisadmin/upload/attachment/{language}/{section}/{number}
        #  apis_ajax_backend_delete_reference                DELETE     ANY      ANY    /apisadmin/reference/delete/{section}/{informationNumber}/{referenceNumber}
        #  apis_ajax_backend_attachment_delete               DELETE     ANY      ANY    /apisadmin/delete/attachment/{section}/{number}/{file}
        #  apis_ajax_backend_attachment_global               PATCH      ANY      ANY    /apisadmin/global/attachment/{section}/{number}/{file}
        -
          path: ^(/apisadmin(/create/service|/receiverlist/service|/reference/search/service|/nextnumber/service|/create/service|/reference/save/service|/flip/attachment/service|/upload/attachment/([^/]*)/service|/global/attachment/service|/reference/delete/service|/delete/attachment/service))|(/apis/report/service)
          methods: [GET, POST, DELETE, PATCH]
          roles:
              - 'ROLE_PORTAL_INFORMATION_SERVICE_ADMIN'
              - 'ROLE_PORTAL_INFORMATION_SERVICE_EDITOR'

        # Dinge die nur ADMINS, EDITOREN und TRANSLATOR dürfen
        #
        # REGEX ^\/apisadmin(\/save|\/state\/change|\/load|\/edit|\/load\/edit)?\/service
        # /apisadmin/service
        # /apisadmin/load/service/{state}
        # /apisadmin/edit/service/{number}
        # /apisadmin/load/edit/service/{number}
        # /apisadmin/save/service/{number}
        # /apisadmin/state/change/service/{number}/{transition}
        #
        #
        #  apis_backend_index                                GET        ANY      ANY    /apisadmin/{section}
        #  apis_ajax_backend_load                            GET        ANY      ANY    /apisadmin/load/{section}/{state}
        #  apis_backend_edit                                 GET        ANY      ANY    /apisadmin/edit/{section}/{number}
        #  apis_ajax_backend_edit                            GET        ANY      ANY    /apisadmin/load/edit/{section}/{number}
        #  apis_ajax_backend_save                            POST       ANY      ANY    /apisadmin/save/{section}/{number}
        #  apis_ajax_backend_state_change                    POST       ANY      ANY    /apisadmin/state/change/{section}/{number}/{transition}
        -
          path: ^/apisadmin(/save|/state/change|/load|/edit|/load/edit)?/service
          methods: [GET, POST]
          roles:
              - 'ROLE_PORTAL_INFORMATION_SERVICE_ADMIN'
              - 'ROLE_PORTAL_INFORMATION_SERVICE_EDITOR'
              - 'ROLE_PORTAL_INFORMATION_SERVICE_TRANSLATOR'

        # Dinge die alle dürfen
        #
        # REGEX ^\/apis(\/load|\/read|\/viewed|\/favorites|\/settings)?\/service
        # /apis/settings/service
        # /apis/service
        # /apis/service/{number}
        # /apis/service/print/{number}
        # /apis/service/pdf/{number}
        # /apis/service/{number}/imageobjects/{filename}
        # /apis/service/{number}/imageobjects/anonymous/{filename}
        # /apis/service/{number}/attachment/{filename}
        # /apis/load/service}/{limit}/{page}
        # /apis/read/service/{number}
        # /apis/viewed/service/{number}
        # /apis/favorites/service/{number}
        # /apis/settings/service
        # /apis/favorites/service/{number}
        #
        #
        #  apis_ajax_settings_load                           GET        ANY      ANY    /apis/settings/{section}
        #  apis_frontend_index                               GET        ANY      ANY    /apis/{section}
        #  apis_frontend_index_single                        GET        ANY      ANY    /apis/{section}/{number}
        #  apis_frontend_print                               GET        ANY      ANY    /apis/{section}/print/{number}
        #  apis_frontend_pdf                                 GET        ANY      ANY    /apis/{section}/pdf/{number}
        #  apis_frontend_images                              GET        ANY      ANY    /apis/{section}/{number}/imageobjects/{filename}
        #  apis_frontend_images_anonymous                    GET        ANY      ANY    /apis/{section}/{number}/imageobjects/anonymous/{filename}
        #  apis_frontend_attachment                          GET        ANY      ANY    /apis/{section}/{number}/attachment/{filename}
        #  apis_ajax_paged                                   POST       ANY      ANY    /apis/load/{section}/{limit}/{page}
        #  apis_ajax_marked_as_read                          POST       ANY      ANY    /apis/read/{section}/{number}
        #  apis_ajax_viewed                                  POST       ANY      ANY    /apis/viewed/{section}/{number}
        #  apis_ajax_add_favorites                           POST       ANY      ANY    /apis/favorites/{section}/{number}
        #  apis_ajax_settings_store                          POST       ANY      ANY    /apis/settings/{section}
        #  apis_ajax_remove_favorites                        DELETE     ANY      ANY    /apis/favorites/{section}/{number}
        -
          path: ^/apis(/load|/read|/viewed|/favorites|/settings)?/service
          methods: [GET, POST, DELETE]
          roles:
              - 'ROLE_PORTAL_INFORMATION_SERVICE_ADMIN'
              - 'ROLE_PORTAL_INFORMATION_SERVICE_EDITOR'
              - 'ROLE_PORTAL_INFORMATION_SERVICE_TRANSLATOR'
              - 'ROLE_PORTAL_INFORMATION_SERVICE_INTERN'
              - 'ROLE_PORTAL_INFORMATION_SERVICE_TOECHTER'
              - 'ROLE_PORTAL_INFORMATION_SERVICE_EXPORTPARTNER'
              - 'ROLE_PORTAL_GRUPPE_MONTAGEPARTNER'
              - 'ROLE_PORTAL_GRUPPE_SERVICEPARTNER'
              - 'ROLE_PORTAL_GRUPPE_SERVICEPARTNER_MASTER'
              - 'ROLE_PORTAL_GRUPPE_ABNAHMEPARTNER'

# ABUS Informationen MOUNTING
        # Dinge die nur ADMINS und EDITOREN dürfen
        #
        # REGEX: ^(\/apisadmin(\/create\/mounting|\/receiverlist/mounting|\/reference\/search|\/nextnumber|\/create\/mounting|\/reference\/save\/mounting|\/flip\/attachment\/mounting|\/upload\/attachment\/([^\/]*)\/mounting|\/global\/attachment\/mounting|\/reference\/delete\/mounting|\/delete\/attachment\/mounting))|(\/apis\/report\/mounting)
        # /apisadmin/create/mounting
        # /apisadmin/receiverlist/mounting
        # /apisadmin/reference/search/mounting
        # /apisadmin/nextnumber/mounting
        # /apisadmin/create/mounting/{number}
        # /apisadmin/reference/save/mounting/{number}
        # /apisadmin/flip/attachment/mounting/{number}/{file}
        # /apisadmin/upload/attachment/{language}/mounting/{number}
        # /apisadmin/global/attachment/mounting/{number}/{file}
        # /apisadmin/reference/delete/mounting/{informationNumber}/{referenceNumber}
        # /apisadmin/delete/attachment/mounting/{number}/{file}
        # /apis/report/mounting/{number}
        #
        #
        #  apis_backend_create                               GET        ANY      ANY    /apisadmin/create/{section}
        #  apis_ajax_backend_receiver_list                   GET        ANY      ANY    /apisadmin/receiverlist/{section}
        #  apis_ajax_backend_search_reference                GET        ANY      ANY    /apisadmin/reference/search/{section}
        #  apis_ajax_backend_nextnumber                      GET        ANY      ANY    /apisadmin/nextnumber/{section}
        #  apis_ajax_report                                  GET        ANY      ANY    /apis/report/{section}/{number}
        #  apis_ajax_backend_create                          POST       ANY      ANY    /apisadmin/create/{section}/{number}
        #  apis_ajax_backend_save_reference                  POST       ANY      ANY    /apisadmin/reference/save/{section}/{number}
        #  apis_ajax_backend_attachment_flip                 POST       ANY      ANY    /apisadmin/flip/attachment/{section}/{number}/{file}
        #  apis_ajax_backend_attachment_upload               POST       ANY      ANY    /apisadmin/upload/attachment/{language}/{section}/{number}
        #  apis_ajax_backend_delete_reference                DELETE     ANY      ANY    /apisadmin/reference/delete/{section}/{informationNumber}/{referenceNumber}
        #  apis_ajax_backend_attachment_delete               DELETE     ANY      ANY    /apisadmin/delete/attachment/{section}/{number}/{file}
        #  apis_ajax_backend_attachment_global               PATCH      ANY      ANY    /apisadmin/global/attachment/{section}/{number}/{file}
        -
          path: ^(/apisadmin(/create/mounting|/receiverlist/mounting|/reference/search/mounting|/nextnumber/mounting|/create/mounting|/reference/save/mounting|/flip/attachment/mounting|/upload/attachment/([^/]*)/mounting|/global/attachment/mounting|/reference/delete/mounting|/delete/attachment/mounting))|(/apis/report/mounting)
          methods: [GET, POST, DELETE, PATCH]
          roles:
              - 'ROLE_PORTAL_INFORMATION_MOUNTING_ADMIN'
              - 'ROLE_PORTAL_INFORMATION_MOUNTING_EDITOR'

        # Dinge die nur ADMINS, EDITOREN und TRANSLATOR dürfen
        #
        # REGEX ^\/apisadmin(\/save|\/state\/change|\/load|\/edit|\/load\/edit)?\/mounting
        # /apisadmin/mounting
        # /apisadmin/load/mounting/{state}
        # /apisadmin/edit/mounting/{number}
        # /apisadmin/load/edit/mounting/{number}
        # /apisadmin/save/mounting/{number}
        # /apisadmin/state/change/mounting/{number}/{transition}
        #
        #
        #  apis_backend_index                                GET        ANY      ANY    /apisadmin/{section}
        #  apis_ajax_backend_load                            GET        ANY      ANY    /apisadmin/load/{section}/{state}
        #  apis_backend_edit                                 GET        ANY      ANY    /apisadmin/edit/{section}/{number}
        #  apis_ajax_backend_edit                            GET        ANY      ANY    /apisadmin/load/edit/{section}/{number}
        #  apis_ajax_backend_save                            POST       ANY      ANY    /apisadmin/save/{section}/{number}
        #  apis_ajax_backend_state_change                    POST       ANY      ANY    /apisadmin/state/change/{section}/{number}/{transition}
        -
          path: ^/apisadmin(/save|/state/change|/load|/edit|/load/edit)?/mounting
          methods: [GET, POST]
          roles:
              - 'ROLE_PORTAL_INFORMATION_MOUNTING_ADMIN'
              - 'ROLE_PORTAL_INFORMATION_MOUNTING_EDITOR'
              - 'ROLE_PORTAL_INFORMATION_MOUNTING_TRANSLATOR'

        # Dinge die alle dürfen
        #
        # REGEX ^\/apis(\/load|\/read|\/viewed|\/favorites|\/settings)?\/mounting
        # /apis/settings/mounting
        # /apis/mounting
        # /apis/mounting/{number}
        # /apis/mounting/print/{number}
        # /apis/mounting/pdf/{number}
        # /apis/mounting/{number}/imageobjects/{filename}
        # /apis/mounting/{number}/imageobjects/anonymous/{filename}
        # /apis/mounting/{number}/attachment/{filename}
        # /apis/load/mounting}/{limit}/{page}
        # /apis/read/mounting/{number}
        # /apis/viewed/mounting/{number}
        # /apis/favorites/mounting/{number}
        # /apis/settings/mounting
        # /apis/favorites/mounting/{number}
        #
        #
        #  apis_ajax_settings_load                           GET        ANY      ANY    /apis/settings/{section}
        #  apis_frontend_index                               GET        ANY      ANY    /apis/{section}
        #  apis_frontend_index_single                        GET        ANY      ANY    /apis/{section}/{number}
        #  apis_frontend_print                               GET        ANY      ANY    /apis/{section}/print/{number}
        #  apis_frontend_pdf                                 GET        ANY      ANY    /apis/{section}/pdf/{number}
        #  apis_frontend_images                              GET        ANY      ANY    /apis/{section}/{number}/imageobjects/{filename}
        #  apis_frontend_images_anonymous                    GET        ANY      ANY    /apis/{section}/{number}/imageobjects/anonymous/{filename}
        #  apis_frontend_attachment                          GET        ANY      ANY    /apis/{section}/{number}/attachment/{filename}
        #  apis_ajax_paged                                   POST       ANY      ANY    /apis/load/{section}/{limit}/{page}
        #  apis_ajax_marked_as_read                          POST       ANY      ANY    /apis/read/{section}/{number}
        #  apis_ajax_viewed                                  POST       ANY      ANY    /apis/viewed/{section}/{number}
        #  apis_ajax_add_favorites                           POST       ANY      ANY    /apis/favorites/{section}/{number}
        #  apis_ajax_settings_store                          POST       ANY      ANY    /apis/settings/{section}
        #  apis_ajax_remove_favorites                        DELETE     ANY      ANY    /apis/favorites/{section}/{number}
        -
          path: ^/apis(/load|/read|/viewed|/favorites|/settings)?/mounting
          methods: [GET, POST, DELETE]
          roles:
              - 'ROLE_PORTAL_INFORMATION_MOUNTING_ADMIN'
              - 'ROLE_PORTAL_INFORMATION_MOUNTING_EDITOR'
              - 'ROLE_PORTAL_INFORMATION_MOUNTING_TRANSLATOR'
              - 'ROLE_PORTAL_INFORMATION_MOUNTING_INTERN'
              - 'ROLE_PORTAL_INFORMATION_MOUNTING_AUFTRAGSZENTRUM_DE'
              - 'ROLE_PORTAL_GRUPPE_MONTAGEPARTNER'
              - 'ROLE_PORTAL_GRUPPE_SERVICEPARTNER'
              - 'ROLE_PORTAL_GRUPPE_SERVICEPARTNER_MASTER'
              - 'ROLE_PORTAL_GRUPPE_SERVICEPARTNER_BASIC'
              - 'ROLE_PORTAL_GRUPPE_ABNAHMEPARTNER'
              - 'ROLE_PORTAL_GRUPPE_TECHNISCHER_AUSSENDIENST'

# ABUS Informationen TECHNICAL
        # Dinge die nur ADMINS und EDITOREN dürfen
        #
        # REGEX: ^(\/apisadmin(\/create\/technical|\/receiverlist/technical|\/reference\/search|\/nextnumber|\/create\/technical|\/reference\/save\/technical|\/flip\/attachment\/technical|\/upload\/attachment\/([^\/]*)\/technical|\/global\/attachment\/technical|\/reference\/delete\/technical|\/delete\/attachment\/technical))|(\/apis\/report\/technical)
        # /apisadmin/create/technical
        # /apisadmin/receiverlist/technical
        # /apisadmin/reference/search/technical
        # /apisadmin/nextnumber/technical
        # /apisadmin/create/technical/{number}
        # /apisadmin/reference/save/technical/{number}
        # /apisadmin/flip/attachment/technical/{number}/{file}
        # /apisadmin/upload/attachment/{language}/technical/{number}
        # /apisadmin/global/attachment/technical/{number}/{file}
        # /apisadmin/reference/delete/technical/{informationNumber}/{referenceNumber}
        # /apisadmin/delete/attachment/technical/{number}/{file}
        # /apis/report/technical/{number}
        #
        #
        #  apis_backend_create                               GET        ANY      ANY    /apisadmin/create/{section}
        #  apis_ajax_backend_receiver_list                   GET        ANY      ANY    /apisadmin/receiverlist/{section}
        #  apis_ajax_backend_search_reference                GET        ANY      ANY    /apisadmin/reference/search/{section}
        #  apis_ajax_backend_nextnumber                      GET        ANY      ANY    /apisadmin/nextnumber/{section}
        #  apis_ajax_report                                  GET        ANY      ANY    /apis/report/{section}/{number}
        #  apis_ajax_backend_create                          POST       ANY      ANY    /apisadmin/create/{section}/{number}
        #  apis_ajax_backend_save_reference                  POST       ANY      ANY    /apisadmin/reference/save/{section}/{number}
        #  apis_ajax_backend_attachment_flip                 POST       ANY      ANY    /apisadmin/flip/attachment/{section}/{number}/{file}
        #  apis_ajax_backend_attachment_upload               POST       ANY      ANY    /apisadmin/upload/attachment/{language}/{section}/{number}
        #  apis_ajax_backend_delete_reference                DELETE     ANY      ANY    /apisadmin/reference/delete/{section}/{informationNumber}/{referenceNumber}
        #  apis_ajax_backend_attachment_delete               DELETE     ANY      ANY    /apisadmin/delete/attachment/{section}/{number}/{file}
        #  apis_ajax_backend_attachment_global               PATCH      ANY      ANY    /apisadmin/global/attachment/{section}/{number}/{file}
        -
          path: ^(/apisadmin(/create/technical|/receiverlist/technical|/reference/search/technical|/nextnumber/technical|/create/technical|/reference/save/technical|/flip/attachment/technical|/upload/attachment/([^/]*)/technical|/global/attachment/technical|/reference/delete/technical|/delete/attachment/technical))|(/apis/report/technical)
          methods: [GET, POST, DELETE, PATCH]
          roles:
              - 'ROLE_PORTAL_INFORMATION_TECHNIK_ADMIN'
              - 'ROLE_PORTAL_INFORMATION_TECHNIK_EDITOR'

        # Dinge die nur ADMINS, EDITOREN und TRANSLATOR dürfen
        #
        # REGEX ^\/apisadmin(\/save|\/state\/change|\/load|\/edit|\/load\/edit)?\/technical
        # /apisadmin/technical
        # /apisadmin/load/technical/{state}
        # /apisadmin/edit/technical/{number}
        # /apisadmin/load/edit/technical/{number}
        # /apisadmin/save/technical/{number}
        # /apisadmin/state/change/technical/{number}/{transition}
        #
        #
        #  apis_backend_index                                GET        ANY      ANY    /apisadmin/{section}
        #  apis_ajax_backend_load                            GET        ANY      ANY    /apisadmin/load/{section}/{state}
        #  apis_backend_edit                                 GET        ANY      ANY    /apisadmin/edit/{section}/{number}
        #  apis_ajax_backend_edit                            GET        ANY      ANY    /apisadmin/load/edit/{section}/{number}
        #  apis_ajax_backend_save                            POST       ANY      ANY    /apisadmin/save/{section}/{number}
        #  apis_ajax_backend_state_change                    POST       ANY      ANY    /apisadmin/state/change/{section}/{number}/{transition}
        -
          path: ^/apisadmin(/save|/state/change|/load|/edit|/load/edit)?/technical
          methods: [GET, POST]
          roles:
              - 'ROLE_PORTAL_INFORMATION_TECHNIK_ADMIN'
              - 'ROLE_PORTAL_INFORMATION_TECHNIK_EDITOR'
              - 'ROLE_PORTAL_INFORMATION_TECHNIK_TRANSLATOR'

        # Dinge die alle dürfen
        #
        # REGEX ^\/apis(\/load|\/read|\/viewed|\/favorites|\/settings)?\/technical
        # /apis/settings/technical
        # /apis/technical
        # /apis/technical/{number}
        # /apis/technical/print/{number}
        # /apis/technical/pdf/{number}
        # /apis/technical/{number}/imageobjects/{filename}
        # /apis/technical/{number}/imageobjects/anonymous/{filename}
        # /apis/technical/{number}/attachment/{filename}
        # /apis/load/technical}/{limit}/{page}
        # /apis/read/technical/{number}
        # /apis/viewed/technical/{number}
        # /apis/favorites/technical/{number}
        # /apis/settings/technical
        # /apis/favorites/technical/{number}
        #
        #
        #  apis_ajax_settings_load                           GET        ANY      ANY    /apis/settings/{section}
        #  apis_frontend_index                               GET        ANY      ANY    /apis/{section}
        #  apis_frontend_index_single                        GET        ANY      ANY    /apis/{section}/{number}
        #  apis_frontend_print                               GET        ANY      ANY    /apis/{section}/print/{number}
        #  apis_frontend_pdf                                 GET        ANY      ANY    /apis/{section}/pdf/{number}
        #  apis_frontend_images                              GET        ANY      ANY    /apis/{section}/{number}/imageobjects/{filename}
        #  apis_frontend_images_anonymous                    GET        ANY      ANY    /apis/{section}/{number}/imageobjects/anonymous/{filename}
        #  apis_frontend_attachment                          GET        ANY      ANY    /apis/{section}/{number}/attachment/{filename}
        #  apis_ajax_paged                                   POST       ANY      ANY    /apis/load/{section}/{limit}/{page}
        #  apis_ajax_marked_as_read                          POST       ANY      ANY    /apis/read/{section}/{number}
        #  apis_ajax_viewed                                  POST       ANY      ANY    /apis/viewed/{section}/{number}
        #  apis_ajax_add_favorites                           POST       ANY      ANY    /apis/favorites/{section}/{number}
        #  apis_ajax_settings_store                          POST       ANY      ANY    /apis/settings/{section}
        #  apis_ajax_remove_favorites                        DELETE     ANY      ANY    /apis/favorites/{section}/{number}
        -
          path: ^/apis(/load|/read|/viewed|/favorites|/settings)?/technical
          methods: [GET, POST, DELETE]
          roles:
              - 'ROLE_PORTAL_INFORMATION_TECHNIK_ADMIN'
              - 'ROLE_PORTAL_INFORMATION_TECHNIK_EDITOR'
              - 'ROLE_PORTAL_INFORMATION_TECHNIK_TRANSLATOR'
              - 'ROLE_PORTAL_INFORMATION_TECHNIK_INTERN'
              - 'ROLE_PORTAL_INFORMATION_TECHNIK_INLAND'
              - 'ROLE_PORTAL_INFORMATION_TECHNIK_TOECHTER'
              - 'ROLE_PORTAL_INFORMATION_TECHNIK_EXPORTPARTNER'
              - 'ROLE_PORTAL_GRUPPE_MONTAGEPARTNER'
              - 'ROLE_PORTAL_GRUPPE_SERVICEPARTNER'
              - 'ROLE_PORTAL_GRUPPE_SERVICEPARTNER_MASTER'
              - 'ROLE_PORTAL_GRUPPE_SERVICEPARTNER_BASIC'
              - 'ROLE_PORTAL_GRUPPE_ABNAHMEPARTNER'


# ABUS Informationen ALLGEMEIN

        #  apis_ajax_dashboard                               GET        ANY      ANY    /apis/dashboard
        -
          path: ^/apis/dashboard
          methods: [GET]
          roles:
              - 'ROLE_PORTAL_INFORMATION_SALES_ADMIN'
              - 'ROLE_PORTAL_INFORMATION_SALES_EDITOR'
              - 'ROLE_PORTAL_INFORMATION_SALES_TRANSLATOR'
              - 'ROLE_PORTAL_INFORMATION_SALES_INTERN'
              - 'ROLE_PORTAL_INFORMATION_SALES_INLAND'
              - 'ROLE_PORTAL_INFORMATION_SALES_EXPORT'
              - 'ROLE_PORTAL_INFORMATION_SERVICE_ADMIN'
              - 'ROLE_PORTAL_INFORMATION_SERVICE_EDITOR'
              - 'ROLE_PORTAL_INFORMATION_SERVICE_TRANSLATOR'
              - 'ROLE_PORTAL_INFORMATION_SERVICE_INTERN'
              - 'ROLE_PORTAL_INFORMATION_SERVICE_TOECHTER'
              - 'ROLE_PORTAL_INFORMATION_SERVICE_EXPORTPARTNER'
              - 'ROLE_PORTAL_INFORMATION_MOUNTING_ADMIN'
              - 'ROLE_PORTAL_INFORMATION_MOUNTING_EDITOR'
              - 'ROLE_PORTAL_INFORMATION_MOUNTING_INTERN'
              - 'ROLE_PORTAL_INFORMATION_MOUNTING_TRANSLATOR'
              - 'ROLE_PORTAL_INFORMATION_TECHNIK_ADMIN'
              - 'ROLE_PORTAL_INFORMATION_TECHNIK_EDITOR'
              - 'ROLE_PORTAL_INFORMATION_TECHNIK_TRANSLATOR'
              - 'ROLE_PORTAL_INFORMATION_TECHNIK_INTERN'
              - 'ROLE_PORTAL_INFORMATION_TECHNIK_INLAND'
              - 'ROLE_PORTAL_INFORMATION_TECHNIK_TOECHTER'
              - 'ROLE_PORTAL_INFORMATION_TECHNIK_EXPORTPARTNER'
              - 'ROLE_PORTAL_GRUPPE_MONTAGEPARTNER'
              - 'ROLE_PORTAL_GRUPPE_SERVICEPARTNER'
              - 'ROLE_PORTAL_GRUPPE_SERVICEPARTNER_MASTER'
              - 'ROLE_PORTAL_GRUPPE_SERVICEPARTNER_BASIC'
              - 'ROLE_PORTAL_GRUPPE_ABNAHMEPARTNER'
              - 'ROLE_PORTAL_INFORMATION_MOUNTING_AUFTRAGSZENTRUM_DE'

        - { path: ^/apis, roles: [] } # Alles andere verbieten


# ABUS Technische Daten
          #  abus_common_abukonfis-technical-data_sso             GET        ANY      ANY    /abukonfis-technical-data
        -
          path: ^/technicaldata/eot_cranes_abukonfis$
          methods: [GET]
          roles:
            - 'ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_ALL'
            - 'ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_EXT_DB_ALL'
            - 'ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_EXT_SK_LPK_KZUG_HB'
            - 'ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_EXT_DB_LK_SK_LPK_HB'

        -
          path: ^/technicaldata/jib_cranes_abukonfis$
          methods: [GET]
          roles:
            - 'ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_ALL'
            - 'ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_EXT_DB_ALL'
            - 'ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_EXT_SK_LPK_KZUG_HB'
            - 'ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_EXT_DB_LK_SK_LPK_HB'

        -
          path: ^/technicaldata/mobile_gantry_cranes_abukonfis$
          methods: [GET]
          roles:
            - 'ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_ALL'
            - 'ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_EXT_DB_ALL'
            - 'ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_EXT_SK_LPK_KZUG_HB'
            - 'ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_EXT_DB_LK_SK_LPK_HB'

        -
          path: ^/technicaldata/wire_rope_hoists_abukonfis$
          methods: [GET]
          roles:
            - 'ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_ALL'
            - 'ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_EXT_DB_ALL'
            - 'ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_TEST_EKZ'

        -
          path: ^/technicaldata/chain_hoists_with_trolley_abukonfis$
          methods: [GET]
          roles:
            - 'ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_ALL'
            - 'ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_EXT_DB_ALL'
            - 'ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_EXT_SK_LPK_KZUG_HB'
            - 'ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_TEST_EKZFW'

        -
          path: ^/technicaldata/stationary_chain_hoists_abukonfis$
          methods: [GET]
          roles:
            - 'ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_ALL'
            - 'ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_EXT_DB_ALL'
            - 'ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_EXT_SK_LPK_KZUG_HB'
            - 'ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_TEST_SZ'

        -
          path: ^/technicaldata/crane_drives_abukonfis$
          methods: [GET]
          roles:
            - 'ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_TEST_FW'

        -
          path: ^/technicaldata/hb-systems_abukonfis$
          methods: [GET]
          roles:
            - 'ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_TEST_HB'

      # abus_tdata_product                                GET        ANY      ANY    /technicaldata/{product}
        -
          path: ^/technicaldata/eot_cranes$
          methods: [GET]
          roles:
              - 'ROLE_PORTAL_TECHNICAL_DATA_ALL'
              - 'ROLE_PORTAL_TECHNICAL_DATA_LAUFKRAN'
              - 'ROLE_PORTAL_TECHNICAL_DATA_KIT'
              - 'ROLE_PORTAL_TECHNICAL_DATA_EXTERNAL_PLANNING'
              - 'ROLE_PORTAL_TECHNICAL_DATA_EXTERNAL_MOUNTING_PARTNER'

        # abus_tdata_product                                GET        ANY      ANY    /technicaldata/{product}
        -
          path: ^/technicaldata/(jib_cranes|mobile_gantry_cranes)$
          methods: [GET]
          roles:
              - 'ROLE_PORTAL_TECHNICAL_DATA_ALL'
              - 'ROLE_PORTAL_TECHNICAL_DATA_LAUFKRAN'
              - 'ROLE_PORTAL_TECHNICAL_DATA_KIT'
              - 'ROLE_PORTAL_TECHNICAL_DATA_RESELLER'
              - 'ROLE_PORTAL_TECHNICAL_DATA_EXTERNAL_PLANNING'
              - 'ROLE_PORTAL_TECHNICAL_DATA_EXTERNAL_MOUNTING_PARTNER'
        -
          path: ^/technicaldata/hb-systems$
          methods: [GET]
          roles:
            - 'ROLE_PORTAL_TECHNICAL_DATA_ALL'
            - 'ROLE_PORTAL_TECHNICAL_DATA_LAUFKRAN'
            - 'ROLE_PORTAL_TECHNICAL_DATA_KIT'
            - 'ROLE_PORTAL_TECHNICAL_DATA_RESELLER'
            - 'ROLE_PORTAL_TECHNICAL_DATA_EXTERNAL_PLANNING'
            - 'ROLE_PORTAL_TECHNICAL_DATA_EXTERNAL_MOUNTING_PARTNER'
            - 'ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_ALL'
            - 'ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_EXT_DB_ALL'
            - 'ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_EXT_SK_LPK_KZUG_HB'
            - 'ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_EXT_DB_LK_SK_LPK_HB'

        # abus_tdata_product                                GET        ANY      ANY    /technicaldata/{product}
        -
          path: ^/technicaldata/wire_rope_hoists$
          methods: [GET]
          roles:
              - 'ROLE_PORTAL_TECHNICAL_DATA_ALL'
              - 'ROLE_PORTAL_TECHNICAL_DATA_LAUFKRAN'
              - 'ROLE_PORTAL_TECHNICAL_DATA_KIT'
              - 'ROLE_PORTAL_TECHNICAL_DATA_EXTERNAL_MOUNTING_PARTNER'

      # abus_tdata_product                                GET        ANY      ANY    /technicaldata/{product}
        -
          path: ^/technicaldata/crane_drives$
          methods: [GET]
          roles:
            - 'ROLE_PORTAL_TECHNICAL_DATA_ALL'
            - 'ROLE_PORTAL_TECHNICAL_DATA_LAUFKRAN'
            - 'ROLE_PORTAL_TECHNICAL_DATA_KIT'
            - 'ROLE_PORTAL_TECHNICAL_DATA_EXTERNAL_MOUNTING_PARTNER'
            - 'ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_ALL'
            - 'ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_EXT_DB_ALL'

        # abus_tdata_product                                GET        ANY      ANY    /technicaldata/{product}
        -
          path: ^/technicaldata/(chain_hoists_with_trolley|stationary_chain_hoists)$
          methods: [GET]
          roles:
              - 'ROLE_PORTAL_TECHNICAL_DATA_ALL'
              - 'ROLE_PORTAL_TECHNICAL_DATA_LAUFKRAN'
              - 'ROLE_PORTAL_TECHNICAL_DATA_KIT'
              - 'ROLE_PORTAL_TECHNICAL_DATA_RESELLER'
              - 'ROLE_PORTAL_TECHNICAL_DATA_EXTERNAL_MOUNTING_PARTNER'

        # abus_tdata_search                                 POST       ANY      ANY    /technicaldata/search
        # abus_tdata_getSelection                           POST       ANY      ANY    /technicaldata/getSelection
        # abus_tdata_download                               GET        ANY      ANY    /technicaldata/download/{file}
        -
          path: ^/technicaldata/(search|getSelection|download)
          methods: [GET, POST]
          roles:
              - 'ROLE_PORTAL_TECHNICAL_DATA_ALL'
              - 'ROLE_PORTAL_TECHNICAL_DATA_LAUFKRAN'
              - 'ROLE_PORTAL_TECHNICAL_DATA_KIT'
              - 'ROLE_PORTAL_TECHNICAL_DATA_RESELLER'
              - 'ROLE_PORTAL_TECHNICAL_DATA_EXTERNAL_PLANNING'
              - 'ROLE_PORTAL_TECHNICAL_DATA_EXTERNAL_MOUNTING_PARTNER'
              - 'ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_ALL'
              - 'ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_EXT_DB_ALL'

        - { path: ^/technicaldata, methods: [GET], roles: [] } # Alles andere verbieten


# ABUS Ersatzteile

        # abus_frame_spareparts                             GET        ANY      ANY    /spareparts/{area}/{catalog}
        -
          path: ^/spareparts
          methods: [GET]
          roles:
              - 'ROLE_PORTAL_CATALOG_SPAREPARTS'
              - 'ROLE_PORTAL_CATALOG_WINETIS'

# ABUS Media

        #  abus_frame_media                                  GET        ANY      ANY    /media
        -
          path: ^/media$
          methods: [GET]
          roles:
              - 'ROLE_PORTAL_DAM_SUPER_ADMIN'
              - 'ROLE_PORTAL_DAM_ADMINISTRATOR'
              - 'ROLE_PORTAL_DAM_BENUTZER_ABUS'
              - 'ROLE_PORTAL_DAM_BENUTZER_MITARBEITER_MIT_UPLOAD'
              - 'ROLE_PORTAL_DAM_BENUTZER_PARTNER'
              - 'ROLE_PORTAL_DAM_BENUTZER_PARTNER_MIT_UPLOAD'
              - 'ROLE_PORTAL_DAM_BENUTZER_EXTERN'

# ABUKonfis

        #  abus_common_abukonfis_sso                         GET        ANY      ANY    /abukonfis
        - { path: ^/abukonfis$, methods: [GET], roles: ['ROLE_PORTAL_ABUKONFIS_ONLINE'] }

        #  abus_common_abukonfis-work_sso                    GET        ANY      ANY    /abukonfis-work
        - { path: ^/abukonfis-work$, methods: [GET], roles: ['ROLE_PORTAL_ABUKONFIS_ONLINE_WORK'] }

        #  abus_common_abukonfis-intern_sso                  GET        ANY      ANY    /abukonfis-intern
        - { path: ^/abukonfis-intern$, methods: [GET], roles: ['ROLE_PORTAL_ABUKONFIS_INTERN'] }

        #  abus_common_abukonfis-intern-work_sso             GET        ANY      ANY    /abukonfis-intern-work
        - { path: ^/abukonfis-intern-work$, methods: [GET], roles: ['ROLE_PORTAL_ABUKONFIS_INTERN_WORK'] }

        #  abus_common_abukonfis-test_sso             GET        ANY      ANY    /abukonfis-test
        - { path: ^/abukonfis-test$, methods: [GET], roles: ['ROLE_PORTAL_ABUKONFIS_TEST'] }

          # abus_common_abukonfis_authentication              GET        ANY      ANY    /abukauth
        - { path: ^/abukauth$, methods: [GET], roles: ['PUBLIC_ACCESS'] }

# Bewerbungen

        #  abus_application_homepage                         GET        ANY      ANY    /application
        #  abus_application_transfer                         POST       ANY      ANY    /application/transfer
        #  abus_application_view                             GET        ANY      ANY    /application/view/{id}
        #  abus_application_logging                          GET|POST   ANY      ANY    /application/logging
        #  abus_application_delete                           GET        ANY      ANY    /application/delete/{id}
        #  abus_application_attachment                       GET        ANY      ANY    /application/attachment/{id}/{file}
        #  abus_application_print                            GET        ANY      ANY    /application/print/{id}
        #  abus_application_pdf                              GET        ANY      ANY    /application/pdf/{id}
        - { path: ^/application, methods: [GET, POST], roles: ['ROLE_PORTAL_BEWERBUNGEN']}

# Chat
      #  abus_frame_chat                                     GET        ANY      ANY    /chat
        - { path: ^/chat$, methods: [GET], roles: ['ROLE_PORTAL_CHAT']}

# Gitlab
        #  abus_frame_gitlab                                 GET        ANY      ANY    /gitlab
        - { path: ^/gitlab$, methods: [GET], roles: ['ROLE_WEB_GITLAB']}

# Printshop
        - { path: ^/printshop$, methods: [GET], roles: ['ROLE_PORTAL_PRINT_SHOP']}


# Benutzerverwaltung

        #  app_usermanagement_ajax_getcompanies              GET        ANY      ANY    /management/ajax/getCompanies
        #  app_usermanagement_ajax_getcompany                GET        ANY      ANY    /management/ajax/getCompany/{name}
        #  app_usermanagement_ajax_getuser                   GET        ANY      ANY    /management/ajax/getUser/{email}
        #  app_usermanagement_ajax_getpermissionpackages     GET        ANY      ANY    /management/ajax/getPermissionPackages
        #  app_usermanagement_ajax_getadgroups               GET        ANY      ANY    /management/ajax/getADGroups
        #  app_usermanagement_ajax_getmodulenames            GET        ANY      ANY    /management/ajax/getModuleNames/{language}
        #  app_usermanagement_ajax_storepermissionpackage    POST       ANY      ANY    /management/ajax/storePermissionPackage
        #  app_usermanagement_ajax_updatepermissionpackage   PATCH      ANY      ANY    /management/ajax/updatePermissionPackage
        #  abus_user_management_companies                    GET        ANY      ANY    /management/companies
        #  abus_user_management_permission_packages          GET        ANY      ANY    /management/permission_packages
        #  abus_user_management_homepage                     GET        ANY      ANY    /management/users
        #  abus_management_problems_found                    GET        ANY      ANY    /management/problems/found
        #- { path: ^/management, methods: [GET, POST, PATCH], roles: ['ROLE_PORTAL_USERMANAGEMENT_ADMIN']}


# Buchungstool - TEMPORÄR FÜR DEV AUSKOMMENTIERT
        #- { path: ^/bookingsTool, methods: [GET, POST, PATCH], roles: ['ROLE_PORTAL_BOOKING_EXAMINATION', 'ROLE_PORTAL_BOOKING_CRANE_FORKLIFT_TRAINING', 'ROLE_PORTAL_BOOKING_FIRST_AID_TRAINING', 'ROLE_PORTAL_BOOKING_KRANHAUS', 'ROLE_PORTAL_BOOKING_ADVICE_BAV', 'ROLE_PORTAL_BOOKING_CAT_TRAINING', 'ROLE_PORTAL_BOOKING_ASANA']}

# Zugangsverwaltung

        #  abus_account_grant_legacy                         GET        ANY      ANY    /account/grant/{code}
        #  abus_account_check_legacy                         GET        ANY      ANY    /account/check/{code}
        #  abus_account_done_legacy                          GET        ANY      ANY    /account/done/{code}
        #  abus_account_done_pa_legacy                       GET        ANY      ANY    /account/done/pa/{code}
        #  abus_account_done_eo_legacy                       GET        ANY      ANY    /account/done/eo/{code}
        #  abus_account_done_vo_legacy                       GET        ANY      ANY    /account/done/vo/{code}
        #  abus_account_done_ov_legacy                       GET        ANY      ANY    /account/done/ov/{code}
        - { path: ^/account/(grant|check|done), methods: [GET], roles: ['PUBLIC_ACCESS']}
        - { path: ^/account, methods: [GET], roles: ['ROLE_WEB_ACCESSMANAGEMENT', 'ROLE_WEB_ACCESSMANAGEMENT_ADMIN', 'ROLE_WEB_ACCESSMANAGEMENT_SUPERADMIN']}

# Reporting

        #  abus_frame_reporting_legacy                       GET        ANY      ANY    /reportinglegacy
        - { path: ^/reportinglegacy, methods: [GET], roles: ['ROLE_PORTAL_REPORTING_USERS']}

        #  abus_matomo                                       GET        ANY      ANY    /matomo
        - { path: ^/matomo$, methods: [GET], roles: ['ROLE_PORTAL_MATOMO']}

# DevOps

      #  abus_frame_logs                                   GET        ANY      ANY    /logs
        - { path: ^/logs$, methods: [GET], roles: ['ROLE_PORTAL_GRAYLOG_ADMIN', 'ROLE_PORTAL_GRAYLOG_READER']}

      #  abus_frame_grafana                                  GET        ANY      ANY    /portainer
        - { path: ^/grafana$, methods: [GET], roles: ['ROLE_PORTAL_GRAFANA_ADMIN', 'ROLE_PORTAL_GRAFANA_VIEWER']}

      #  abus_frame_sql                              GET        ANY      ANY    /sql
        - { path: ^/sql$, methods: [GET], roles: ['ROLE_PORTAL_DEVOPS']}

      #  abus_frame_traefik                              GET        ANY      ANY    /traefik
        - { path: ^/traefik$, methods: [GET], roles: ['ROLE_PORTAL_DEVOPS']}

      #  abus_frame_kibana                              GET        ANY      ANY    /kibana
        - { path: ^/kibana$, methods: [GET], roles: ['ROLE_PORTAL_DEVOPS']}

      # Service
        #  abus_frame_service_legacy                         GET        ANY      ANY    /service/{url}
        - { path: ^/service, methods: [GET], roles: ['ROLE_WEB_PORTAL_SERVICES']}


# Intranet
        #  abus_frame_intranet                               GET        ANY      ANY    /intranet
        - { path: ^/intranet$, methods: [GET], roles: ['ROLE_ABUS Intern']}


# Internet
        #  abus_frame_website                                GET        ANY      ANY    /website/{lang}
        - { path: ^/website, methods: [GET], roles: ['IS_AUTHENTICATED_FULLY']}

# Nextcloud
        #  abus_frame_nextcloud                              GET        ANY      ANY    /nextcloud
        - { path: ^/nextcloud, methods: [GET], roles: ['IS_AUTHENTICATED_FULLY']}

# OVISS
        #  abus_frame_oviss                              GET        ANY      ANY    /oviss
        - { path: ^/oviss, methods: [GET], roles: ['ROLE_PORTAL_OVISS']}


# Weitere Dienste
        #  abus_frame_furtherServices_legacy                 GET        ANY      ANY    /furtherservices
        -
          path: ^/furtherservices
          methods: [GET]
          roles:
              - 'ROLE_ABUS Intern'
              - 'ROLE_PORTAL_GRUPPE_VERTRIEBSPARTNER_INLAND'
              - 'ROLE_PORTAL_GRUPPE_EXPORTPARTNER'
              - 'ROLE_PORTAL_GRUPPE_TOECHTER'

# ABUTools

        #  app_abutools_response_usergroups                  POST       ANY      ANY    /abutools/usergroups
        #  app_abutools_response_usergroups_1                POST       ANY      ANY    /abutools/usergroups.{_format}
        #  app_abutools_response_checkversion                POST       ANY      ANY    /abutools/checkversion
        #  app_abutools_response_checkversion_1              POST       ANY      ANY    /abutools/checkversion.{_format}
        #  app_abutools_response_getauftragsdaten            POST       ANY      ANY    /abutools/auftragsdaten
        #  app_abutools_response_getauftragsdaten_1          POST       ANY      ANY    /abutools/auftragsdaten.{_format}
        - { path: ^/abutools, methods: [POST], roles: ['PUBLIC_ACCESS'] }


# UserMode

        #  usermode_search                                   GET        ANY      ANY    /api/userMode/search
        - { path: ^/api/userMode/search, methods: [GET], roles: ['ROLE_PORTAL_ADMIN_USERMODE'] }


# Online Users
        #  abus_frame_users_online                           GET        ANY      ANY    /usersOnline
        - { path: ^/usersOnline$, methods: [GET], roles: ['ROLE_PORTAL_ADMIN_ACTTIVEUSERS'] }


# Profile / Settings

        #  abus_frame_profile                                GET        ANY      ANY    /profile
        #  abus_frame_my_profile                             GET        ANY      ANY    /myProfile
        #  abus_frame_my_settings                            GET        ANY      ANY    /mySettings
        #  abus_frame_my_settings_save                       POST       ANY      ANY    /mySettings/save
        - { path: ^/(profile$|myProfile$|mySettings), methods: [GET, POST], roles: ['IS_AUTHENTICATED_FULLY'] }


# Register / Login / Passwort zurücksetzen

        #  login                                             ANY        ANY      ANY    /login
        #  legal                                             ANY        ANY      ANY    /legal
        #  abus_api_forgot_password                          POST       ANY      ANY    /forgotpassword
        #  abus_api_reset_password                           GET        ANY      ANY    /resetpassword/{hash}
        #  abus_reset_password_post                          POST       ANY      ANY    /resetpassword/{hash}
        - { path: ^/(login|legal|forgotpassword|resetpassword), roles: ['PUBLIC_ACCESS'] }


        #  abus_api_change_password                          GET        ANY      ANY    /changepassword
        #  abus_api_change_password_post                     POST       ANY      ANY    /changepassword
        #  abus_frame_legal_notice                           GET        ANY      ANY    /legalnotice
        #  logout                                            ANY        ANY      ANY    /logout
        - { path: ^/(changepassword|legalnotice|reportProblem|logout)$, methods: [GET, POST], roles: ['IS_AUTHENTICATED_FULLY'] }

        #  abus_frame_report_problem                         GET        ANY      ANY    /reportProblem
        #  abus_frame_report_problem_send                    POST       ANY      ANY    /reportProblem/send
        #  abus_frame_report_problem_image_upload            POST       ANY      ANY    /reportProblem/uploadImage
        #  abus_frame_report_problem_image                   GET        ANY      ANY    /reportProblem/image/{image}
        - { path: ^/reportProblem, methods: [GET, POST], roles: ['IS_AUTHENTICATED_FULLY'] }

        #  abus_register_legacy                              GET        ANY      ANY    /register
        #  abus_register_grantpermission_legacy              GET        ANY      ANY    /register/grantpermission/{code}
        - { path: ^/register, methods: [GET], roles: ['PUBLIC_ACCESS'] }

        #  abus_register_tdata_legacy                        GET        ANY      ANY    /registration
        - { path: ^/registration, methods: [GET], roles: ['PUBLIC_ACCESS'] }

      #  abus_portal_createLDAPuserTable
        - { path: ^/createLDAPuserTable, methods: [ GET ], roles: [ 'IS_AUTHENTICATED_FULLY' ] }

# NEWS
        #  abus_frame_archive                                GET        ANY      ANY    /news/archive
        #  abus_frame_news_dashboard                         GET        ANY      ANY    /news/dashboard
        #  abus_frame_news_dashboard_archive                 GET        ANY      ANY    /news/dashboard/archive
        #  abus_frame_news_read                              GET        ANY      ANY    /news/read
        - { path: ^/news/archive$, methods: [GET], roles: ['IS_AUTHENTICATED_FULLY'] }
        - { path: ^/news/dashboard$, methods: [GET], roles: ['IS_AUTHENTICATED_FULLY'] }
        - { path: ^/news/dashboard/archive$, methods: [GET], roles: ['IS_AUTHENTICATED_FULLY'] }
        - { path: ^/news/read, methods: [GET], roles: ['IS_AUTHENTICATED_FULLY'] }
        - { path: ^/news/imageobjects, methods: [GET], roles: ['IS_AUTHENTICATED_FULLY'] }
        - { path: ^/news/attachments, methods: [GET], roles: ['IS_AUTHENTICATED_FULLY'] }

        #  abus_frame_news                                   GET        ANY      ANY    /news
        - { path: ^/news, methods: [GET, POST, PATCH, DELETE], roles: ['ROLE_PORTAL_NEWS_ADMIN'] }

# Documentation
        - { path: ^/documentation/pdf$, roles: ['IS_AUTHENTICATED_FULLY'] }
        - { path: ^/documentation/online$, roles: ['IS_AUTHENTICATED_FULLY'] }
        - { path: ^/documentation/service/auth, roles: ['PUBLIC_ACCESS'] }
        - { path: ^/documentation/service, roles: ['ROLE_ABUS Intern', 'ROLE_PORTAL_GRUPPE_MONTAGEPARTNER', 'ROLE_PORTAL_GRUPPE_SERVICEPARTNER', 'ROLE_PORTAL_GRUPPE_SERVICEPARTNER_MASTER', 'ROLE_PORTAL_GRUPPE_ABNAHMEPARTNER', 'ROLE_PORTAL_GRUPPE_EXPORTPARTNER', 'ROLE_PORTAL_GRUPPE_TOECHTER'] }

# Assets
        - { path: ^/dist/protected/, roles: ['IS_AUTHENTICATED_FULLY'] }
        - { path: ^/dist, roles: ['PUBLIC_ACCESS'] }


# Corporate Design
        #  abus_frame_corporateDesign                        GET        ANY      ANY    /corporateDesign
        - { path: ^/corporateDesign$, methods: [GET], roles: ['ROLE_PORTAL_CORPORATE_DESIGN'] }

# Alert
        #   alert                                             GET        ANY      ANY    /alert/{message}
        - { path: ^/alert, methods: [GET], roles: ['ROLE_PORTAL_ADMIN'] }

# TaskRunner
        # abus_task_runner_create                           POST       ANY      ANY    /taskrunner/create                                                GET        ANY      ANY    /alert/{message}
        - { path: ^/taskrunner/create$, methods: [POST], roles: ['ROLE_PORTAL_ADMIN', 'ROLE_PORTAL_BOX_ADMIN', 'ROLE_PORTAL_NEXTCLOUD_ADMIN'] }

# CookieHinweis
        # abus_accept_cookie                           POST       ANY      ANY    /acceptCookie                                                GET        ANY      ANY    /alert/{message}
        - { path: ^/acceptCookie$, methods: [POST], roles: ['PUBLIC_ACCESS'] }

# ABUETK öffentlich
      # abuetk_public                           GET      ANY      ANY    /sparepartspublic                                                GET        ANY      ANY    /alert/{message}
        - { path: ^/sparepartspublic$, methods: [GET], roles: ['PUBLIC_ACCESS'] }

      # Error Pages                                          GET        ANY      ANY    /_error
        - { path: ^/_error, methods: [GET], roles: ['ROLE_PORTAL_ADMIN'] }

# Allgemein
        #  abus_frame_homepage                               GET        ANY      ANY    /
        - { path: ^/$, methods: [GET], roles: ['ROLE_PORTAL_2018'] }
        - { path: ^/, roles: [] } # Alles andere verbieten
