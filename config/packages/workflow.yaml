framework:
    workflows:
        information:
            type: 'state_machine'
            marking_store:
                type: 'method'
                property: 'workflow'
            supports:
                - App\Entity\APIS\Information
            initial_marking: draft
            places:
                - draft
                - wait_for_translation
                - wait_for_publishing
                - published
                - released
            transitions:
                request_translation:
                    from: draft
                    to: wait_for_translation
                translate:
                    from: wait_for_translation
                    to: wait_for_publishing
                request_publishing:
                    from: draft
                    to: wait_for_publishing
                translator_rejection:
                    from: wait_for_translation
                    to: draft
                publisher_rejection:
                    from: wait_for_publishing
                    to: draft
                publish:
                    from: wait_for_publishing
                    to: published
                unpublish:
                    from: published
                    to: wait_for_publishing
                release:
                    from: published
                    to: released
                unrelease:
                    from: released
                    to: wait_for_publishing
        account_delete:
            type: 'workflow'
            audit_trail:
                enabled: true
            marking_store:
                type: 'method'
                property: 'workflow'
            supports:
                - App\Model\AccessManagement\Account
            initial_marking: draft
            places:
                - draft
                - request
                - rejected
                - deleted
                - validate
                - MA
                - doneMA
                - finalMA
                - IT
                - doneIT
                - finalIT
                - VO
                - doneVO
                - finalVO
                - OV
                - doneOV
                - finalOV
                - PA
                - donePA
                - finalPA
                - EO
                - doneEO
                - finalEO
            transitions:
                request:
                    from: draft
                    to: request
                rejected:
                    from: request
                    to: rejected
                architect_delete_from_AD:
                    from: request
                    to: doneIT
                architect_deleted:
                    from: doneIT
                    to: [ doneMA, doneEO, donePA, doneVO, doneOV, doneIT ]
                validate:
                    from: request
                    to: [ MA, EO, PA, VO, IT, OV ]
                decisionOV:
                    from: OV
                    to: doneOV
                decisionMA:
                    from: MA
                    to: doneMA
                decisionEO:
                    from: EO
                    to: doneEO
                decisionPA:
                    from: PA
                    to: donePA
                decisionVO:
                    from: VO
                    to: doneVO
                decisionIT:
                    from: IT
                    to: doneIT
                final:
                    from: [ doneMA, doneEO, donePA, doneVO, doneOV, doneIT ]
                    to: [ finalMA, finalEO, finalPA, finalVO, finalOV, finalIT ]
        account_create:
            type: 'workflow'
            audit_trail:
                enabled: true
            marking_store:
                type: 'method'
                property: 'workflow'
            supports:
                - App\Model\AccessManagement\Account
            initial_marking: draft
            places:
                - draft
                - request
                - rejected
                - doneMA
                - finalMA
                - doneIT
                - finalIT
                - doneEO
                - finalEO
            transitions:
                request:
                    from: draft
                    to: request
                rejected:
                    from: request
                    to: rejected
                decisionMA:
                    from: request
                    to: doneMA
                decisionEO:
                    from: doneMA
                    to: doneEO
                decisionIT:
                    from: doneEO
                    to: doneIT
                final:
                    from: doneIT
                    to: [finalMA, finalEO, finalIT]
        account_vpn_create:
            type: 'workflow'
            audit_trail:
                enabled: true
            marking_store:
                type: 'method'
                property: 'workflow'
            supports:
                - App\Model\AccessManagement\Account
            initial_marking: draft
            places:
                - draft
                - request
                - rejected
                - doneMA
                - finalMA
                - doneIT
                - finalIT
            transitions:
                request:
                    from: draft
                    to: request
                rejected:
                    from: request
                    to: rejected
                decisionMA:
                    from: request
                    to: doneMA
                decisionIT:
                    from: doneMA
                    to: doneIT
                final:
                    from: doneIT
                    to: [ finalMA, finalIT ]
        account_vpn_reassign:
            type: 'workflow'
            audit_trail:
                enabled: true
            marking_store:
                type: 'method'
                property: 'workflow'
            supports:
                - App\Model\AccessManagement\Account
            initial_marking: draft
            places:
                - draft
                - request
                - rejected
                - doneMA
                - finalMA
                - doneIT
                - finalIT
            transitions:
                request:
                    from: draft
                    to: request
                decisionMA:
                    from: request
                    to: doneMA
                rejected:
                    from: request
                    to: rejected
                decisionIT:
                    from: doneMA
                    to: doneIT
                final:
                    from: doneIT
                    to: [ finalMA, finalIT ]
