# see https://symfony.com/doc/current/reference/configuration/framework.html
framework:
    secret: '%env(APP_SECRET)%'
    csrf_protection: true
    #http_method_override: true
    assets:
        json_manifest_path: '%kernel.project_dir%/public/dist/manifest.json'

        packages:
            vueform:
                json_manifest_path: '%kernel.project_dir%/public/vueform/manifest.json'

    trusted_proxies: '*********/8'
    trusted_headers: ['x-forwarded-for', 'x-forwarded-host', 'x-forwarded-proto', 'x-forwarded-port', 'x-forwarded-prefix']

    # Enables session support. Note that the session will ONLY be started if you read or write from it.
    # Remove or comment this section to explicitly disable session support.
    session:
        # http://symfony.com/doc/current/reference/configuration/framework.html#handler-id
        handler_id: null
        storage_factory_id: session.storage.factory.native
        save_path:   "%kernel.project_dir%/var/sessions/%kernel.environment%"
        name: ABUSPORTAL2018
        cookie_domain: '%env(COOKIE_DOMAIN)%'
        cookie_lifetime: 0
        cookie_path: /
        cookie_secure: true
        cookie_httponly: true
        gc_divisor: 100
        gc_probability: 1
        gc_maxlifetime: 16200

    router:
        utf8: true

    php_errors:
        log: true
