# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.
imports:
    - { resource: database.yaml }
    - { resource: ldap.yaml }
    - { resource: modules.yaml }
    - { resource: groups.yaml }
    - { resource: redis.yaml }
    - { resource: logger.yaml }
    - { resource: file.yaml }
    - { resource: usermanagement.yaml }
    - { resource: technicalData.yaml }
    - { resource: gitlab.yaml }
    - { resource: storage.yaml }
    - { resource: apis.yaml }
    - { resource: account.yaml }
    - { resource: asana.yaml }

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices/configuration.html#application-related-configuration
parameters:
    locale: en
    domain: '%env(DOMAIN)%'
    router.request_context.host: 'portal.%env(DOMAIN)%'
    router.request_context.scheme: 'https'
    app.secret.public: '%env(FORM_BACKEND_PUBLIC_KEY)%'
    app.keycloak.enabled: '%env(bool:IAM_ENABLED)%'

services:
    # default configuration for services in *this* file
    _defaults:
        # automatically injects dependencies in your services
        autowire: true

        # automatically registers your services as commands, event subscribers, etc.
        autoconfigure: true

        # this means you cannot fetch services directly from the container via $container->get()
        # if you need to do this, you can override this setting on individual services
        # TODO geht ggf. ab Verion 4 mit TypeHinting
        #public: '%env(SERVICES_PUBLIC)%'
        public: false

#    twig.extension.intl:
#        class: Twig_Extensions_Extension_Intl
#        tags:
#            - { name: twig.extension }

    # Repository
    App\Repository\:
        resource: '../src/Repository/*'

    # API
    App\Model\Api\:
        resource: '../src/Model/Api/*'

    # Box
    App\Model\Box\:
        resource: '../src/Model/Box/*'

    # ABUTools
    App\Model\ABUTools\:
        resource: '../src/Model/ABUTools/*'

    # TaskRunner
    App\Model\TaskRunner\:
        resource: '../src/Model/TaskRunner/*'

    # APIS
    App\Model\APIS\:
        resource: '../src/Model/APIS/*'

    # Management
    App\Model\Management\:
        resource: '../src/Model/Management/*'

    #    # UserManagement
    #    App\Model\Api\UserManagement\:
    #        resource: '../src/Model/Api/UserManagement/*'

    # Alert
    App\Model\Alert\:
        resource: '../src/Model/Alert/*'

    # Interfaces
    Symfony\Component\Security\Core\User\UserProviderInterface: '@App\Security\User\ABUSUserProvider'

    # Mailer
    App\Model\Api\Mailer\MailerSwift:
        arguments:
            $defaultMailFromAddress: '%env(DEFAULT_MAIL_FROM_ADDRESS)%'
            $defaultMailFromName: '%env(DEFAULT_MAIL_FROM_NAME)%'
            $env: '%env(APP_ENV)%'

    #    App\Model\Api\User\UserWriteRepositoryInterface: '@App\Model\Api\UserManagement\User\LDAP\UserWriteRepositoryLDAP'
    App\Model\Api\Logger\LoggerInterface: '@App\Model\Api\Logger\Logger'
    App\Model\Api\Connector\inMemoryConnectorInterface: '@App\Model\Api\Connector\Redis\RedisConnector'
    App\Model\Api\Module\ModuleGetterInterface: '@App\Model\Api\Module\ModuleGetterYML'
    App\Model\Api\Module\ModuleInterface: '@App\Model\Api\Module\Module'
    App\Model\Api\Mailer\MailerInterface: '@App\Model\Api\Mailer\MailerSwift'
    #    App\Model\Api\User\UserWritePermissionInterface: '@App\Model\Api\User\UserWritePermission\UserWritePermission'
    App\Model\Api\Module\ModuleRepositoryInterface: '@App\Model\Api\Module\ModuleRepository'
    #    App\Model\Api\User\PortalUser\PortalUserInterface: '@App\Model\Api\User\PortalUser'
    App\Model\Api\UserManagement\User\UserWriteRepositoryInterface: '@App\Model\Api\UserManagement\User\LDAP\UserWriteRepositoryLDAP'
    #    App\Model\Api\Company\CompaniesRepositoryInterface: '@App\Model\Api\Company\LDAP\CompaniesRepositoryLDAP'
    #    App\Model\Api\User\UserMode\UserModeInterface: '@App\Model\Api\User\UserMode\UserMode'
    App\Model\ABUTools\Authorization\AuthorizationInterface: '@App\Model\ABUTools\Authorization\AuthorizationLDAP'
    App\Model\ABUTools\Data\DataGetterInterface: '@App\Model\ABUTools\Data\Version\Getter\VersionGetterForTesting'
    App\Model\APIS\Repository\RepositoryInterface: '@App\Model\APIS\Repository\RepositoryDatabase'
    #    App\Model\Box\Nextcloud\Connector\WebdavConnectorInferface: '@App\Model\Box\Nextcloud\Connector\Webdav\WebdavConnectorGuzzle'
    #    App\Model\Api\CompanyCategory\LDAP\CompanyCategoryInterface: '@App\Model\Api\CompanyCategory\CompanyCategory'

    # Events
    App\Event\LoggerEvent:

    # Entities
    App\Entity\Api\ForgotPassword:
    App\Entity\TaskRunner\Task:

    # Listener
    App\EventListener\ExceptionListener:
        tags:
            - { name: kernel.event_listener, event: kernel.exception }

    App\EventListener\LocaleListener:
        arguments:
            $defaultLocale: '%kernel.default_locale%'
        tags:
            - { name: kernel.event_subscriber }

    App\EventListener\AuthenticationListener:
        tags:
            - { name: kernel.event_listener, event: security.interactive_login, method: onInteractiveLogin }

    App\EventListener\LogoutListener:

    # Workflow EventSubscriber
    App\EventSubscriber\DeleteAccountSubscriber:
        arguments:
            $accountNotification: '@App\Model\AccessManagement\Notification\AccountNotificationMailer'
            $accountParameters: '%account_parameters%'
            $chatUserDelete: '@App\Model\AccessManagement\ChatUserDelete'

    App\EventSubscriber\CreateAccountSubscriber:
        arguments:
            $accountNotification: '@App\Model\AccessManagement\Notification\AccountNotificationMailer'
            $accountParameters: '%account_parameters%'

    App\EventSubscriber\VpnCreateSubscriber:
        arguments:
            $accountNotification: '@App\Model\AccessManagement\Notification\AccountNotificationMailer'
            $accountParameters: '%account_parameters%'

    App\EventSubscriber\VpnReassignSubscriber:
        arguments:
            $accountNotification: '@App\Model\AccessManagement\Notification\AccountNotificationMailer'
            $accountParameters: '%account_parameters%'


    App\Model\Form\Encryptor\FormEncryptor:

    # Twig
    App\Twig\TwigExtension:
        arguments:
            $encryptor: '@App\Model\Form\Encryptor\FormEncryptor'
            $domain: '%domain%'
        tags:
            - { name: twig.extension }

    App\Service\ViteAssetHelper:
        arguments:
            $projectDir: '%kernel.project_dir%'
            $manifestPath: '/public/build/manifest.json'
            $publicPath: '/build/'

    App\Twig\ViteExtension:
        tags:
            - { name: twig.extension }

    # Doctrine
    Doctrine\ORM\EntityManager: '@doctrine.orm.entity_manager'
    Doctrine\ORM\EntityManagerInterface: '@doctrine.orm.entity_manager'

    # LDAP

    # Man braucht einen LDAPReadAdapter und einen LDAPWriteAdapter weil die sich sonst in die Quere kommen mit dem Read und Write User
    LDAPReadAdapter:
        class: Symfony\Component\Ldap\Adapter\ExtLdap\Adapter
        arguments:
            $config:
                host: ************
                port: 636
                debug: false
                encryption: ssl
                version: 3

    LDAPWriteAdapter:
        class: Symfony\Component\Ldap\Adapter\ExtLdap\Adapter
        arguments:
            $config:
                host: ************
                port: 636
                debug: false
                encryption: ssl
                version: 3

    LDAPRead:
        class: Symfony\Component\Ldap\Ldap
        arguments:
            $adapter: '@LDAPReadAdapter'
        public: true

    LDAPWrite:
        class: Symfony\Component\Ldap\Ldap
        arguments:
            $adapter: '@LDAPWriteAdapter'
        public: true

    LDAPReadInterface:
        alias: LDAPRead
        public: true

    LDAPWriteInterface:
        alias: LDAPWrite
        public: true

    #    App\Model\Api\Connector\LDAP\ConnectorLDAP:
    #        arguments:
    #            $ldap: '@LDAPWriteInterface'
    #            $adapter: '@LDAPWriteAdapter'
    #        public: true

    ConnectorLDAPWrite:
        class: App\Model\Api\Connector\LDAP\ConnectorLDAP
        arguments:
            $ldap: '@LDAPWriteInterface'
            $adapter: '@LDAPWriteAdapter'
        public: true

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    #App\:
    #    resource: '../src/'
    #    exclude:
    #        - '../src/DependencyInjection/'
    #        - '../src/Entity/'
    #        - '../src/Kernel.php'

    App\Model\Api\UserManagement\Identifier\IdentifierGetter:
        arguments:
            $redisConnectionDsn: '%redis_connection_dsn%'

    App\Model\Api\UserManagement\Company\LDAP\CompanyReadRepositoryLDAP:
        arguments:
            $connectorLDAP: '@ConnectorLDAPWrite'
            $connectionAttributesLDAP: '@App\Model\Api\Connector\LDAP\ConnectionAttributesLDAP'
            $dbPortal: '@pdoportal'

    App\Model\Api\UserManagement\Company\LDAP\CompanyWriteRepositoryLDAP:
        arguments:
            $connectorLDAP: '@ConnectorLDAPWrite'
            $connectionAttributesLDAP: '@App\Model\Api\Connector\LDAP\ConnectionAttributesLDAP'
            $groupReadRepository: '@App\Model\Api\UserManagement\Group\LDAP\GroupReadRepositoryLDAP'
            $groupWriteRepository: '@App\Model\Api\UserManagement\Group\LDAP\GroupWriteRepositoryLDAP'

    App\Model\Api\UserManagement\Company\Redis\CompanyReadRepositoryRedis:
        arguments:
            $identifierGetter: '@App\Model\Api\UserManagement\Identifier\IdentifierGetter'
            $redisConnectionDsn: '%redis_connection_dsn%'

    App\Model\Api\UserManagement\Company\Redis\CompanyWriteRepositoryRedis:
        arguments:
            $redisConnectionDsn: '%redis_connection_dsn%'

    App\Model\Api\UserManagement\CompanyCategory\LDAP\CompanyCategoryReadRepositoryLDAP:
        arguments:
            $connectorLDAP: '@ConnectorLDAPWrite'
            $connectionAttributesLDAP: '@App\Model\Api\Connector\LDAP\ConnectionAttributesLDAP'

    App\Model\Api\UserManagement\CompanyCategory\Redis\CompanyCategoryReadRepositoryRedis:
        arguments:
            $redisConnectionDsn: '%redis_connection_dsn%'

    App\Model\Api\UserManagement\CompanyCategory\Redis\CompanyCategoryWriteRepositoryRedis:
        arguments:
            $redisConnectionDsn: '%redis_connection_dsn%'

    App\Model\Api\UserManagement\Group\LDAP\GroupReadRepositoryLDAP:
        arguments:
            $connectorLDAP: '@ConnectorLDAPWrite'
            $connectionAttributesLDAP: '@App\Model\Api\Connector\LDAP\ConnectionAttributesLDAP'
        public: true

    App\Model\Api\UserManagement\Group\Redis\GroupReadRepositoryRedis:
        arguments:
            $redisConnectionDsn: '%redis_connection_dsn%'

    App\Model\Api\UserManagement\Group\LDAP\GroupWriteRepositoryLDAP:
        arguments:
            $connectorLDAP: '@ConnectorLDAPWrite'
            $connectionAttributesLDAP: '@App\Model\Api\Connector\LDAP\ConnectionAttributesLDAP'

    App\Model\Api\UserManagement\Group\Redis\GroupWriteRepositoryRedis:
        arguments:
            $identifierGetter: '@App\Model\Api\UserManagement\Identifier\IdentifierGetter'
            $redisConnectionDsn: '%redis_connection_dsn%'

    App\Model\Api\UserManagement\User\LDAP\UserReadRepositoryLDAP:
        arguments:
            $connectorLDAP: '@ConnectorLDAPWrite'
            $connectionAttributesLDAP: '@App\Model\Api\Connector\LDAP\ConnectionAttributesLDAP'

    App\Model\Api\UserManagement\User\Redis\UserReadRepositoryRedis:
        arguments:
            $redisConnectionDsn: '%redis_connection_dsn%'

    App\Model\Api\UserManagement\User\LDAP\UserWriteRepositoryLDAP:
        arguments:
            $connectorLDAP: '@ConnectorLDAPWrite'
            $connectionAttributesLDAP: '@App\Model\Api\Connector\LDAP\ConnectionAttributesLDAP'
            $dbPortal: '@pdoportal'
            $groupWriteRepository: '@App\Model\Api\UserManagement\Group\LDAP\GroupWriteRepositoryLDAP'

    App\Model\Api\UserManagement\User\Redis\UserWriteRepositoryRedis:
        arguments:
            $redisConnectionDsn: '%redis_connection_dsn%'

    App\Model\Api\UserManagement\Facade\LDAP\UserManagementReadLDAP:
        arguments:
            $companyReadRepository: '@App\Model\Api\UserManagement\Company\LDAP\CompanyReadRepositoryLDAP'
            $companyCategoryReadRepository: '@App\Model\Api\UserManagement\CompanyCategory\LDAP\CompanyCategoryReadRepositoryLDAP'
            $userReadRepository: '@App\Model\Api\UserManagement\User\LDAP\UserReadRepositoryLDAP'
            $groupReadRepository: '@App\Model\Api\UserManagement\Group\LDAP\GroupReadRepositoryLDAP'
        public: true

    App\Model\Api\UserManagement\Facade\LDAP\UserManagementWriteLDAP:
        arguments:
            $companyWriteRepository: '@App\Model\Api\UserManagement\Company\LDAP\CompanyWriteRepositoryLDAP'
            $userWriteRepository: '@App\Model\Api\UserManagement\User\LDAP\UserWriteRepositoryLDAP'
            $groupWriteRepository: '@App\Model\Api\UserManagement\Group\LDAP\GroupWriteRepositoryLDAP'
        public: true

    App\Model\Api\UserManagement\Facade\Redis\UserManagementReadRedis:
        arguments:
            $companyReadRepository: '@App\Model\Api\UserManagement\Company\Redis\CompanyReadRepositoryRedis'
            $companyCategoryReadRepository: '@App\Model\Api\UserManagement\CompanyCategory\Redis\CompanyCategoryReadRepositoryRedis'
            $groupReadRepository: '@App\Model\Api\UserManagement\Group\Redis\GroupReadRepositoryRedis'
            $userReadRepository: '@App\Model\Api\UserManagement\User\Redis\UserReadRepositoryRedis'
        public: true

    App\Model\Api\UserManagement\Facade\Redis\UserManagementWriteRedis:
        arguments:
            $companyWriteRepository: '@App\Model\Api\UserManagement\Company\Redis\CompanyWriteRepositoryRedis'
            $userWriteRepository: '@App\Model\Api\UserManagement\User\Redis\UserWriteRepositoryRedis'
            $groupWriteRepository: '@App\Model\Api\UserManagement\Group\Redis\GroupWriteRepositoryRedis'
        public: true

    App\Model\Api\UserManagement\ObjectBuilder\CompanyBuilder:
        arguments:
            $userManagementRead: '@App\Model\Api\UserManagement\Facade\Redis\UserManagementReadRedis'

    App\Model\Api\UserManagement\ObjectBuilder\UserBuilder:
        arguments:
            $userManagementRead: '@App\Model\Api\UserManagement\Facade\Redis\UserManagementReadRedis'

    App\Model\Api\UserManagement\DatabaseConnector\Country:
        arguments:
            $dbPortal: '@pdoportal'

    App\Model\Api\UserManagement\DatabaseConnector\Werksvertretung:
        arguments:
            $dbPortal: '@pdoportal'

    App\Model\Api\UserManagement\PermissionChecker\PermissionChecker:
        arguments:
            $userManagementRead: '@App\Model\Api\UserManagement\Facade\LDAP\UserManagementReadLDAP'


    #    App\Model\Api\Group\LDAP\GroupLDAP:
    #        public: true
    #        arguments:
    #            $connectorLDAP: '@ConnectorLDAPWrite'
    #
    #    App\Model\Api\Group\LDAP\GroupRepositoryLDAP:
    #        arguments:
    #            $connectorLDAP: '@ConnectorLDAPWrite'
    #
    #    App\Model\Api\User\UserReadRepository\UserReadRepositoryLDAP:
    #        arguments:
    #            $ldap: '@LDAPReadInterface'
    #            $user: '@App\Model\Api\User\User\UserLDAP'
    #        public: true
    #
    #    App\Model\Api\User\UserWriteRepository\UserWriteRepositoryLDAP:
    #            arguments:
    #                $connectorLDAP: '@ConnectorLDAPWrite'
    #                $userReadRepository: '@App\Model\Api\User\UserReadRepository\UserReadRepositoryLDAP'

    #    App\Model\Api\Company\LDAP\CompaniesRepositoryLDAP:
    #        arguments:
    #            $ldap: '@LDAPReadInterface'
    #            $userReadRepository: '@App\Model\Api\User\UserReadRepository\UserReadRepositoryLDAP'
    #
    #    App\Model\Api\User\AttributeRepository\AttributeRepositoryLDAP:
    #        arguments:
    #            $ldap: '@LDAPReadInterface'
    #            $adapter: '@LDAPReadAdapter'
    #
    #    App\Model\Api\User\User\UserLDAP:
    #        arguments:
    #            $connectorLDAP: '@ConnectorLDAPWrite'
    #            $ldap: '@LDAPReadInterface'
    #            $group: '@App\Model\Api\Group\LDAP\GroupLDAP'

    #    # REDIS
    #    App\Model\Api\User\User\UserRedis:
    #        arguments:
    #            $group: '@App\Model\Api\Group\Redis\GroupRedis'
    #
    #
    #    App\Model\Api\User\UserReadRepository\UserReadRepositoryRedis:
    #        arguments:
    #            $user: '@App\Model\Api\User\User\UserRedis'
    #        public: true
    #
    #    App\Model\Api\User\UserMode\UserMode:
    #        arguments:
    #            $userReadRepository: '@App\Model\Api\User\UserReadRepository\UserReadRepositoryRedis'

    App\Model\Api\UserMode\Redis\UserModeRedis:
        arguments:
            $userManagementRead: '@App\Model\Api\UserManagement\Facade\Redis\UserManagementReadRedis'

    App\Model\APIS\Handler\Report\ReportHandler:
        arguments:
            $userManagementRead: '@App\Model\Api\UserManagement\Facade\Redis\UserManagementReadRedis'

    App\Model\APIS\Handler\ReceiverList\ReceiverListHandler:
        arguments:
            $userManagementRead: '@App\Model\Api\UserManagement\Facade\Redis\UserManagementReadRedis'
            $configurationGetter: '@App\Model\APIS\Configuration\ConfigurationGetterYML'

    App\Model\APIS\Handler\Mailer\MailerHandler:
        arguments:
            $userManagementRead: '@App\Model\Api\UserManagement\Facade\Redis\UserManagementReadRedis'
            $userReadRepository: '@App\Model\Api\UserManagement\User\Redis\UserReadRepositoryRedis'

    App\Entity\APIS\Information:
    App\Entity\APIS\Reference:
    App\Entity\APIS\Optin:
    App\Entity\APIS\File:
    App\Entity\APIS\Viewed:


    # Single Sign On
    App\Model\Api\SingleSignOn\Helper\SingleSignOnHelper:
        arguments:
            $userReadRepository: '@App\Model\Api\UserManagement\User\Redis\UserReadRepositoryRedis'
            $domain: '%domain%'

    #    App\Model\Api\Logger\LoggerMonolog: # Logger
    #        public: true

    Symfony\Component\HttpFoundation\Request:

    #    App\Model\Registration\Group\GroupRepository:
    #        arguments:
    #            $modulesGroups: '%portal_modules%'

    App\Model\Api\Module\ModuleGetterYML:
        arguments:
            $configuration: '%modules%'

    App\Model\Api\Module\Module:
        arguments:
            $moduleBlueprint: '%module_blueprint%'
    App\Model\Api\UserManagement\User\User:
        arguments:
            $dn: ''
            $sAMAccountName: ''
            $email: ''
            $firstName: ''
            $lastName: ''
            $company: ''

    App\Model\Api\PortalUser\PortalUser:
        arguments:
            $userMode: '@App\Model\Api\UserMode\Redis\UserModeRedis'
            $inMemoryConnector: '@App\Model\Api\Connector\Redis\RedisConnector'
        public: true

    App\Model\Api\Connector\LDAP\ConnectionAttributesLDAP:
        arguments:
            $LDAPConnectionAttributesArray: '%ldap%'
        public: true

    App\Model\Api\Portal\PortalRepository:
        arguments:
            $userManagementRead: '@App\Model\Api\UserManagement\Facade\Redis\UserManagementReadRedis'
        public: true

    App\Model\Api\Connector\Redis\RedisConnector:
        arguments:
            $redisConnectionDsn: '%redis_connection_dsn%'

    App\Security\FormLoginAuthenticator:
        arguments:
            $ldap: '@LDAPReadInterface'
            $domain: '%domain%'

    App\Security\User\ABUSUserProvider:
        arguments:
            $ldap: '@LDAPReadInterface'
            $baseDn: OU=Benutzer,OU=DMZ,DC=abus-vpn,DC=de
            $searchDn: CN=webserver_read,OU=Benutzer,OU=DMZ,DC=abus-vpn,DC=de
            $searchPassword: '%env(LDAP_READ_PASSWORD)%'
            $uidKey: sAMAccountName
            $filter: (mail={username})

    App\Model\Api\PasswordManagement\ResetPassword:
        arguments:
            $userReadRepository: '@App\Model\Api\UserManagement\User\LDAP\UserReadRepositoryLDAP'

    App\Model\Api\PasswordManagement\UserPasswordRecovery:
        arguments:
            $userReadRepository: '@App\Model\Api\UserManagement\User\LDAP\UserReadRepositoryLDAP'

    pdoportal:
        class: PDO
        arguments: ['mysql:host=%database_host%;dbname=%database_name%;charset=%database_charset%', '%database_user%', '%database_password%']
        calls:
            - [setAttribute, [3, 2]] # \PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION

    pdosales:
        class: PDO
        arguments: [ 'mysql:host=%database_host%;dbname=%sales_database_name%;charset=%database_charset%', '%sales_database_user%', '%sales_database_password%' ]
        calls:
            - [ setAttribute, [ 3, 2 ] ] # \PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION

    pdomontage:
        class: PDO
        arguments: [ 'mysql:host=%database_host%;dbname=%montage_database_name%;charset=%database_charset%', '%montage_database_user%', '%montage_database_password%' ]
        calls:
            - [ setAttribute, [ 3, 2 ] ] # \PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION

    pdocatalogcreator:
        class: App\Model\Service\Database\OptionalPDOConnection
        arguments: 
            - 'mysql:host=%catalog2020_database_host%;dbname=%catalog2020_database_name%;charset=%catalog2020_database_charset%'
            - '%catalog2020_database_user%'
            - '%catalog2020_database_password%'
            - []
            - '@App\Model\Api\Logger\Logger'

    pdotdata:
        class: PDO
        arguments: ['mysql:host=%tdata_database_host%;dbname=%tdata_database_name%;charset=%tdata_database_charset%', '%tdata_database_user%', '%tdata_database_password%']
        calls:
            - [setAttribute, [3, 2]] # \PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION

    pdoinformation:
        class: PDO
        arguments: ['mysql:host=%information_database_host%;dbname=%information_database_name%;charset=%information_database_charset%', '%information_database_user%', '%information_database_password%']
        calls:
            - [ setAttribute, [ 3, 2 ] ] # \PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION

    pdoservice:
        class: PDO
        arguments: [ 'mysql:host=%database_host%;dbname=%service_database_name%;charset=%database_charset%', '%service_database_user%', '%service_database_password%' ]
        calls:
            - [ setAttribute, [ 3, 2 ] ] # \PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION

    pdotdatapreview:
        class: PDO
        arguments: ['mysql:host=%tdata_preview_database_host%;dbname=%tdata_preview_database_name%;charset=%tdata_preview_database_charset%', '%tdata_preview_database_user%', '%tdata_preview_database_password%']
        calls:
            - [setAttribute, [3, 2]] # \PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION

    # Optional PDO connection for ABUKonfis - only connects when needed
    pdoabukonfis:
        class: App\Model\Service\Database\OptionalPDOConnection
        arguments:
            - 'mysql:host=%abukonfis_database_host%;dbname=%abukonfis_database_name%;charset=%abukonfis_database_charset%'
            - '%abukonfis_database_user%'
            - '%abukonfis_database_password%'
            -
                2: 5  # PDO::ATTR_TIMEOUT = 5 seconds
                3: 2  # PDO::ATTR_ERRMODE = PDO::ERRMODE_EXCEPTION
                12: false  # PDO::ATTR_PERSISTENT = false
            - '@App\Model\Api\Logger\LoggerInterface'

    pdowebsite:
        class: PDO
        arguments: ['mysql:host=%website_database_host%;dbname=%website_database_name%;charset=%website_database_charset%', '%website_database_user%', '%website_database_password%']
        calls:
            - [setAttribute, [3, 2]] # \PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION

    pdonextcloud:
        class: PDO
        arguments: ['mysql:host=%nextcloud_database_host%;dbname=%nextcloud_database_name%;charset=%nextcloud_database_charset%', '%nextcloud_database_user%', '%nextcloud_database_password%']
        calls:
            - [setAttribute, [3, 2]] # \PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION

    App\Model\Api\Setting\SettingRepository:
        arguments:
            $dbPortal: '@pdoportal'

    App\Model\Api\CatalogCreator\CatalogCreatorRepository:
        arguments:
            $pdocatalogcreator: '@pdocatalogcreator'
            $logger: '@App\Model\Api\Logger\Logger'

    App\Model\Api\CatalogCreator\CatalogCreator:
        #        arguments:
        #            $abuetk_link: 'http://abuetk.%env(DOMAIN)%/index.php'
        public: true



    App\Model\Api\Werksvertretung\WerksvertretungRepository:
        arguments:
            $dbPortal: '@pdoportal'

    # Logger
    monolog.formatter.abus:
        class: Monolog\Formatter\LineFormatter
        arguments:
            - "%%datetime%% | %%extra.request_ip%% | %%extra.client_ip%% | %%extra.username%% | %%extra.channel%% | %%channel%%.%%level_name%%: %%message%% %%context%% | %%extra.requestUri%% | %%extra.method%% | %%extra.useragent%%\n"

    App\Monolog\ABUSProcessor:
        arguments:
            - '@request_stack'
            - '@security.token_storage'
            - '@App\Model\Api\UserMode\Redis\UserModeRedis'
        tags:
            - { name: monolog.processor }
            - { name: kernel.event_listener, event: kernel.request, method: onKernelRequest }


    App\EventListener\LoggerListener:
        arguments:
            - ['@monolog.logger.abus']
        tags:
            - { name: kernel.event_listener, event: abus.logger }

    App\EventListener\KernelListener:
        arguments:
            - "@security.token_storage"
        tags:
            - { name: kernel.event_listener, event: kernel.controller, method: onKernelRequest }

    App\EventListener\SwitchUserListener:
        tags:
            - { name: kernel.event_listener, event: security.switch_user, method: onSwitchUser }

    Knp\Snappy\Pdf:
        public: true

    Smalot\PdfParser\Parser:

    # Technische Daten
    App\Model\TechnicalData\:
        resource: '../src/Model/TechnicalData/*'

    App\Model\TechnicalData\TechnicalData:
        arguments:
            $dbTdata: '@pdotdata'
            $dbTdataPreview: '@pdotdatapreview'
            $dbPortal: '@pdoportal'
            $parameters: "%technicalData%"
        public: true

    App\Model\Api\TechnicalData\TechnicalDataRepository:
        arguments:
            $dbPortal: '@pdoportal'
            $parameters: "%technicalData%"

    App\Model\TechnicalData\DownloadLogger:
        arguments:
            $dbPortal: '@pdoportal'

    App\Model\Api\FileOperation\FileHandler:
        public: true
        arguments:
            $basePath: "%file_basePath%"

    App\Model\Api\Summernote\ImageUploader:
        arguments:
            $uploadDirectory: "%gitlab_upload_directory%"
        public: true

    League\HTMLToMarkdown\HtmlConverter:

    App\Model\Api\IssueTracker\IssueTrackerGitlab:
        arguments:
            $gitlabParameters: "%gitlab%"
            $baseUploadPath: "%file_basePath%"

    App\Model\Api\IssueTracker\IssueTrackerAsana:
        arguments:
            $asanaParameters: "%asana%"
        public: true

    # TaskRunner
    App\Model\TaskRunner\TaskPermission:
        arguments:
            $registeredBundles: ['ABUSBox']

    App\Model\TaskRunner\TaskCreator:
        arguments:
            $entityManager: '@doctrine.orm.taskrunner_entity_manager'

    App\Model\TaskRunner\TaskExecutor:
        arguments:
            $entityManager: '@doctrine.orm.taskrunner_entity_manager'
            $taskServices: { ABUSBox: '@App\Model\Box\TaskRunner\TaskRunner' }

    # Box
    App\Entity\Box\FilesystemIndex:
    #        arguments:
    #            $configurationObject: '@App\Model\Box\Configuration\ConfigurationObject'

    App\Model\Box\Crawler\Crawler:
        arguments:
            $entityManager: '@doctrine.orm.storage_entity_manager'

    App\Model\Box\Crawler\Configuration\CrawlerConfiguration:
        arguments:
            $configuration: '%storage%'

    #    App\Model\Box\Configuration\ConfigurationObject:


    App\Model\Box\Content\Loader:
        arguments:
            $entityManager: '@doctrine.orm.storage_entity_manager'
            $configuration: '%storage%'

    App\Model\Box\DatabaseOperations\DatabaseOperations:
        arguments:
            $entityManager: '@doctrine.orm.storage_entity_manager'

    App\Model\Box\Content\ContentObject:
        arguments:
            $entityManager: '@doctrine.orm.storage_entity_manager'

    App\Model\Box\TaskRunner\TaskRunner:

    App\Model\Box\Loader\LoaderDatabase:
        arguments:
            $entityManager: '@doctrine.orm.storage_entity_manager'

    App\Model\Box\Download\Download:
        arguments:
            $configuration: '%storage%'

    App\Model\Box\Upload\FolderOperations:
        arguments:
            $configuration: '%storage%'


    # NEXTCLOUD
    App\Model\Box\Nextcloud\inMemory\inMemoryConnectorRedis:
        arguments:
            $redisConnectionDsn: '%redis_connection_dsn%'

    App\Model\Box\Nextcloud\Database\Nextcloud\NextcloudDatabaseConnectorMySQL:
        arguments:
            $pdonextcloud: '@pdonextcloud'

    App\Model\Box\Nextcloud\Database\Portal\PortalDatabaseConnectorMySQL:
        arguments:
            $pdoportal: '@pdoportal'

    App\Model\Box\Nextcloud\Connector\Webdav\WebdavConnectorGuzzle:
    App\Model\Box\Nextcloud\Connector\Webdav\WebdavConnectorGuzzleConfigParser:

    App\Model\Box\Nextcloud\ConfigParser\ConfigParser:
        arguments:
            #            $webdavConnector: '@App\Model\Box\Nextcloud\Connector\FileSystemConnector'
            $webdavConnector: '@App\Model\Box\Nextcloud\Connector\Webdav\WebdavConnectorGuzzleConfigParser'

    App\Model\Box\Nextcloud\FileZipper\FileZipper:
        arguments:
            $webdavConnector: '@App\Model\Box\Nextcloud\Connector\Webdav\WebdavConnectorGuzzle'

    # APIS
    App\Entity\APIS\Favorite:
    App\Entity\APIS\MarkedAsRead:

    App\Model\APIS\Repository\RepositoryDatabase:
        arguments:
            $entityManager: '@doctrine.orm.apis_entity_manager'

    App\Model\APIS\InformationBuilder\InformationBuilderABUS:
        arguments:
            $entityManager: '@doctrine.orm.apis_entity_manager'

    App\Model\APIS\Loader\LoaderDatabase:
        arguments:
            $entityManager: '@doctrine.orm.apis_entity_manager'

    App\Model\APIS\Storer\StorerDatabase:
        arguments:
            $entityManager: '@doctrine.orm.apis_entity_manager'

    App\Model\APIS\Remover\RemoverDatabase:
        arguments:
            $entityManager: '@doctrine.orm.apis_entity_manager'

    App\Model\APIS\Configuration\ConfigurationGetterYML:
        arguments:
            $configuration: '%apis%'
            $userManagementRead: '@App\Model\Api\UserManagement\Facade\Redis\UserManagementReadRedis'

    # Management
    App\Model\Management\LDAPProblemFinder:
        arguments:
            $dbPortal: '@pdoportal'

    crm_connector_suitecrm:
        class: App\Model\CRM\Connector\CRMConnectorSuiteCRM
        arguments:
            $baseURI: '%env(CRM_SUITECRM_BASE_URI)%'
            $username: '%env(CRM_SUITECRM_USERNAME)%'
            $password: '%env(CRM_SUITECRM_PASSWORD)%'

    crm_connector_suitecrm_stage:
        class: App\Model\CRM\Connector\CRMConnectorSuiteCRM
        arguments:
            $baseURI: '%env(CRM_SUITECRM_STAGE_BASE_URI)%'
            $username: '%env(CRM_SUITECRM_STAGE_USERNAME)%'
            $password: '%env(CRM_SUITECRM_STAGE_PASSWORD)%'

    # ABUKonfis
    App\Model\Api\ABUKonfis\ABUKonfis:
        arguments:
            $dbABUKonfis: '@pdoabukonfis'
            $dbPortal: '@pdoportal'
            $connectorSuiteCRM: '@crm_connector_suitecrm'
            $connectorSuiteCRMStage: '@crm_connector_suitecrm_stage'
            $LDAPReader: '@App\Model\Api\UserManagement\User\LDAP\UserReadRepositoryLDAP'
            $REDISReader: '@App\Model\Api\UserManagement\User\Redis\UserReadRepositoryRedis'
            $managementRead: '@App\Model\Api\UserManagement\Facade\Redis\UserManagementReadRedis'
            $sendDataToStage: '%env(CRM_SEND_DATA_STAGE)%'

    # UserManagement
    #    App\Model\Api\UserManagement\Configuration:
    #        arguments:
    #            $parameters: '%usermanagement%'
    #
    App\Repository\Api\CompanyRepository:
        arguments:
            $dbPortal: '@pdoportal'
    #
    #    App\Model\Api\UserManagement\CompanyResponsibilityRepository:
    #            arguments:
    #                $entityManager: '@doctrine.orm.usermanagement_entity_manager'
    #
    #    App\Model\Api\UserManagement\PermissionPackageRepository:
    #            arguments:
    #                $entityManager: '@doctrine.orm.usermanagement_entity_manager'


    # Commands

    App\Command\Api\LDAP2RedisCommand:
        tags:
            - { name: console.command }

    #    App\Command\Api\LDAP2Redis:
    #        arguments:
    #            $ldap: '@LDAPReadInterface'
    #        tags:
    #            - { name: console.command }

    App\Command\TaskRunner\TaskRunnerCommand:
        tags:
            - { name: console.command }

    App\Command\APIS\PublishCommand:
        arguments:
        tags:
            - { name: console.command }

    App\Command\APIS\AddCategoryCommand:
        arguments:
        tags:
            - { name: console.command }

    App\Command\APIS\RemoveCategoryCommand:
        arguments:
        tags:
            - { name: console.command }

    App\Command\TechnicalData\DailyMailCommand:
        arguments:
        tags:
            - { name: console.command }

    App\Command\TechnicalData\DailyMailViaABUKonfisCommand:
        arguments:
        tags:
            - { name: console.command }

    App\Command\Box\NextcloudTagsCommand:
        arguments:
        tags:
            - { name: console.command }

    App\Command\Box\NextcloudElasticsearchCommand:
        arguments:
        tags:
            - { name: console.command }

    App\Command\Box\NextcloudConfigParserCommand:
        arguments:
        tags:
            - { name: console.command }

    App\Command\Box\NextcloudTempCleanerCommand:
        arguments:
        tags:
            - { name: console.command }

    App\Command\CADExchange\CrawlerCommand:
        arguments:
        tags:
            - { name: console.command }

    App\Command\News\MailCommand:
        arguments:
        tags:
            - { name: console.command }

    #PasswordManagement
    App\Model\Api\PasswordManagement\PasswordSecurity\LdapPasswordSecurity:
        arguments:
            $ldap: '@LDAPReadInterface'
            $connectorLDAP: '@ConnectorLDAPWrite'

    #Services
    App\Model\Service\Service:

    #News
    App\Model\News\ModuleGetter:
        arguments:
            $moduleRepository: '@App\Model\Api\Module\ModuleRepositoryInterface'

    App\Model\News\GroupGetter:
        arguments:
            $userManagementRead: '@App\Model\Api\UserManagement\Facade\Redis\UserManagementReadRedis'

    App\Model\News\Sanitizer\Sanitizer:

    App\Model\News\Serializer\Serializer:

    App\Model\News\NewsBuilder\FilePurifier:


    App\Model\News\NewsBuilder\NewsPurifier:
        arguments:
            $loader: '@App\Model\News\Loader\LoaderDatabase'
            $filePurifier: '@App\Model\News\NewsBuilder\FilePurifier'

    App\Model\News\Permission\PermissionChecker:

    App\Entity\News\File:
    App\Model\News\Handler\Uploader\UploadHandler:
        arguments:
            $loader: '@App\Model\News\Loader\LoaderDatabase'
            $storer: '@App\Model\News\Storer\StorerDatabase'
            $remover: '@App\Model\News\Remover\RemoverDatabase'

    App\Model\News\NewsBuilder\NewsBuilder:
        arguments:
            $loader: '@App\Model\News\Loader\LoaderDatabase'
            $newsPurifier: '@App\Model\News\NewsBuilder\NewsPurifier'
            $filePurifier: '@App\Model\News\NewsBuilder\FilePurifier'

    App\Model\News\Storer\StorerDatabase:
        arguments:
            $entityManager: '@doctrine.orm.news_entity_manager'

    App\Model\News\Loader\LoaderDatabase:
        arguments:
            $entityManager: '@doctrine.orm.news_entity_manager'

    App\Model\News\Remover\RemoverDatabase:
        arguments:
            $entityManager: '@doctrine.orm.news_entity_manager'

    App\Model\News\Handler\News\NewsHandler:
        arguments:
            $storer: '@App\Model\News\Storer\StorerDatabase'
            $loader: '@App\Model\News\Loader\LoaderDatabase'
            $remover: '@App\Model\News\Remover\RemoverDatabase'
    App\Model\News\Handler\Mailer\MailerHandler:
        arguments:
            $storer: '@App\Model\News\Storer\StorerDatabase'
            $userManagementRead: '@App\Model\Api\UserManagement\Facade\Redis\UserManagementReadRedis'

    App\Model\CADExchange\Connector\inMemory\Redis\inMemoryConnectorRedis:
        arguments:
            $redisConnectionDsn: '%redis_connection_dsn%'

    App\Model\CADExchange\Connector\Database\SQL\DatabaseConnectorSQL:
        arguments:
            $databaseConnection: '@pdoabukonfis'

    App\Model\CADExchange\Connector\Webdav\Guzzle\WebdavConnectorGuzzle:
    App\Model\CADExchange\Connector\Nextcloud\Guzzle\NextcloudConnectorGuzzle:

    App\Model\CADExchange\Config\ConfigObject:

    App\Model\CADExchange\Config\ConfigReader:
        arguments:
            $webdavConnector: '@App\Model\CADExchange\Connector\Webdav\Guzzle\WebdavConnectorGuzzle'
            $configObject: '@App\Model\CADExchange\Config\ConfigObject'

    App\Model\CADExchange\Mailer\Mailer:
        arguments:
            $mailer: '@App\Model\Api\Mailer\MailerSwift'

    App\Model\CADExchange\Crawler\Crawler:
        arguments:
            $inMemoryConnector: '@App\Model\CADExchange\Connector\inMemory\Redis\inMemoryConnectorRedis'
            $webdavConnector: '@App\Model\CADExchange\Connector\Webdav\Guzzle\WebdavConnectorGuzzle'
            $nextcloudConnector: '@App\Model\CADExchange\Connector\Nextcloud\Guzzle\NextcloudConnectorGuzzle'
            $databaseConnector: '@App\Model\CADExchange\Connector\Database\SQL\DatabaseConnectorSQL'
            $configReader: '@App\Model\CADExchange\Config\ConfigReader'
            $mailer: '@App\Model\CADExchange\Mailer\Mailer'
        public: true

    #Reporting
    App\Model\Reporting\Reporting:
        arguments:
            $pdoPortal: '@pdoportal'
            $pdoWebsite: '@pdowebsite'
            $parameters: "%technicalData%"

    App\Model\Api\Country\Country:
        arguments:
            $dbPortal: '@pdoportal'

    App\Model\Registration\FurtherService:
        arguments:
            $dbportal: '@pdoportal'
            $mailerSwift: '@App\Model\Api\Mailer\MailerSwift'
            $moduleRepository: '@App\Model\Api\Module\ModuleRepository'
            $registrationReceiversEmail: '%registration_receivers_email%'
            $domain: '%domain%'

    App\Model\Registration\PortalRegister:
        arguments:
            $dbportal: '@pdoportal'
            $mailerSwift: '@App\Model\Api\Mailer\MailerSwift'
            $moduleRepository: '@App\Model\Api\Module\ModuleRepository'
            $registrationReceiversEmail: '%registration_receivers_email%'
            $subsidiaryCompanyResellersReceiversEmails: '%subsidiary_company_receivers_emails%'
            $domain: '%domain%'

    App\Model\Registration\Validator\PortalRegisterValidator:

    App\Model\Service\LDAP\LDAP:
        arguments:
            $dbportal: '@pdoportal'

    App\Model\Registration\Request\LDAPRequest:
    App\Model\Registration\Request\ServiceRequest:
    App\Model\Service\MailGenerator\MailGeneratorRequest:

    App\Model\Registration\EmailProvider\MailGenerator:
        arguments:
            $registrationReceiversEmail: '%registration_receivers_email%'
            $adminsEmail: '%admin_emails%'

    App\Model\Registration\Request\PortalRequest:
        arguments:
            $LDAP: '@App\Model\Api\UserManagement\User\LDAP\UserReadRepositoryLDAP'

    App\Model\Registration\Request\PermissionRequest:
        arguments:
            $translator: '@Symfony\Contracts\Translation\TranslatorInterface'

    App\Model\Registration\Request\TDataRequest:
    App\Model\Registration\Builder\LDAPUserBuilder:
        arguments:
            $companyRepository: '@App\Repository\Api\CompanyRepository'

    App\Model\Registration\PortalRegisterRequestBase:
        arguments:
            $dbportal: '@pdoportal'
            $moduleRepository: '@App\Model\Api\Module\ModuleRepository'

    #    # makes classes in src/ available to be used as services
    #    # this creates a service per class whose id is the fully-qualified class name
    #    App\Model\Api\User\:
    #        resource: '../src/Model/Api/User/*'
    #        # you can exclude directories or files
    #        # but if a service is unused, it's removed anyway
    ##        exclude: '../src/Model/Api/User/{LDAP}'
    #        public: true
    #        arguments:
    #            $LDAPConnectionAttributesArray: '%ldap%'

    # AccessManagment

    App\Model\AccessManagement\Notification\AccountNotificationMailer:

    App\Model\AccessManagement\ChatUserDelete:

    App\Model\AccessManagement\ArchitectsDelete:
        arguments:
            $userRepository: '@App\Model\AccessManagement\Repository\UserRepository'

    App\Model\AccessManagement\AccountVerification:
        arguments:
            $account: '@App\Model\AccessManagement\Account'
            $accountRepository: '@App\Model\AccessManagement\Repository\AccountRepository'

    App\Model\AccessManagement\Account:
        arguments:
            $accountRepository: '@App\Model\AccessManagement\Repository\AccountRepository'

    tokenStorage:
        class: Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorage

    authorizationChecker:
        class: Symfony\Component\Security\Core\Authorization\AuthorizationChecker

    App\Model\AccessManagement\Repository\UserRepository:
        arguments:
            $dbPortal: '@pdoportal'

    App\Repository\APIS\InformationRepository:
        arguments:
            $dbinformation: '@pdoinformation'

    App\Model\AccessManagement\Repository\AccountRepository:
        arguments:
            $dbPortal: '@pdoportal'

    App\Model\ABUSBookingsTool\AppointmentEvent:
        arguments:
            $repository: '@App\Repository\Appointment\EventRepository'

    App\Model\ABUSBookingsTool\AppointmentCategory:
        arguments:
            $repository: '@App\Repository\Appointment\CategoryRepository'

    App\Model\ABUSBookingsTool\AppointmentDate:
        arguments:
            $repository: '@App\Repository\Appointment\DateRepository'

    App\Model\ABUSBookingsTool\AppointmentBooking:
        arguments:
            $repository: '@App\Repository\Appointment\BookingRepository'

    App\Repository\Appointment\BookingRepository:
        factory: [ '@doctrine.orm.pimcore_entity_manager', getRepository ]
        arguments:
            - App\Entity\Pimcore\Appointment\AbusAppointmentBookings

    App\Repository\Appointment\EventRepository:
        factory: [ '@doctrine.orm.pimcore_entity_manager', getRepository ]
        arguments:
            - App\Entity\Pimcore\Appointment\AbusAppointmentEvents

    App\Repository\Appointment\CategoryRepository:
        factory: [ '@doctrine.orm.pimcore_entity_manager', getRepository ]
        arguments:
            - App\Entity\Pimcore\Appointment\AbusAppointmentCategories

    App\Repository\Appointment\DateRepository:
        factory: [ '@doctrine.orm.pimcore_entity_manager', getRepository ]
        arguments:
            - App\Entity\Pimcore\Appointment\AbusAppointmentDates

    App\Model\AccessManagement\Common:
        arguments:
            $userRepository: '@App\Model\AccessManagement\Repository\UserRepository'

    App\Model\AccessManagement\Services\CreateAccount:
    App\Model\AccessManagement\Services\DeleteAccount:
    App\Model\AccessManagement\Services\VpnCreate:
    App\Model\AccessManagement\Services\VpnReassign:
    App\Model\AccessManagement\Services\ListAccount:

    App\Model\AccessManagement\Request\ABUKonfisRequestHandler:
    App\Model\AccessManagement\Request\DeleteAccountRequestHandler:
    App\Model\AccessManagement\Request\VpnRequestHandler:



    # controllers are imported separately to make sure they
    # have the tag that allows actions to type-hint services
    App\Controller\:
        resource: '../src/Controller/'
        tags: ['controller.service_arguments']

    App\Controller\Registration\RegistrationController:
        arguments:
            $portalRegister: '@App\Model\Registration\PortalRegister'
            $portalRequest: '@App\Model\Registration\Request\PortalRequest'

    App\Controller\Registration\GrantPermissionController:
        arguments:
            $portalRegister: '@App\Model\Registration\PortalRegister'
            $permissionRequest: '@App\Model\Registration\Request\PermissionRequest'

    App\Controller\Registration\TDataController:
        arguments:
            $tDataRequest: '@App\Model\Registration\Request\TDataRequest'

    App\Controller\Registration\FurtherServicesController:
        arguments:
            $portalUser: '@App\Model\Api\PortalUser\PortalUser'

    App\Controller\Service\ServiceController:
        arguments:
            $serviceRequest: '@App\Model\Registration\Request\ServiceRequest'

    App\Controller\MailGenerator\MailGeneratorController:
        arguments:
            $mailGeneratorRequest: '@App\Model\Service\MailGenerator\MailGeneratorRequest'

    App\Controller\Service\LDAPController:
        arguments:
            $ldap: '@App\Model\Service\LDAP\LDAP'
            $userReadRepositoryLDAP: '@App\Model\Api\UserManagement\User\LDAP\UserReadRepositoryLDAP'
            $user: '@App\Model\Api\UserManagement\User\User'
            $lDAPRequest: '@App\Model\Registration\Request\LDAPRequest'
            $userBuilder: '@App\Model\Registration\Builder\LDAPUserBuilder'
            $userWriteRepositoryLDAP: '@App\Model\Api\UserManagement\User\LDAP\UserWriteRepositoryLDAP'
            $groupReadRepository: '@App\Model\Api\UserManagement\Group\Redis\GroupReadRepositoryRedis'

    #AccessManagment
    App\Controller\AccessManagement\CommonController:
        arguments:
            $common: '@App\Model\AccessManagement\Common'

    App\Controller\AccessManagement\DeleteAccountController:
        arguments:
            $account: '@App\Model\AccessManagement\Account'
            $accountVerification: '@App\Model\AccessManagement\AccountVerification'
            $deleteAccount: '@App\Model\AccessManagement\Services\DeleteAccount'
            $deleteAccountRequestHandler: '@App\Model\AccessManagement\Request\DeleteAccountRequestHandler'

    App\Controller\AccessManagement\GrantController:
        arguments:
            $accountParameters: '%account_parameters%'
            $architectsDelete: '@App\Model\AccessManagement\ArchitectsDelete'

    App\Controller\AccessManagement\VpnAccountController:
        arguments:
            $accountVerification: '@App\Model\AccessManagement\AccountVerification'
            $vpnCreate: '@App\Model\AccessManagement\Services\VpnCreate'
            $vpnReassign: '@App\Model\AccessManagement\Services\VpnReassign'
            $vpnRequestHandler: '@App\Model\AccessManagement\Request\VpnRequestHandler'

    App\Controller\AccessManagement\CreateAccountController:
        arguments:
            $ABUKonfisRequestHandler: '@App\Model\AccessManagement\Request\ABUKonfisRequestHandler'
            $createAccount: '@App\Model\AccessManagement\Services\CreateAccount'

    App\Controller\AccessManagement\ListAccountController:
        arguments:
            $listAccount: '@App\Model\AccessManagement\Services\ListAccount'

    App\Controller\InternalBooking\InternalBookingController:
        arguments:
            $booking: '@App\Model\ABUSBookingsTool\AppointmentBooking'
            $event: '@App\Model\ABUSBookingsTool\AppointmentEvent'
            $category: '@App\Model\ABUSBookingsTool\AppointmentCategory'


    Mainick\KeycloakClientBundle\Interface\IamClientInterface:
        alias: Mainick\KeycloakClientBundle\Provider\KeycloakClient

    App\Security\Voter\RoleInRedisVoter:
        arguments:
            $userReadRepository: '@App\Model\Api\UserManagement\User\Redis\UserReadRepositoryRedis'
        tags:
            - { name: security.voter }