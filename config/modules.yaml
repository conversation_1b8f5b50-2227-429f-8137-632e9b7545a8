parameters:

    admin_emails:
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>

    registration_receivers_email:
        - <EMAIL>
        - <EMAIL>

    subsidiary_company_receivers_emails:
        - <EMAIL>

    module_blueprint:
        inDevelopment: false
        uniqueIdentifier: ''
        identifier: ''
        name: 'module/'
        icon: ''
        link: ''
        convertLinkToDevelopment: true
        openInNewTab: false
        linkActive: true
        roles: []
        rolesCanSeeRightSidebar:
            - ^ROLE_PORTAL_ADMIN$
            - ^ROLE_PORTAL_ADMIN_USERMODE$
            - ^ROLE_PORTAL_ADMIN_ACTTIVEUSERS$
        requestable: false
        canBeSee: true
        maintenance: false
        order: 0
        subModules: []
        descriptions: []
        decisionMakerEmail: []
        ldapGroups: []


    modules:
        dashboard:
            uniqueIdentifier: 395e3d6c5b5b3e444d215b2747
            identifier: dashboard
            name: 'module/dashboard'
            icon: far fa-tachometer-alt
            link: 'https://portal.abus-kransysteme.de'
            convertLinkToDevelopment: true
            openInNewTab: false
            roles:
                - ^ROLE_ANY$
            rolesCanSeeRightSidebar:
                - ^ROLE_PORTAL_NEWS_ADMIN$
            requestable: false
            maintenance: false
            order: 100

        #        box:
        #            uniqueIdentifier: 63633e673a7e4e4d3b236e4343
        #            identifier: box
        #            name: 'module/box'
        #            icon: fas fa-cloud
        #            link: 'https://portal.abus-kransysteme.de/box'
        #            convertLinkToDevelopment: true
        #            openInNewTab: false
        #            roles:
        #                - ROLE_ANY
        #            rolesCanSeeRightSidebar:
        #                - ROLE_ANY
        #            maintenance: false
        #            order: 150

        portalaccessVideoconferenceChat:
            uniqueIdentifier: 0d81435393cc4cb59d46251efb9a9c16
            identifier: portalaccessVideoconferenceChat
            name: 'module/portalaccess_Videoconference_Chat'
            link: 'https://portal.abus-kransysteme.de'
            convertLinkToDevelopment: false
            openInNewTab: false
            requestable: true
            maintenance: false
            order: 99
            decisionMakerEmail: <EMAIL>
            ldapGroups:
                PORTAL_2018:
                    name: PORTAL_CHAT
                    nice_name: PORTAL_CHAT

        box:
            uniqueIdentifier: 6a4e353c6b657b242d2f28655b
            identifier: box
            name: 'module/box'
            icon: fas fa-cloud
            link: 'https://portal.abus-kransysteme.de/box/nextcloud'
            convertLinkToDevelopment: true
            openInNewTab: false
            roles:
                - ^ROLE_ANY$
            rolesCanSeeRightSidebar:
                - ^ROLE_ANY$
            requestable: false
            maintenance: false
            order: 200
            submodules:
                pricelist:
                    uniqueIdentifier: 55516bb65e8a49f6abda96d49fc1b9e7
                    identifier: pricelist
                    name: 'module/pricelist'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    canBeSee: false
                    roles:
                        - ^ROLE_PORTAL_PRICELIST$
                    requestable: true
                    descriptions:
                        - de_DE: 'module/pricelist/description/de'
                        - en_GB: 'module/pricelist/description/en'
                        - fr_FR: 'module/pricelist/description/fr'
                        - es_ES: 'module/pricelist/description/es'
                    decisionMakerEmail: <EMAIL>
                    ldapGroups:
                        PORTAL_PRICELIST:
                            name: PORTAL_PRICELIST
                            nice_name: ABUS Box - Preislisten

        nextcloud:
            uniqueIdentifier: 53766326473339405f27713222
            identifier: nextcloud
            name: 'module/nextcloud'
            icon: fas fa-cloud-upload
            link: 'https://portal.abus-kransysteme.de/nextcloud'
            convertLinkToDevelopment: true
            openInNewTab: false
            roles:
                - ^ROLE_PORTAL_NEXTCLOUD_BACKEND$
            requestable: false
            maintenance: false
            order: 300

        documentation:
            uniqueIdentifier: 29226c685e542d65503d495336
            identifier: documentation
            name: 'module/documentation'
            icon: fal fa-book
            link: 'https://portal.abus-kransysteme.de/documentation'
            convertLinkToDevelopment: true
            openInNewTab: false
            roles:
                - ^ROLE_ANY$
            requestable: false
            maintenance: false
            order: 350
            decisionMakerEmail: <EMAIL>
            submodules:
                pdf:
                    uniqueIdentifier: 5a55265a2e4631562b31262b36
                    identifier: 'documentation_pdf'
                    name: 'module/documentation_pdf'
                    link: 'https://portal.abus-kransysteme.de/documentation/pdf'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_ANY$
                    requestable: false

                online:
                    uniqueIdentifier: 634e25283f72744c704a527533
                    identifier: 'documentation_online'
                    name: 'module/documentation_online'
                    link: 'https://portal.abus-kransysteme.de/documentation/online'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_ANY$
                    requestable: false

                service:
                    uniqueIdentifier: 5c786d2d236f362e592928312d
                    identifier: 'documentation_service'
                    name: 'module/documentation_service'
                    link: 'https://portal.abus-kransysteme.de/documentation/service'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_ABUS Intern$
                        - ^ROLE_PORTAL_GRUPPE_MONTAGEPARTNER$
                        - ^ROLE_PORTAL_GRUPPE_SERVICEPARTNER$
                        - ^ROLE_PORTAL_GRUPPE_SERVICEPARTNER_MASTER$
                        - ^ROLE_PORTAL_GRUPPE_ABNAHMEPARTNER$
                        - ^ROLE_PORTAL_GRUPPE_EXPORTPARTNER$
                        - ^ROLE_PORTAL_GRUPPE_TOECHTER$
                    requestable: false

        abukonfis:
            uniqueIdentifier: 5f66684a4b4e3c7a5067274a67
            identifier: abukonfis
            name: 'module/abukonfis'
            icon: far fa-file-alt
            link: 'https://portal.abus-kransysteme.de/abukonfis'
            convertLinkToDevelopment: true
            openInNewTab: false
            roles:
                - ^ROLE_PORTAL_ABUKONFIS_
            requestable: false
            maintenance: false
            order: 400
            submodules:
                abukonfis:
                    uniqueIdentifier: 4978212d442b537227437b5375
                    identifier: 'abukonfis'
                    name: 'module/abukonfis_online'
                    link: 'https://portal.abus-kransysteme.de/abukonfis'
                    convertLinkToDevelopment: true
                    openInNewTab: true
                    roles:
                        - ^ROLE_PORTAL_ABUKONFIS_ONLINE$
                    requestable: false

                abukonfis-work:
                    uniqueIdentifier: 7d537e572a243c672e27652352
                    identifier: 'abukonfis-work'
                    name: 'module/abukonfis_online_work'
                    link: 'https://portal.abus-kransysteme.de/abukonfis-work'
                    convertLinkToDevelopment: true
                    openInNewTab: true
                    roles:
                        - ^ROLE_PORTAL_ABUKONFIS_ONLINE_WORK$
                    requestable: false

                abukonfis-intern:
                    uniqueIdentifier: 477648743b25356f2b5f6e3528
                    identifier: 'abukonfis-intern'
                    name: 'module/abukonfis_intern'
                    link: 'https://portal.abus-kransysteme.de/abukonfis-intern'
                    convertLinkToDevelopment: true
                    openInNewTab: true
                    roles:
                        - ^ROLE_PORTAL_ABUKONFIS_INTERN$
                    requestable: false

                abukonfis-intern-work:
                    uniqueIdentifier: 4f5b2c7c537c7355266179684f
                    identifier: 'abukonfis-intern-work'
                    name: 'module/abukonfis_intern_work'
                    link: 'https://portal.abus-kransysteme.de/abukonfis-intern-work'
                    convertLinkToDevelopment: true
                    openInNewTab: true
                    roles:
                        - ^ROLE_PORTAL_ABUKONFIS_INTERN_WORK$
                    requestable: false

                abukonfis-test:
                    uniqueIdentifier: 4978212d442a237227437b5223
                    identifier: 'abukonfis-test'
                    name: 'module/abukonfis-test'
                    link: 'https://portal.abus-kransysteme.de/abukonfis-test'
                    convertLinkToDevelopment: true
                    openInNewTab: true
                    roles:
                        - ^ROLE_PORTAL_ABUKONFIS_TEST$

        spareparts:
            uniqueIdentifier: 2b645e27774e2550227e24675d
            identifier: spareparts
            name: 'module/spare_parts'
            icon: fab fa-opencart
            link: 'https://portal.abus-kransysteme.de/spareparts'
            convertLinkToDevelopment: true
            openInNewTab: false
            roles:
                - ^ROLE_PORTAL_CATALOG_(SPAREPARTS|WINETIS)$
            requestable: false
            maintenance: false
            order: 600
            decisionMakerEmail: <EMAIL>
            subModules:
                cart:
                    uniqueIdentifier: 5f457c564b63647b5327696445
                    identifier: cart
                    name: 'module/spare_parts_basket'
                    link: 'https://portal.abus-kransysteme.de/spareparts/cart'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    requestable: false
                    canBeSee: true
                    roles:
                        - ^ROLE_PORTAL_CATALOG_(SPAREPARTS|WINETIS)$
                search:
                    uniqueIdentifier: 6e3b554c257d3a3c3a2b2e4d42
                    identifier: search
                    name: 'module/spare_parts_search'
                    link: 'https://portal.abus-kransysteme.de/spareparts/search'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    requestable: false
                    canBeSee: true
                    roles:
                        - ^ROLE_PORTAL_CATALOG_(SPAREPARTS|WINETIS)$
                winetis:
                    uniqueIdentifier: e8079932032442fd8b4f9ec5cacb8e0b
                    identifier: winetis
                    name: 'module/spare_parts_winetis'
                    link: 'https://portal.abus-kransysteme.de/spareparts/catalog/WINETIS'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    requestable: true
                    canBeSee: false
                    roles:
                        - ^ROLE_PORTAL_CATALOG_WINETIS$
                    descriptions:
                        - de_DE: 'module/winetis/description/de'
                        - en_GB: 'module/winetis/description/en'
                        - fr_FR: 'module/winetis/description/fr'
                        - es_ES: 'module/winetis/description/es'
                    decisionMakerEmail: <EMAIL>
                    ldapGroups:
                        PORTAL_CATALOG_WINETIS:
                            name: PORTAL_CATALOG_WINETIS
                            nice_name: PORTAL_CATALOG_WINETIS
                sparepartspricelist:
                    uniqueIdentifier: 20ab982b342a4e738161ee4935e721e5
                    identifier: sparepartsprices
                    name: 'module/spare_parts_prices'
                    link: 'https://portal.abus-kransysteme.de/spareparts/catalog/Preisliste'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    requestable: true
                    canBeSee: false
                    roles:
                        - ^ROLE_PORTAL_CATALOG_CAN_SEE_PRICES$
                    descriptions:
                        - de_DE: 'module/spareparts/description/de'
                        - en_GB: 'module/spareparts/description/en'
                        - fr_FR: 'module/spareparts/description/fr'
                        - es_ES: 'module/spareparts/description/es'
                    decisionMakerEmail: <EMAIL>
                    ldapGroups:
                        PORTAL_CATALOG_CAN_SEE_PRICES:
                            name: PORTAL_CATALOG_CAN_SEE_PRICES
                            nice_name: PORTAL_CATALOG_CAN_SEE_PRICES

        information:
            uniqueIdentifier: 77793b76544d4841367444315c
            identifier: information
            name: 'module/information'
            icon: fas fa-bullhorn
            link: 'https://portal.abus-kransysteme.de/apis'
            convertLinkToDevelopment: true
            openInNewTab: false
            requestable: false
            roles:
                - ^ROLE_PORTAL_INFORMATION_(SALES|MOUNTING|SERVICE|TECHNIK)
                - ^ROLE_PORTAL_GRUPPE_MONTAGEPARTNER$
                - ^ROLE_PORTAL_GRUPPE_SERVICEPARTNER$
                - ^ROLE_PORTAL_GRUPPE_SERVICEPARTNER_MASTER$
                - ^ROLE_PORTAL_GRUPPE_SERVICEPARTNER_BASIC$
                - ^ROLE_PORTAL_GRUPPE_ABNAHMEPARTNER$
            maintenance: false
            order: 700
            decisionMakerEmail: <EMAIL>
            subModules:
                sales:
                    uniqueIdentifier: 41465927675b6c53285e383020
                    identifier: sales
                    name: 'module/information_sales'
                    link: 'https://portal.abus-kransysteme.de/apis/sales'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    requestable: true
                    roles:
                        - ^ROLE_PORTAL_INFORMATION_SALES
                    rolesCanSeeRightSidebar:
                        - ^ROLE_PORTAL_INFORMATION_SALES
                    decisionMakerEmail: <EMAIL>
                    ldapGroups:
                        PORTAL_INFORMATION_SALES_INTERN:
                            name: PORTAL_INFORMATION_SALES_INTERN
                            nice_name: ABUS Verkaufshinweise - Intern
                        PORTAL_INFORMATION_SALES_INLAND:
                            name: PORTAL_INFORMATION_SALES_INLAND
                            nice_name: ABUS Verkaufshinweise - Inland
                        PORTAL_INFORMATION_SALES_EXPORT:
                            name: PORTAL_INFORMATION_SALES_EXPORT
                            nice_name: ABUS Verkaufshinweise - Export
                service:
                    uniqueIdentifier: 4d432a594a58753e396f22724c
                    identifier: service
                    name: 'module/information_service'
                    link: 'https://portal.abus-kransysteme.de/apis/service'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    requestable: false
                    roles:
                        - ^ROLE_PORTAL_INFORMATION_SERVICE
                        - ^ROLE_PORTAL_GRUPPE_MONTAGEPARTNER$
                        - ^ROLE_PORTAL_GRUPPE_SERVICEPARTNER$
                        - ^ROLE_PORTAL_GRUPPE_SERVICEPARTNER_MASTER$
                        - ^ROLE_PORTAL_GRUPPE_ABNAHMEPARTNER$
                    rolesCanSeeRightSidebar:
                        - ^ROLE_PORTAL_INFORMATION_SERVICE
                        - ^ROLE_PORTAL_GRUPPE_MONTAGEPARTNER$
                        - ^ROLE_PORTAL_GRUPPE_SERVICEPARTNER$
                        - ^ROLE_PORTAL_GRUPPE_SERVICEPARTNER_MASTER$
                        - ^ROLE_PORTAL_GRUPPE_ABNAHMEPARTNER$
                mounting:
                    uniqueIdentifier: 3a677d5177272077572171252e
                    identifier: mounting
                    name: 'module/information_mounting'
                    link: 'https://portal.abus-kransysteme.de/apis/mounting'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    requestable: false
                    roles:
                        - ^ROLE_PORTAL_INFORMATION_MOUNTING
                        - ^ROLE_PORTAL_GRUPPE_MONTAGEPARTNER$
                        - ^ROLE_PORTAL_GRUPPE_SERVICEPARTNER$
                        - ^ROLE_PORTAL_GRUPPE_SERVICEPARTNER_MASTER$
                        - ^ROLE_PORTAL_GRUPPE_SERVICEPARTNER_BASIC$
                        - ^ROLE_PORTAL_GRUPPE_ABNAHMEPARTNER$
                        - ^ROLE_PORTAL_GRUPPE_TECHNISCHER_AUSSENDIENST$
                    rolesCanSeeRightSidebar:
                        - ^ROLE_PORTAL_INFORMATION_MOUNTING
                        - ^ROLE_PORTAL_GRUPPE_MONTAGEPARTNER$
                        - ^ROLE_PORTAL_GRUPPE_SERVICEPARTNER$
                        - ^ROLE_PORTAL_GRUPPE_SERVICEPARTNER_MASTER$
                        - ^ROLE_PORTAL_GRUPPE_SERVICEPARTNER_BASIC$
                        - ^ROLE_PORTAL_GRUPPE_ABNAHMEPARTNER$
                        - ^ROLE_PORTAL_GRUPPE_TECHNISCHER_AUSSENDIENST$

                technical:
                    uniqueIdentifier: 2a63544d673f38785f64473336
                    identifier: technical
                    name: 'module/information_technical'
                    link: 'https://portal.abus-kransysteme.de/apis/technical'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_PORTAL_INFORMATION_TECHNIK
                        - ^ROLE_PORTAL_GRUPPE_MONTAGEPARTNER$
                        - ^ROLE_PORTAL_GRUPPE_SERVICEPARTNER$
                        - ^ROLE_PORTAL_GRUPPE_SERVICEPARTNER_MASTER$
                        - ^ROLE_PORTAL_GRUPPE_SERVICEPARTNER_BASIC$
                        - ^ROLE_PORTAL_GRUPPE_ABNAHMEPARTNER$
                    rolesCanSeeRightSidebar:
                        - ^ROLE_PORTAL_INFORMATION_TECHNIK
                        - ^ROLE_PORTAL_GRUPPE_MONTAGEPARTNER$
                        - ^ROLE_PORTAL_GRUPPE_SERVICEPARTNER$
                        - ^ROLE_PORTAL_GRUPPE_SERVICEPARTNER_MASTER$
                        - ^ROLE_PORTAL_GRUPPE_SERVICEPARTNER_BASIC$
                        - ^ROLE_PORTAL_GRUPPE_ABNAHMEPARTNER$
                    requestable: false

        intranet:
            uniqueIdentifier: 796e2636686a28405023572555
            identifier: intranet
            name: 'module/intranet'
            icon: far fa-newspaper
            link: 'https://portal.abus-kransysteme.de/intranet'
            convertLinkToDevelopment: true
            openInNewTab: false
            roles:
                - ^ROLE_ABUS Intern$
            requestable: false
            maintenance: false

            order: 800

        website:
            uniqueIdentifier: 4b30276522523e6c42465f7148
            identifier: website
            name: 'module/website'
            icon: far fa-globe
            roles:
                - ^ROLE_ANY$
            requestable: false
            maintenance: false
            order: 900
            submodules:
                de_de:
                    uniqueIdentifier: 27726b5c2d3155395266252724
                    identifier: germany
                    name: 'module/website_germany'
                    link: 'https://portal.abus-kransysteme.de/website/de'
                    roles:
                        - ^ROLE_ANY$
                    convertLinkToDevelopment: false
                    openInNewTab: false
                    requestable: false
                de_at:
                    uniqueIdentifier: hk3q7fdw1ye3w8zywweO
                    identifier: austria
                    name: 'module/website_austria'
                    link: 'https://portal.abus-kransysteme.de/website/at'
                    roles:
                        - ^ROLE_ANY$
                    convertLinkToDevelopment: false
                    openInNewTab: false
                    requestable: false
                en_gb:
                    uniqueIdentifier: 4b654a793549634a22476e5756
                    identifier: england
                    name: 'module/website_england'
                    link: 'https://portal.abus-kransysteme.de/website/uk'
                    roles:
                        - ^ROLE_ANY$
                    convertLinkToDevelopment: false
                    openInNewTab: false
                    requestable: false
                es:
                    uniqueIdentifier: 292b617e5b5a294e6b3921295b
                    identifier: spain
                    name: 'module/website_spain'
                    link: 'https://portal.abus-kransysteme.de/website/es'
                    roles:
                        - ^ROLE_ANY$
                    convertLinkToDevelopment: false
                    openInNewTab: false
                    requestable: false
                pl:
                    uniqueIdentifier: 315b4c38244b5d4c7953703d41
                    identifier: poland
                    name: 'module/website_poland'
                    link: 'https://portal.abus-kransysteme.de/website/pl'
                    roles:
                        - ^ROLE_ANY$
                    convertLinkToDevelopment: false
                    openInNewTab: false
                    requestable: false
                se:
                    uniqueIdentifier: 2740557571382a442b4e48273e
                    identifier: sweden
                    name: 'module/website_sweden'
                    link: 'https://portal.abus-kransysteme.de/website/se'
                    roles:
                        - ^ROLE_ANY$
                    convertLinkToDevelopment: false
                    openInNewTab: false
                    requestable: false
                fr_fr:
                    uniqueIdentifier: 307c2e23683363556a7d4f7d66
                    identifier: france
                    name: 'module/website_france'
                    link: 'https://portal.abus-kransysteme.de/website/fr'
                    roles:
                        - ^ROLE_ANY$
                    convertLinkToDevelopment: false
                    openInNewTab: false
                    requestable: false
                nl:
                    uniqueIdentifier: 64622a717c7b462d2c434e7027
                    identifier: netherlands
                    name: 'module/website_netherlands'
                    link: 'https://portal.abus-kransysteme.de/website/nl'
                    roles:
                        - ^ROLE_ANY$
                    convertLinkToDevelopment: false
                    openInNewTab: false
                    requestable: false
                com:
                    uniqueIdentifier: 6a39635f7d65755576394e2d62
                    identifier: international
                    name: 'module/website_international'
                    link: 'https://portal.abus-kransysteme.de/website/com'
                    roles:
                        - ^ROLE_ANY$
                    convertLinkToDevelopment: false
                    openInNewTab: false
                    requestable: false
                pimcore:
                    uniqueIdentifier: 6a39635f7d65755576394e2d12
                    identifier: pimcore_admin
                    name: 'module/website_pimcore_admin'
                    link: 'https://www.abus-kransysteme.de/admin'
                    roles:
                        - ^ROLE_PIMCORE_
                    convertLinkToDevelopment: false
                    openInNewTab: true
                    requestable: false
        media:
            inDevelopment: false
            uniqueIdentifier: 5f66684a4b4e3c7a5067274a67
            identifier: media
            name: 'module/media'
            icon: far fa-images
            #            link: 'https://portal.abus-kransysteme.de/media'
            link: 'https://media.abus-kransysteme.de'
            convertLinkToDevelopment: true
            openInNewTab: true
            roles:
                - ^ROLE_PORTAL_DAM_
            requestable: true
            maintenance: false
            order: 1000
            descriptions:
                - de_DE: 'module/media/description/de'
                - en_GB: 'module/media/description/en'
                - fr_FR: 'module/media/description/fr'
                - es_ES: 'module/media/description/es'
            decisionMakerEmail: '<EMAIL>'
            ldapGroups:
#                PORTAL_DAM_Administrator:
#                    name: PORTAL_DAM_Administrator
#                    nice_name: ABUS Media - Administrator
                PORTAL_DAM_Benutzer_ABUS:
                    name: PORTAL_DAM_Benutzer_ABUS
                    nice_name: ABUS Media - Mitarbeiter
                PORTAL_DAM_Benutzer_Extern:
                    name: PORTAL_DAM_Benutzer_Extern
                    nice_name: ABUS Media - Extern
                PORTAL_DAM_Benutzer_Partner:
                    name: PORTAL_DAM_Benutzer_Partner
                    nice_name: ABUS Media - Partner
#                PORTAL_DAM_Super_Admin:
#                    name: PORTAL_DAM_Super_Admin
#                    nice_name: ABUS Media - Super Administrator
#                WEB_DAM_Benutzer_Mitarbeiter_mit_Upload:
#                    name: WEB_DAM_Benutzer_Mitarbeiter_mit_Upload
#                    nice_name: ABUS Media - Mitarbeiter

        crm:
            uniqueIdentifier: 27528c15e16e028191461eb683
            identifier: crm
            name: 'module/crm'
            icon: fas fa-poll-people
            link: 'https://crm.abus-kransysteme.de'
            convertLinkToDevelopment: false
            openInNewTab: true
            roles:
                - ^ROLE_PORTAL_CRM$
            requestable: false
            maintenance: false
            order: 1100

        oviss:
            uniqueIdentifier: 35697b2c4435304e5b274a3e2f
            identifier: oviss
            name: 'module/oviss'
            icon: fab fa-black-tie
            link: 'https://service.abus-kransysteme.de'
            convertLinkToDevelopment: false
            openInNewTab: true
            roles:
                - ^ROLE_PORTAL_GRUPPE_VERTRIEBSPARTNER_INLAND$
                - ^ROLE_PORTAL_OVISS_INTERN$
            requestable: false
            maintenance: false
            order: 1100

        oviss_over_ssl:
            uniqueIdentifier: 534962287733534c7a66705d2e
            identifier: oviss_over_ssl
            name: 'module/oviss_over_ssl'
            icon: fab fa-black-tie
            link: 'https://ssl.abus-kransysteme.de'
            convertLinkToDevelopment: false
            openInNewTab: true
            roles:
                - ^ROLE_PORTAL_OVISS_SSL$
            requestable: false
            maintenance: false
            order: 1101

        ilias:
            uniqueIdentifier: 998962287733534c7a66705d2e
            identifier: ilias
            name: 'module/ilias'
            icon: fas fa-graduation-cap
            link: 'https://academy.abus-kransysteme.de/login.php?lang=$language$'
            convertLinkToDevelopment: false
            openInNewTab: true
            roles:
                - ^ROLE_PORTAL_ACADEMY
                - ^ROLE_ABUS Intern$
            requestable: false
            maintenance: false
            order: 1200

        technical_data:
            uniqueIdentifier: 5e666d64295943792c5f4c6248
            identifier: tdata
            name: 'module/technical_data'
            icon: far fa-table
            link: 'https://portal.abus-kransysteme.de/tdata'
            convertLinkToDevelopment: true
            openInNewTab: false
            roles:
                # Die Rollen müssen hier einzelnd stehen bleiben und in der Berechtigung absteigend.
                # Hiermit wird die höchste Berechtigungsstufe ermittelt -> getHighestTdataPermissionGroup()
                - ^ROLE_PORTAL_TECHNICAL_DATA_ALL$
                - ^ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_ALL$
                - ^ROLE_PORTAL_TECHNICAL_DATA_LAUFKRAN$
                - ^ROLE_PORTAL_TECHNICAL_DATA_KIT$
                - ^ROLE_PORTAL_TECHNICAL_DATA_EXTERNAL_MOUNTING_PARTNER$
                - ^ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_EXT_DB_ALL$
                - ^ROLE_PORTAL_TECHNICAL_DATA_RESELLER$
                - ^ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_EXT_SK_LPK_KZUG_HB$
                - ^ROLE_PORTAL_TECHNICAL_DATA_EXTERNAL_PLANNING$
                - ^ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_EXT_DB_LK_SK_LPK_HB$
            requestable: true
            maintenance: false
            order: 1300
            decisionMakerEmail: <EMAIL>
            descriptions:
                - de_DE: 'module/technical_data/description/de'
                - en_GB: 'module/technical_data/description/en'
                - fr_FR: 'module/technical_data/description/fr'
                - es_ES: 'module/technical_data/description/es'
            ldapGroups:
                PORTAL_TECHNICAL_DATA_ABUKONFIS_ALL:
                    name: PORTAL_TECHNICAL_DATA_ABUKONFIS_ALL
                    nice_name: ABUS Technische Daten - ABUKonfis - Alle
                PORTAL_TECHNICAL_DATA_ABUKONFIS_EXT_SK_LPK_KZUG_HB:
                    name: PORTAL_TECHNICAL_DATA_ABUKONFIS_EXT_SK_LPK_KZUG_HB
                    nice_name: ABUS Technische Daten - ABUKonfis - EXT SK LPK KZUG HB
            subModules:
                eot_cranes:
                    uniqueIdentifier: 66553f5e5c3c45224f2666734c
                    identifier: eot_cranes
                    name: 'module/technical_data_eot_cranes'
                    link: 'https://portal.abus-kransysteme.de/technicaldata/eot_cranes'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_PORTAL_TECHNICAL_DATA_(ALL|LAUFKRAN|KIT|EXTERNAL_PLANNING|EXTERNAL_MOUNTING_PARTNER)$
                eot_cranes_abukonfis:
                    uniqueIdentifier: 21486d73445732407e28727951
                    identifier: eot_cranes_abukonfis
                    name: 'module/technical_data_eot_cranes_abukonfis'
                    link: 'https://portal.abus-kransysteme.de/technicaldata/eot_cranes_abukonfis'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_(ALL|EXT_DB_ALL|EXT_LK_SK_LPK_HB|EXT_DB_LK_SK_LPK_HB)$
                jib_cranes:
                    uniqueIdentifier: 2720437d5a565e712827534525
                    identifier: jib_cranes
                    name: 'module/technical_data_jib_cranes'
                    link: 'https://portal.abus-kransysteme.de/technicaldata/jib_cranes'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_PORTAL_TECHNICAL_DATA_(ALL|LAUFKRAN|KIT|RESELLER|EXTERNAL_PLANNING|EXTERNAL_MOUNTING_PARTNER)$
                jib_cranes_abukonfis:
                    uniqueIdentifier: 73314f6d675a3f7045502c4a56
                    identifier: jib_cranes_abukonfis
                    name: 'module/technical_data_jib_cranes_abukonfis'
                    link: 'https://portal.abus-kransysteme.de/technicaldata/jib_cranes_abukonfis'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_(ALL|EXT_DB_ALL|EXT_LK_SK_LPK_HB|EXT_DB_LK_SK_LPK_HB|EXT_SK_LPK_KZUG_HB)$
                mobile_gantry_cranes:
                    uniqueIdentifier: 492a7b2e676b345c7e4e7b3b7a
                    identifier: mobile_gantry_cranes
                    name: 'module/technical_data_mobile_gantry_cranes'
                    link: 'https://portal.abus-kransysteme.de/technicaldata/mobile_gantry_cranes'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_PORTAL_TECHNICAL_DATA_(ALL|LAUFKRAN|KIT|RESELLER|EXTERNAL_PLANNING|EXTERNAL_MOUNTING_PARTNER)$
                mobile_gantry_cranes_abukonfis:
                    uniqueIdentifier: 6135322225383c59452e735466
                    identifier: mobile_gantry_cranes_abukonfis
                    name: 'module/technical_data_mobile_gantry_cranes_abukonfis'
                    link: 'https://portal.abus-kransysteme.de/technicaldata/mobile_gantry_cranes_abukonfis'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_(ALL|EXT_DB_ALL|EXT_LK_SK_LPK_HB|EXT_DB_LK_SK_LPK_HB|EXT_SK_LPK_KZUG_HB)$
                wire_rope_hoists:
                    uniqueIdentifier: 675d425b2622334b2c7b7b222f
                    identifier: wire_rope_hoists
                    name: 'module/technical_data_wire_rope_hoists'
                    link: 'https://portal.abus-kransysteme.de/technicaldata/wire_rope_hoists'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_PORTAL_TECHNICAL_DATA_(ALL|LAUFKRAN|KIT|EXTERNAL_MOUNTING_PARTNER)$
                wire_rope_hoists_abukonfis:
                    uniqueIdentifier: 724328635a6e38424263206322
                    identifier: wire_rope_hoists_abukonfis
                    name: 'module/technical_data_wire_rope_hoists_abukonfis'
                    link: 'https://portal.abus-kransysteme.de/technicaldata/wire_rope_hoists_abukonfis'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_(ALL|EXT_DB_ALL|TEST_EKZ)$
                chain_hoists_with_trolley:
                    uniqueIdentifier: 30274d2621292d51566f3e5e28
                    identifier: chain_hoists_with_trolley
                    name: 'module/technical_data_chain_hoists_with_trolley'
                    link: 'https://portal.abus-kransysteme.de/technicaldata/chain_hoists_with_trolley'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_PORTAL_TECHNICAL_DATA_(ALL|LAUFKRAN|KIT|RESELLER||EXTERNAL_MOUNTING_PARTNER)$
                chain_hoists_with_trolley_abukonfis:
                    uniqueIdentifier: 323e395c322c476c644b463f43
                    identifier: chain_hoists_with_trolley_abukonfis
                    name: 'module/technical_data_chain_hoists_with_trolley_abukonfis'
                    link: 'https://portal.abus-kransysteme.de/technicaldata/chain_hoists_with_trolley_abukonfis'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_(ALL|EXT_DB_ALL|EXT_SK_LPK_KZUG_HB|TEST_EKZFW)$
                stationary_chain_hoists:
                    uniqueIdentifier: 6d5b53373751316f48367e7635
                    identifier: stationary_chain_hoists
                    name: 'module/technical_data_stationary_chain_hoists'
                    link: 'https://portal.abus-kransysteme.de/technicaldata/stationary_chain_hoists'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_PORTAL_TECHNICAL_DATA_(ALL|LAUFKRAN|KIT|RESELLER|EXTERNAL_MOUNTING_PARTNER)$
                stationary_chain_hoists_abukonfis:
                    uniqueIdentifier: 2f2737453d3e314f46324c3063
                    identifier: stationary_chain_hoists_abukonfis
                    name: 'module/technical_data_stationary_chain_hoists_abukonfis'
                    link: 'https://portal.abus-kransysteme.de/technicaldata/stationary_chain_hoists_abukonfis'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_(ALL|EXT_DB_ALL|EXT_SK_LPK_KZUG_HB|TEST_SZ)$
                crane_drives:
                    uniqueIdentifier: 5535734f6b483e454a294e273e
                    identifier: crane_drives
                    name: 'module/technical_data_crane_drives'
                    link: 'https://portal.abus-kransysteme.de/technicaldata/crane_drives'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_PORTAL_TECHNICAL_DATA_(ALL|LAUFKRAN|KIT|EXTERNAL_MOUNTING_PARTNER)$
                        - ^ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_(ALL|EXT_DB_ALL)$
                crane_drives_abukonfis:
                    uniqueIdentifier: 79613477744b25312b20523a36
                    identifier: crane_drives_abukonfis
                    name: 'module/technical_data_crane_drives_abukonfis'
                    link: 'https://portal.abus-kransysteme.de/technicaldata/crane_drives_abukonfis'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_TEST_FW$
                hb-systems:
                    uniqueIdentifier: 682839525a4e407561524c4373
                    identifier: hb-systems
                    name: 'module/technical_data_hb_systems'
                    link: 'https://portal.abus-kransysteme.de/technicaldata/hb-systems'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_PORTAL_TECHNICAL_DATA_(ALL|LAUFKRAN|KIT|RESELLER|EXTERNAL_PLANNING|EXTERNAL_MOUNTING_PARTNER)$
                        - ^ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_(ALL|EXT_DB_ALL|EXT_LK_SK_LPK_HB|EXT_DB_LK_SK_LPK_HB|EXT_SK_LPK_KZUG_HB)$
                hb-systems_abukonfis:
                    uniqueIdentifier: 48227a352f5e763f275b397521
                    identifier: hb-systems_abukonfis
                    name: 'module/technical_data_hb_systems_abukonfis'
                    link: 'https://portal.abus-kransysteme.de/technicaldata/hb-systems_abukonfis'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS_TEST_HB$

        print_shop:
            uniqueIdentifier: 3c49214823337c545f6c36225d
            identifier: print_shop
            name: 'module/print_shop'
            icon: fas fa-print
            link: 'https://me-portal.me-druckhaus.de/PrinectPortal/login2?locale=de'
            convertLinkToDevelopment: false
            openInNewTab: true
            roles:
                - ^ROLE_PORTAL_PRINT_SHOP$
            requestable: false
            maintenance: false
            order: 1350

        chat:
            uniqueIdentifier: 367c6e5b59725c292a6a264068
            identifier: chat
            name: 'module/chat'
            icon: fac fa-abus-chat
            link: 'https://portal.abus-kransysteme.de/chat'
            convertLinkToDevelopment: false
            openInNewTab: false
            roles:
                - ^ROLE_PORTAL_CHAT$
            requestable: false
            maintenance: false
            order: 1450

        gitlab:
            uniqueIdentifier: 7644317259203f335c47552257
            identifier: gitlab
            name: 'module/gitlab'
            icon: fab fa-gitlab
            link: 'https://portal.abus-kransysteme.de/gitlab'
            convertLinkToDevelopment: false
            openInNewTab: false
            roles:
                - ^ROLE_WEB_GITLAB$
            requestable: false
            maintenance: false
            order: 1500

        reporting:
            uniqueIdentifier: 34634249576e3f273b566d785f
            identifier: reporting
            name: 'module/reporting'
            icon: far fa-chart-bar
            link: 'https://portal.abus-kransysteme.de/management'
            roles:
                - ^ROLE_PORTAL_REPORTING_USERS$
                - ^ROLE_PORTAL_REPORTING_WEBSITE$
                - ^ROLE_PORTAL_MATOMO$
                - ^ROLE_PORTAL_GOOGLE$
            requestable: false
            maintenance: false
            order: 1600
            submodules:
                legacy:
                    uniqueIdentifier: 472b2230723b354749645e4e48
                    identifier: legacy
                    name: 'module/reporting_users'
                    link: 'https://portal.abus-kransysteme.de/reporting/users'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_PORTAL_REPORTING_USERS$
                    requestable: false
                matomo:
                    uniqueIdentifier: 425a3f663c62445d6e62276831
                    identifier: matomo
                    name: 'module/reporting_matomo'
                    link: 'https://portal.abus-kransysteme.de/matomo'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_PORTAL_MATOMO$
                    requestable: false
                analytics:
                    uniqueIdentifier: 3e6e24574746485b75635b2477
                    identifier: google_analytics
                    name: 'module/reporting_google_analytics'
                    link: 'https://analytics.google.com'
                    convertLinkToDevelopment: false
                    openInNewTab: true
                    roles:
                        - ^ROLE_PORTAL_GOOGLE$
                    requestable: false
                adwords:
                    uniqueIdentifier: 4b4324454b41514f233d65354b
                    identifier: google_adwords
                    name: 'module/reporting_adwords'
                    link: 'https://adwords.google.com'
                    convertLinkToDevelopment: false
                    openInNewTab: true
                    roles:
                        - ^ROLE_PORTAL_GOOGLE$
                    requestable: false
                found_problems:
                    uniqueIdentifier: 3e6e36707579733e68587d5953
                    identifier: found_problems
                    name: 'module/usermanagement_found_problems'
                    link: 'https://portal.abus-kransysteme.de/management/problems/found'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_PORTAL_USERMANAGEMENT_ADMIN$
                    requestable: false

        devops:
            uniqueIdentifier: 6f2164234c205a32302d5e243e
            identifier: devops
            name: 'module/devops'
            icon: fas fa-monitor-heart-rate
            roles:
                - ^ROLE_PORTAL_GRAYLOG_ADMIN$
                - ^ROLE_PORTAL_DEVOPS$
            requestable: false
            maintenance: false
            order: 1650
            submodules:
                logs:
                    uniqueIdentifier: 257436695d4062317a67302d5b
                    identifier: logs
                    name: 'module/devops_logs'
                    link: 'https://portal.abus-kransysteme.de/logs'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_PORTAL_GRAYLOG_ADMIN$
                        - ^ROLE_PORTAL_GRAYLOG_READER$
                    requestable: false

                grafana:
                    inDevelopment: false
                    uniqueIdentifier: 50293a4b557d6a6c4d48237544
                    identifier: grafana
                    name: 'module/devops_grafana'
                    link: 'https://portal.abus-kransysteme.de/grafana'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_PORTAL_GRAFANA_ADMIN$
                        - ^ROLE_PORTAL_GRAFANA_VIEWER$
                    requestable: false
                    maintenance: false

                sql:
                    uniqueIdentifier: 5331666d26715e31277675687d
                    identifier: sql
                    name: 'module/devops_sql'
                    link: 'https://portal.abus-kransysteme.de/sql'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_PORTAL_DEVOPS$
                    requestable: false


                traefik:
                    inDevelopment: false
                    uniqueIdentifier: 3c6b5363622239303d667d2c6b
                    identifier: traefik
                    name: 'module/devops_traefik'
                    link: 'https://portal.abus-kransysteme.de/traefik'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_PORTAL_DEVOPS$
                    requestable: false
                    maintenance: false

                kibana:
                    inDevelopment: false
                    uniqueIdentifier: 7625492b3f214d4d5666497a37
                    identifier: kibana
                    name: 'module/devops_kibana'
                    link: 'https://portal.abus-kransysteme.de/kibana'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_PORTAL_DEVOPS$
                    requestable: false
                    maintenance: false

        service:
            inDevelopment: false
            uniqueIdentifier: 363d784561695679634d246d5c
            identifier: service
            name: 'module/service'
            icon: far fa-fighter-jet
            link: 'https://portal.abus-kransysteme.de/service'
            roles:
                - ^ROLE_WEB_PORTAL_SERVICES$
            submodules:
                service_pwdGenerator:
                    uniqueIdentifier: 7e475d73712a22307a224b6f61
                    identifier: 'service_pwdGenerator'
                    name: 'module/service_password_generator'
                    link: 'https://portal.abus-kransysteme.de/services/pwdGenerator'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_WEB_PORTAL_SERVICES$
                    requestable: false
                service_mailGenerator:
                    uniqueIdentifier: 553e5a475b6347686366555e3c
                    identifier: 'service_mailGenerator'
                    name: 'module/service_mail_generator'
                    link: 'https://portal.abus-kransysteme.de/services/mailGenerator'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_WEB_PORTAL_SERVICES$
                    requestable: false
                service_activedirectory:
                    uniqueIdentifier: 3733302b6e20273f596c2f3c4c
                    identifier: 'service_activedirectory'
                    name: 'module/service_activedirectory'
                    link: 'https://portal.abus-kransysteme.de/services/activedirectory'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_WEB_PORTAL_SERVICES$
                    requestable: false
            requestable: false
            maintenance: false
            order: 1700

        bookingsTool:
            inDevelopment: false
            uniqueIdentifier: 363d784561695679634d246d5345678
            identifier: bookingsTool
            name: 'module/bookingsTool'
            icon: far fa-file-alt
            link: 'https://portal.abus-kransysteme.de/bookingsTool/bookings'
            convertLinkToDevelopment: true
            openInNewTab: false
            roles:
                - ^ROLE_PORTAL_BOOKING_
            rolesCanSeeRightSidebar:
                - ^ROLE_PORTAL_BOOKING_
            requestable: false
            maintenance: false
            order: 1700

        account:
            uniqueIdentifier: 303c757e47373148564d4b2873
            identifier: account
            name: 'module/access_management'
            icon: far fa-calculator
            roles:
                - ^ROLE_WEB_ACCESSMANAGEMENT
            submodules:
                account_list:
                    uniqueIdentifier: 50344a2732564e7577432e3767
                    identifier: 'account_list'
                    name: 'module/access_management_list_users'
                    link: 'https://portal.abus-kransysteme.de/accounts/list'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_WEB_ACCESSMANAGEMENT
                    requestable: false
                #                account_problems:
                #                    uniqueIdentifier: 372f715d342b2d6e5446453167
                #                    identifier: 'account_problems'
                #                    name: 'module/access_management_find_problems'
                #                    names:
                #                        en_gb: 'Find problems'
                #                        de_de: 'Probleme finden'
                #                    link: 'https://portal.abus-kransysteme.de/account/problems'
                #                    convertLinkToDevelopment: true
                #                    openInNewTab: false
                #                    roles:
                #                       - ^ROLE_WEB_ACCESSMANAGEMENT_SUPERADMIN$
                account_delete:
                    uniqueIdentifier: 29732b45735836586d546e7d40
                    identifier: 'account_delete'
                    name: 'module/access_management_account_delete'
                    link: 'https://portal.abus-kransysteme.de/accounts/delete'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_WEB_ACCESSMANAGEMENT
                    requestable: false
                account_workflow:
                    uniqueIdentifier: 623c78454921466b46637b7b3b
                    identifier: 'account_workflow'
                    name: 'module/access_management_account_workflow'
                    link: 'https://portal.abus-kransysteme.de/accounts/workflow'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_WEB_ACCESSMANAGEMENT_(ADMIN|SUPERADMIN)$
                    requestable: false
                account_create:
                    uniqueIdentifier: 395638497569405e3c664a6938
                    identifier: 'account_create'
                    name: 'module/access_management_account_create'
                    link: 'https://portal.abus-kransysteme.de/accounts/create'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_WEB_ACCESSMANAGEMENT
                    requestable: false
                #                account_reassign:
                #                    uniqueIdentifier: 63646840554e57216742627b49
                #                    identifier: 'account_reassign'
                #                    name: 'module/access_management_account_reassign'
                #                    link: 'https://portal.abus-kransysteme.de/accounts/reassign'
                #                    convertLinkToDevelopment: true
                #                    openInNewTab: false
                #                    roles:
                #                        - ^ROLE_WEB_ACCESSMANAGEMENT
                #                    requestable: false
                account_vpn_create:
                    uniqueIdentifier: c67bd0c3e560a5688c72702b376558c0
                    identifier: 'account_vpn_create'
                    name: 'module/access_management_account_vpn_create'
                    link: 'https://portal.abus-kransysteme.de/accounts/vpn/create'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_PORTAL_GRUPPE_VERTRIEBSPARTNER_INLAND
                        - ^ROLE_WEB_ACCESSMANAGEMENT_SUPERADMIN
                    requestable: false
                account_vpn_reassign:
                    uniqueIdentifier: 71811d70369dba9ab5abefd84a57a61c
                    identifier: 'account_vpn_reassign'
                    name: 'module/access_management_account_vpn_reassign'
                    link: 'https://portal.abus-kransysteme.de/accounts/vpn/reassign'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_PORTAL_GRUPPE_VERTRIEBSPARTNER_INLAND
                        - ^ROLE_WEB_ACCESSMANAGEMENT_SUPERADMIN
                    requestable: false
                #                account_request:
                #                    uniqueIdentifier: 5a384e5d71712c52534d5c5e52
                #                    identifier: 'account_request'
                #                    name: 'module/access_management_account_request'
                #                    link: 'https://portal.abus-kransysteme.de/account/request'
                #                    convertLinkToDevelopment: true
                #                    openInNewTab: false
                #                    roles:
                #                        - ^ROLE_WEB_ACCESSMANAGEMENT
                refresh_data:
                    uniqueIdentifier: 575f233a7a215038433c22202e
                    identifier: 'refresh_data'
                    name: 'module/refresh_data'
                    link: 'https://portal.abus-kransysteme.de/createLDAPuserTable'
                    convertLinkToDevelopment: true
                    openInNewTab: false
                    roles:
                        - ^ROLE_WEB_ACCESSMANAGEMENT_(ADMIN|SUPERADMIN)$
                    requestable: false

            requestable: false
            maintenance: false
            order: 1800

        furtherservices:
            inDevelopment: false
            uniqueIdentifier: 7e5e6f5669217d4c7844583e42
            identifier: furtherservices
            name: 'module/further_services'
            icon: far fa-comment-plus
            link: 'https://portal.abus-kransysteme.de/furtherservices'
            convertLinkToDevelopment: true
            openInNewTab: false
            roles:
                - ^ROLE_ABUS Intern$
                - ^ROLE_PORTAL_GRUPPE_VERTRIEBSPARTNER_INLAND$
                - ^ROLE_PORTAL_GRUPPE_EXPORTPARTNER$
                - ^ROLE_PORTAL_GRUPPE_TOECHTER$
            requestable: false
            maintenance: false
            order: 1900
