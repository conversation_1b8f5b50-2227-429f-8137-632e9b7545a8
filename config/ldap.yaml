parameters:
    ldap:
        host: ************
        port: 636
        ldap_username: <EMAIL>
        base_dn: OU=<PERSON><PERSON><PERSON>,OU=DMZ,DC=abus-vpn,DC=de
        read_user: CN=webserver_read,OU=<PERSON>utzer,OU=DMZ,DC=abus-vpn,DC=de
        write_dn: CN=webserver_write,OU=<PERSON>utzer,OU=DMZ,DC=abus-vpn,DC=de
        debug: false
        encryption: ssl
        version: 3
        key: sAMAccountName
        filer: (mail={username})
        dn_users: '%env(DN_USERS)%'
        dn_groups: '%env(DN_GROUPS)%'
        dn_companies: '%env(DN_COMPANIES)%'
        dn_user_create: OU=PORTAL_CREATED,OU=Benutzer,OU=DMZ,DC=abus-vpn,DC=de
        dn_user_disable: OU=PORTAL_DISABLED,OU=<PERSON><PERSON><PERSON>,OU=DMZ,DC=abus-vpn,DC=de

        read_password: '%env(LDAP_READ_PASSWORD)%'
        write_password: '%env(LDAP_WRITE_PASSWORD)%'
