{"name": "portal", "version": "3.0.0", "description": "ABUS Kransysteme GmbH Portal", "main": "/public/index.php", "engines": {"node": ">=22.0.0"}, "directories": {"doc": "doc"}, "repository": {"type": "git", "url": "ssh://******************************:2289/abus/portal2018.git"}, "author": "<PERSON>", "contributors": ["<PERSON><PERSON><PERSON>"], "dependencies": {"@ckeditor/ckeditor5-vue": "^7.3.0", "@fortawesome/fontawesome": "^1.1.8", "@fortawesome/fontawesome-svg-core": "^1.2.36", "@fortawesome/free-brands-svg-icons": "^5.15.4", "@fortawesome/pro-light-svg-icons": "^5.11.2", "@fortawesome/pro-regular-svg-icons": "^5.11.2", "@fortawesome/pro-solid-svg-icons": "^5.11.2", "@fortawesome/vue-fontawesome": "^3.1.2", "@vueuse/core": "^13.9.0", "axios": "^1.11.0", "base-64": "^0.1.0", "bootstrap": "^5.3.8", "bootstrap-vue-next": "^0.40.1", "ckeditor5": "^45.2.1", "dayjs": "^1.11.13", "detect-browser": "^5.3.0", "file-saver": "^2.0.5", "fine-uploader": "^5.16.2", "floating-vue": "^5.2.2", "font-awesome": "^4.7.0", "jquery": "^3.4.1", "jquery-slimscroll": "^1.3.8", "jquery-ui-dist": "^1.13.2", "lightbox2": "^2.11.3", "lodash": "^4.17.21", "pinia": "^3.0.3", "re-tree": "0.1.7", "svg-country-flags": "^1.2.6", "typeahead.js": "^0.11.1", "vue": "^3.5.21", "vue-i18n": "^11.1.12"}, "devDependencies": {"@eslint/js": "^9.30.0", "@rollup/plugin-inject": "^5.0.5", "@tsconfig/node22": "^22.0.2", "@types/js-yaml": "^4.0.9", "@types/lodash": "^4.14.184", "@types/node": "^24.3.1", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@vitejs/plugin-legacy": "^6.1.1", "@vitejs/plugin-vue": "^6.0.1", "@vue/tsconfig": "^0.8.1", "eslint": "^9.30.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-vue": "^10.2.0", "globals": "^16.2.0", "jiti": "^2.5.1", "prettier": "^3.6.2", "prettier-plugin-organize-attributes": "^1.0.0", "regenerator-runtime": "^0.13.9", "rollup-plugin-copy": "^3.5.0", "sass": "^1.92.1", "sass-embedded": "^1.89.2", "typescript": "^5.9.2", "typescript-eslint": "^8.35.0", "vite": "npm:rolldown-vite@latest", "vite-plugin-html": "^3.2.2", "vite-plugin-node-polyfills": "^0.23.0", "vite-plugin-pwa": "^1.0.0", "vite-plugin-static-copy": "^3.1.0", "vite-plugin-vue-devtools": "^8.0.1", "vue-tsc": "^3.0.6"}, "scripts": {"cache:clear": "sudo rm -rfdv ./var/cache/dev/*", "phpcs": "docker exec -it abus_portal2018_php-fpm php phpcs.phar --standard=psr12 --error-severity=1 --warning-severity=8 --extensions=php ./src", "phpcbf": "docker exec -it abus_portal2018_php-fpm php phpcbf.phar --standard=psr12 --error-severity=1 --warning-severity=8 --extensions=php ./src", "build": "vite build", "build:dev": "NODE_ENV=development vite build --mode development", "watch": "NODE_ENV=development vite build --mode development --watch", "lint": "eslint . --ext .vue,.ts,.js", "lint:fix": "eslint . --ext .vue,.ts,.js --fix", "format": "prettier --check .", "format:fix": "prettier --write ."}, "browserslist": ["> 1%, last 2 versions, not dead"]}